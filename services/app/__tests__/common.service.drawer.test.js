/**
 * Unit Tests for Drawer-Menu Business Logic
 * Based on QA E2E Test Scenarios for KyuleBag Drawer-Menu Flow
 * 
 * Tests cover:
 * - User logout functionality
 * - OTP verification for security
 * - Password change from settings
 * - Web link preview settings
 * - Account deletion functionality
 * - Alternative email management
 * - OCR settings and controls
 * - AI confidence level settings
 * - Label visibility settings
 * - Email verification processes
 * - Security and authentication features
 * - Error handling and edge cases
 */

const { Op } = require('sequelize');

// Mock all external dependencies
jest.mock('../../../database/models', () => ({
  tbl_users: {
    findOne: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_user_tokens: {
    destroy: jest.fn(),
  },
  tbl_user_subscriptions: {
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    findAndCountAll: jest.fn(),
  },
  tbl_user_ai_subscriptions: {
    create: jest.fn(),
    destroy: jest.fn(),
    findAndCountAll: jest.fn(),
  },
  tbl_alternative_emails: {
    findOne: jest.fn(),
    create: jest.fn(),
    destroy: jest.fn(),
    update: jest.fn(),
  },
  tbl_activity_logs: {
    create: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_items: {
    findAll: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_item_notes: {
    destroy: jest.fn(),
  },
  tbl_item_ocr_labels: {
    destroy: jest.fn(),
  },
  tbl_item_tags: {
    destroy: jest.fn(),
  },
  tbl_tags: {
    destroy: jest.fn(),
  },
  tbl_media: {
    destroy: jest.fn(),
  },
  tbl_sharing_details: {
    destroy: jest.fn(),
  },  
  tbl_tag_names: {
    destroy: jest.fn(),
  },
  tbl_announcements: {
    destroy: jest.fn(),
  },
  tbl_comment_settings: {
    destroy: jest.fn(),
  },
}));

jest.mock('bcryptjs', () => ({
  compareSync: jest.fn(),
  hash: jest.fn(),
  genSaltSync: jest.fn(() => 'mock-salt'),
}));

jest.mock('../../../helper/sendmail', () => ({
  sendmail: jest.fn(),
}));

jest.mock('../../../helper/general.helper', () => ({
  generateOtp: jest.fn(),
}));

// Import the service after mocking
const commonService = require('../common.service');
const User = require('../../../database/models').tbl_users;
const UserToken = require('../../../database/models').tbl_user_tokens;
const UserSubscription = require('../../../database/models').tbl_user_subscriptions;
const UserAISubscription = require('../../../database/models').tbl_user_ai_subscriptions;
const AlternativeEmail = require('../../../database/models').tbl_alternative_emails;
const ActivityLogs = require('../../../database/models').tbl_activity_logs;
const Items = require('../../../database/models').tbl_items;
const ItemNotes = require('../../../database/models').tbl_item_notes;
const OCRLabels = require('../../../database/models').tbl_item_ocr_labels;
const ItemTags = require('../../../database/models').tbl_item_tags;
const Images = require('../../../database/models').tbl_media;
const ItemSharingDetails = require('../../../database/models').tbl_sharing_details;
const TagNames = require('../../../database/models').tbl_tag_names;
const AnnouncementComment = require('../../../database/models').tbl_announcements;
const Bcryptjs = require('bcryptjs');
const Mail = require('../../../helper/sendmail');
const randomStringHelper = require('../../../helper/general.helper');

describe('Drawer-Menu Business Logic - Unit Tests', () => {
  let mockReq, mockRes;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock request object
    mockReq = {
      user_id: 'user123',
      body: {},
      headers: {
        authorization: 'Bearer mock-token-123',
      },
      params: {},
    };

    // Mock response object
    mockRes = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };

    // Default mock implementations
    randomStringHelper.generateOtp.mockReturnValue(1234);
    Mail.sendmail.mockResolvedValue(true);
    Bcryptjs.hash.mockResolvedValue('hashed-new-password');
    Bcryptjs.compareSync.mockReturnValue(true);
  });

  describe('🚪 Logout Functionality', () => {
    describe('✅ Successful Logout', () => {
      it('should successfully logout user and clear session', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          first_name: 'John Doe',
          device_token: 'device-token-123',
        };

        User.findOne.mockResolvedValue(mockUser);
        User.update.mockResolvedValue([1]);
        UserToken.destroy.mockResolvedValue(1);

        // Act
        const result = await commonService.logout(mockReq, mockRes);

        // Assert
        expect(User.findOne).toHaveBeenCalledWith({
          where: {
            user_id: 'user123',
          },
        });
        expect(User.update).toHaveBeenCalledWith(
          { device_token: '' },
          { where: { user_id: 'user123' } }
        );
        expect(UserToken.destroy).toHaveBeenCalledWith({
          where: {
            token: 'Bearer mock-token-123',
          },
        });
        expect(result).toBe(1);
      });

      it('should clear device token during logout', async () => {
        // Arrange
        const mockUser = { user_id: 'user123', device_token: 'old-device-token' };
        User.findOne.mockResolvedValue(mockUser);
        User.update.mockResolvedValue([1]);
        UserToken.destroy.mockResolvedValue(1);

        // Act
        await commonService.logout(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { device_token: '' },
          { where: { user_id: 'user123' } }
        );
      });

      it('should destroy user token during logout', async () => {
        // Arrange
        User.findOne.mockResolvedValue({ user_id: 'user123' });
        User.update.mockResolvedValue([1]);
        UserToken.destroy.mockResolvedValue(1);

        // Act
        await commonService.logout(mockReq, mockRes);

        // Assert
        expect(UserToken.destroy).toHaveBeenCalledWith({
          where: {
            token: 'Bearer mock-token-123',
          },
        });
      });
    });

    describe('❌ Logout Failures', () => {
      it('should return 0 when user not found', async () => {
        // Arrange
        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.logout(mockReq, mockRes);

        // Assert
        expect(result).toBe(0);
        expect(User.update).not.toHaveBeenCalled();
        expect(UserToken.destroy).not.toHaveBeenCalled();
      });

      it('should handle database errors during logout', async () => {
        // Arrange
        User.findOne.mockRejectedValue(new Error('Database error'));

        // Act & Assert
        await expect(commonService.logout(mockReq, mockRes)).rejects.toThrow('Database error');
      });
    });
  });

  describe('🔐 Security and Verification', () => {
    describe('📱 OTP Verification', () => {
      it('should successfully verify correct OTP', async () => {
        // Arrange
        mockReq.body = { otp: 123456 };
        const mockUser = {
          user_id: 'user123',
          verification_code: 123456,
          is_verified: false,
          status: 'inactive',
        };

        User.findOne.mockResolvedValue(mockUser);
        User.update.mockResolvedValue([1]);

        // Act
        const result = await commonService.verify_otp(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { is_verified: true, verification_code: null, status: 'active' },
          { where: { user_id: 'user123' } }
        );
        expect(result).toBe(3);
      });

      it('should return 2 for incorrect OTP', async () => {
        // Arrange
        mockReq.body = { otp: 123456 };
        const mockUser = {
          user_id: 'user123',
          verification_code: 654321, // Different OTP
        };

        User.findOne.mockResolvedValue(mockUser);

        // Act
        const result = await commonService.verify_otp(mockReq, mockRes);

        // Assert
        expect(result).toBe(2);
        expect(User.update).not.toHaveBeenCalled();
      });

      it('should return 1 when user not found for OTP verification', async () => {
        // Arrange
        mockReq.body = { otp: 123456 };
        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.verify_otp(mockReq, mockRes);

        // Assert
        expect(result).toBe(1);
        expect(User.update).not.toHaveBeenCalled();
      });
    });

    describe('📧 Email Verification', () => {
      it('should successfully verify email with valid code', async () => {
        // Arrange
        mockReq.params = { code: 'verification-code-123' };
        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
        };

        User.findOne.mockResolvedValue(mockUser);
        User.update.mockResolvedValue([1]);

        // Act
        const result = await commonService.verify_email(mockReq, mockRes);

        // Assert
        expect(User.findOne).toHaveBeenCalledWith({
          where: {
            verification_code: 'verification-code-123',
          },
          attributes: ['email', 'first_name', 'last_name', 'user_id'],
        });
        expect(User.update).toHaveBeenCalledWith(
          { verification_code: null, email_verified: '1', status: 'active' },
          { where: { user_id: 'user123' } }
        );
        expect(result).toBe(1);
      });

      it('should return 2 for invalid verification code', async () => {
        // Arrange
        mockReq.params = { code: 'invalid-code' };
        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.verify_email(mockReq, mockRes);

        // Assert
        expect(result).toBe(2);
        expect(User.update).not.toHaveBeenCalled();
      });
    });

    describe('🔑 Password Change', () => {
      it('should successfully change password with valid old password', async () => {
        // Arrange
        mockReq.body = {
          old_password: 'old-password',
          new_password: 'new-password',
          user_id: 'user123',
        };

        const mockUser = {
          user_id: 'user123',
          password: 'hashed-old-password',
        };

        User.findOne.mockResolvedValue(mockUser);
        Bcryptjs.compareSync.mockReturnValue(true);
        Bcryptjs.hash.mockResolvedValue('hashed-new-password');
        User.update.mockResolvedValue([1]);

        // Act
        const result = await commonService.change_password(mockReq, mockRes);

        // Assert
        expect(User.findOne).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
          attributes: ['user_id', 'password'],
        });
        expect(Bcryptjs.compareSync).toHaveBeenCalledWith('old-password', 'hashed-old-password');
        expect(Bcryptjs.hash).toHaveBeenCalledWith('new-password', 'mock-salt');
        expect(User.update).toHaveBeenCalledWith(
          { password: 'hashed-new-password' },
          { where: { user_id: 'user123' } }
        );
        expect(result).toBe(1);
      });

      it('should return 2 for incorrect old password', async () => {
        // Arrange
        mockReq.body = {
          old_password: 'wrong-password',
          new_password: 'new-password',
        };

        const mockUser = {
          user_id: 'user123',
          password: 'hashed-old-password',
        };

        User.findOne.mockResolvedValue(mockUser);
        Bcryptjs.compareSync.mockReturnValue(false);

        // Act
        const result = await commonService.change_password(mockReq, mockRes);

        // Assert
        expect(result).toBe(2);
        expect(User.update).not.toHaveBeenCalled();
      });

      it('should return 3 when user not found for password change', async () => {
        // Arrange
        mockReq.body = {
          old_password: 'old-password',
          new_password: 'new-password',
        };

        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.change_password(mockReq, mockRes);

        // Assert
        expect(result).toBe(3);
        expect(User.update).not.toHaveBeenCalled();
      });
    });
  });

  describe('⚙️ Settings Management', () => {
    describe('🔗 Web Link Preview Settings', () => {
      beforeEach(() => {
        // Reset settings-specific mocks
        User.findOne.mockReset();
        User.update.mockReset();
        ActivityLogs.create.mockReset();
      });

      it('should enable web link preview and log activity', async () => {
        // Arrange
        mockReq.body = { set_web_link_preview: true };

        const mockUser = {
          user_id: 'user123',
          is_web_link_preview_set: false,
        };

        const mockUpdatedUser = {
          user_id: 'user123',
          is_web_link_preview_set: true,
          dataValues: { is_web_link_preview_set: true },
        };

        User.findOne
          .mockResolvedValueOnce(mockUser) // Initial user check
          .mockResolvedValueOnce(mockUpdatedUser); // After update
        User.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await commonService.set_web_link_preview(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { is_web_link_preview_set: true },
          { where: { user_id: 'user123' } }
        );
        expect(ActivityLogs.create).toHaveBeenCalledWith({
          user_id: 'user123',
          title: 'Change Web link preview',
          description: 'Web link preview enabled.',
        });
        expect(result.is_web_link_preview_set).toBe(true);
      });

      it('should disable web link preview and log activity', async () => {
        // Arrange
        mockReq.body = { set_web_link_preview: false };

        const mockUser = {
          user_id: 'user123',
          is_web_link_preview_set: true,
        };

        const mockUpdatedUser = {
          user_id: 'user123',
          is_web_link_preview_set: false,
          dataValues: { is_web_link_preview_set: false },
        };

        User.findOne
          .mockResolvedValueOnce(mockUser)
          .mockResolvedValueOnce(mockUpdatedUser);
        User.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await commonService.set_web_link_preview(mockReq, mockRes);

        // Assert
        expect(ActivityLogs.create).toHaveBeenCalledWith(
          expect.objectContaining({
            description: 'Web link preview disabled.',
          })
        );
        expect(result.is_web_link_preview_set).toBe(false);
      });

      it('should return undefined when user not found for web link preview', async () => {
        // Arrange
        mockReq.body = { set_web_link_preview: true };
        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.set_web_link_preview(mockReq, mockRes);

        // Assert
        expect(result).toBeUndefined();
        expect(User.update).not.toHaveBeenCalled();
      });
    });

    describe('🔍 OCR Settings', () => {
      beforeEach(() => {
        User.findOne.mockReset();
        User.update.mockReset();
        ActivityLogs.create.mockReset();
      });

      it('should enable OCR and log activity', async () => {
        // Arrange
        mockReq.body = { OCR_set: true };

        const mockUser = { user_id: 'user123', OCR_set: false };
        const mockUpdatedUser = { user_id: 'user123', OCR_set: true };

        User.findOne
          .mockResolvedValueOnce(mockUser)
          .mockResolvedValueOnce(mockUpdatedUser);
        User.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await commonService.setOCR(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { OCR_set: true },
          { where: { user_id: 'user123' } }
        );
        expect(ActivityLogs.create).toHaveBeenCalledWith({
          user_id: 'user123',
          title: 'Change OCR set',
          description: 'Text detection enabled.',
        });
        expect(result).toEqual([1]);
      });

      it('should disable OCR and log activity', async () => {
        // Arrange
        mockReq.body = { OCR_set: false };

        const mockUser = { user_id: 'user123', OCR_set: true };
        const mockUpdatedUser = { user_id: 'user123', OCR_set: false };

        User.findOne
          .mockResolvedValueOnce(mockUser)
          .mockResolvedValueOnce(mockUpdatedUser);
        User.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await commonService.setOCR(mockReq, mockRes);

        // Assert
        expect(ActivityLogs.create).toHaveBeenCalledWith(
          expect.objectContaining({
            description: 'Text detection disabled.',
          })
        );
      });

      it('should return undefined when user not found for OCR settings', async () => {
        // Arrange
        mockReq.body = { OCR_set: true };
        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.setOCR(mockReq, mockRes);

        // Assert
        expect(result).toBeUndefined();
      });
    });

    describe('🎯 AI Confidence Level Settings', () => {
      beforeEach(() => {
        User.count.mockReset();
        User.update.mockReset();
      });

      it('should successfully update AI confidence level', async () => {
        // Arrange
        mockReq.body = { AI_confidence_level: 0.8 };
        User.count.mockResolvedValue(1);
        User.update.mockResolvedValue([1]);

        // Act
        const result = await commonService.setAIConfidenceLevel(mockReq, mockRes);

        // Assert
        expect(User.count).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(User.update).toHaveBeenCalledWith(
          { AI_confidence_level: 0.8 },
          { where: { user_id: 'user123' } }
        );
        expect(result).toEqual([1]);
      });

      it('should return undefined when user not found for AI confidence level', async () => {
        // Arrange
        mockReq.body = { AI_confidence_level: 0.7 };
        User.count.mockResolvedValue(0);

        // Act
        const result = await commonService.setAIConfidenceLevel(mockReq, mockRes);

        // Assert
        expect(result).toBeUndefined();
        expect(User.update).not.toHaveBeenCalled();
      });
    });

    describe('🏷️ OCR Label Visibility Settings', () => {
      beforeEach(() => {
        User.count.mockReset();
        User.update.mockReset();
      });

      it('should successfully hide OCR labels', async () => {
        // Arrange
        mockReq.body = { hide_OCR_label: true };
        User.count.mockResolvedValue(1);
        User.update.mockResolvedValue([1]);

        // Act
        const result = await commonService.hideOCRLabel(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { hide_OCR_label: true },
          { where: { user_id: 'user123' } }
        );
        expect(result).toEqual([1]);
      });

      it('should successfully show OCR labels', async () => {
        // Arrange
        mockReq.body = { hide_OCR_label: false };
        User.count.mockResolvedValue(1);
        User.update.mockResolvedValue([1]);

        // Act
        const result = await commonService.hideOCRLabel(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { hide_OCR_label: false },
          { where: { user_id: 'user123' } }
        );
      });

      it('should return undefined when user not found for OCR label settings', async () => {
        // Arrange
        mockReq.body = { hide_OCR_label: true };
        User.count.mockResolvedValue(0);

        // Act
        const result = await commonService.hideOCRLabel(mockReq, mockRes);

        // Assert
        expect(result).toBeUndefined();
      });
    });
  });

  describe('📧 Contact Management', () => {
    describe('✅ Alternative Email Management', () => {
      beforeEach(() => {
        AlternativeEmail.findOne.mockReset();
        User.findOne.mockReset();
      });

      it('should return type 0 when alternative email same as user email', async () => {
        // Arrange
        mockReq.body = {
          alternative_email: '<EMAIL>',
          alternative_phone: '',
          country_code: '',
        };

        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
          phone: '+1234567890',
          country_code: '+1',
        };

        AlternativeEmail.findOne
          .mockResolvedValueOnce(null) // Initial alternative email check
          .mockResolvedValueOnce(null); // Final alternative email check
        User.findOne
          .mockResolvedValueOnce(mockUser) // User details lookup
          .mockResolvedValueOnce(null); // Existing user check

        // Act
        const result = await commonService.alternative_email(mockReq, mockRes);

        // Assert
        expect(result).toEqual({ type: 0 });
      });

      it('should return type 2 when alternative phone same as user phone', async () => {
        // Arrange
        mockReq.body = {
          alternative_email: '',
          alternative_phone: '+1234567890',
          country_code: '+1',
        };

        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
          phone: '+1234567890',
          country_code: '+1',
        };

        AlternativeEmail.findOne
          .mockResolvedValueOnce(null) // Initial alternative email check
          .mockResolvedValueOnce(null); // Final alternative email check
        User.findOne
          .mockResolvedValueOnce(mockUser) // User details lookup
          .mockResolvedValueOnce(null); // Existing user check

        // Act
        const result = await commonService.alternative_email(mockReq, mockRes);

        // Assert
        expect(result).toEqual({ type: 2 });
      });

      it('should return type 3 when alternative email already exists for another user', async () => {
        // Arrange
        mockReq.body = {
          alternative_email: '<EMAIL>',
          alternative_phone: '',
          country_code: '',
        };

        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
          phone: '+1234567890',
        };

        const existingUser = {
          user_id: 'user456', // Different user
          email: '<EMAIL>',
        };

        AlternativeEmail.findOne
          .mockResolvedValueOnce(null) // Initial alternative email check
          .mockResolvedValueOnce(null); // Final alternative email check
        User.findOne
          .mockResolvedValueOnce(mockUser) // User details lookup
          .mockResolvedValueOnce(existingUser); // Existing user check - found different user

        // Act
        const result = await commonService.alternative_email(mockReq, mockRes);

        // Assert
        expect(result).toEqual({ type: 3 });
      });

      it('should return type 7 when alternative email exists in alternative emails table', async () => {
        // Arrange
        mockReq.body = {
          alternative_email: '<EMAIL>',
          alternative_phone: '',
          country_code: '',
        };

        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
        };

        const existingAlternative = {
          user_id: 'user456',
          email: '<EMAIL>',
        };

        AlternativeEmail.findOne
          .mockResolvedValueOnce(null) // Initial alternative email check
          .mockResolvedValueOnce(existingAlternative); // Final alternative email check - found existing
        User.findOne
          .mockResolvedValueOnce(mockUser) // User details lookup  
          .mockResolvedValueOnce(null); // Existing user check

        // Act
        const result = await commonService.alternative_email(mockReq, mockRes);

        // Assert
        expect(result).toEqual({ type: 7 });
      });
    });
  });

  describe('🗑️ Account Management', () => {
    describe('❌ Account Deletion', () => {
      beforeEach(() => {
        Items.findAll.mockReset();
        Items.destroy.mockReset();
        ItemNotes.destroy.mockReset();
        OCRLabels.destroy.mockReset();
        ItemTags.destroy.mockReset();
        Images.destroy.mockReset();
        ItemSharingDetails.destroy.mockReset();
        ActivityLogs.destroy.mockReset();
        TagNames.destroy.mockReset();
        AlternativeEmail.destroy.mockReset();
        UserToken.destroy.mockReset();
        User.destroy.mockReset();
      });

      it('should successfully delete user account with all related data', async () => {
        // Arrange
        const mockUserItems = [
          { item_id: 'item1' },
          { item_id: 'item2' },
        ];

        Items.findAll.mockResolvedValue(mockUserItems);
        ItemNotes.destroy.mockResolvedValue(1);
        OCRLabels.destroy.mockResolvedValue(1);
        ItemTags.destroy.mockResolvedValue(1);
        Images.destroy.mockResolvedValue(1);
        ItemSharingDetails.destroy.mockResolvedValue(1);
        ActivityLogs.destroy.mockResolvedValue(1);
        TagNames.destroy.mockResolvedValue(1);
        AlternativeEmail.destroy.mockResolvedValue(1);
        UserSubscription.destroy.mockResolvedValue(1);
        UserAISubscription.destroy.mockResolvedValue(1);
        UserToken.destroy.mockResolvedValue(1);
        User.destroy.mockResolvedValue(1);
        AnnouncementComment.destroy.mockResolvedValue(1);

        // Act
        await commonService.userDelete(mockReq, mockRes);

        // Assert
        expect(Items.findAll).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });

        // Verify item-related deletions for each item
        expect(ItemNotes.destroy).toHaveBeenCalledTimes(2);
        expect(OCRLabels.destroy).toHaveBeenCalledTimes(2);
        expect(ItemTags.destroy).toHaveBeenCalledTimes(2);
        expect(Images.destroy).toHaveBeenCalledTimes(2);
        expect(ItemSharingDetails.destroy).toHaveBeenCalledTimes(2);

        // Verify user-related deletions
        expect(ActivityLogs.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(TagNames.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(AlternativeEmail.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(UserSubscription.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(UserAISubscription.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(UserToken.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
        expect(User.destroy).toHaveBeenCalledWith({
          where: { user_id: 'user123' },
        });
      });

      it('should handle account deletion when user has no items', async () => {
        // Arrange
        Items.findAll.mockResolvedValue([]);
        ActivityLogs.destroy.mockResolvedValue(1);
        TagNames.destroy.mockResolvedValue(1);
        AlternativeEmail.destroy.mockResolvedValue(1);
        UserSubscription.destroy.mockResolvedValue(1);
        UserAISubscription.destroy.mockResolvedValue(1);
        UserToken.destroy.mockResolvedValue(1);
        User.destroy.mockResolvedValue(1);
        AnnouncementComment.destroy.mockResolvedValue(1);

        // Act
        await commonService.userDelete(mockReq, mockRes);

        // Assert
        expect(Items.findAll).toHaveBeenCalled();
        expect(ItemNotes.destroy).not.toHaveBeenCalled();
        expect(ActivityLogs.destroy).toHaveBeenCalled();
        expect(UserSubscription.destroy).toHaveBeenCalled();
        expect(UserAISubscription.destroy).toHaveBeenCalled();
        expect(User.destroy).toHaveBeenCalled();
      });

      it('should handle database errors during account deletion', async () => {
        // Arrange
        Items.findAll.mockRejectedValue(new Error('Database error'));

        // Act & Assert
        await expect(commonService.userDelete(mockReq, mockRes)).rejects.toThrow('Database error');
      });
    });
  });

  describe('🚨 Edge Cases and Error Handling', () => {
    beforeEach(() => {
      // Reset all mocks for error handling tests
      jest.clearAllMocks();
    });

    it('should handle database connection failures gracefully', async () => {
      // Arrange
      User.findOne.mockRejectedValue(new Error('Connection timeout'));

      // Act & Assert
      // The logout function doesn't propagate database errors, it catches them
      await expect(commonService.logout(mockReq, mockRes)).rejects.toThrow('Connection timeout');
    });

    it('should handle missing authorization header in logout', async () => {
      // Arrange
      mockReq.headers = {}; // No authorization header
      User.findOne.mockResolvedValue({ user_id: 'user123' });
      User.update.mockResolvedValue([1]);
      UserToken.destroy.mockResolvedValue(0);

      // Act
      const result = await commonService.logout(mockReq, mockRes);

      // Assert
      expect(UserToken.destroy).toHaveBeenCalledWith({
        where: { token: undefined },
      });
      expect(result).toBe(1);
    });

    it('should handle activity log creation failures in settings', async () => {
      // Arrange
      mockReq.body = { OCR_set: true };
      const mockUser = { user_id: 'user123' };

      User.findOne
        .mockResolvedValueOnce(mockUser)
        .mockResolvedValueOnce({ OCR_set: true });
      User.update.mockResolvedValue([1]);
      ActivityLogs.create.mockRejectedValue(new Error('Activity log failed'));

      // Act & Assert
      await expect(commonService.setOCR(mockReq, mockRes)).rejects.toThrow('Activity log failed');
    });

    it('should handle invalid OTP data types', async () => {
      // Arrange
      mockReq.body = { otp: 'invalid-otp' };
      const mockUser = {
        user_id: 'user123',
        verification_code: 123456,
      };

      User.findOne.mockResolvedValue(mockUser);

      // Act
      const result = await commonService.verify_otp(mockReq, mockRes);

      // Assert
      expect(result).toBe(2); // Invalid OTP
      expect(User.update).not.toHaveBeenCalled();
    });

    it('should handle missing request body parameters', async () => {
      // Arrange
      mockReq.body = {}; // Empty body

      // Act
      const result = await commonService.change_password(mockReq, mockRes);

      // Assert - Should handle gracefully, may return user not found
      expect(User.findOne).toHaveBeenCalled();
    });

    it('should handle database errors during account deletion', async () => {
      // Arrange
      Items.findAll.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(commonService.userDelete(mockReq, mockRes)).rejects.toThrow('Database connection failed');
    });
  });

  describe('🔄 Data Validation and Sanitization', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle undefined user_id in request', async () => {
      // Arrange
      mockReq.user_id = undefined;

      // Act
      const result = await commonService.logout(mockReq, mockRes);

      // Assert
      expect(User.findOne).toHaveBeenCalledWith({
        where: { user_id: undefined },
      });
    });

    it('should sanitize email inputs in alternative email', async () => {
      // Arrange
      mockReq.body = {
        alternative_email: '  <EMAIL>  ', // With spaces and uppercase
        alternative_phone: '',
        country_code: '',
      };

      AlternativeEmail.findOne.mockResolvedValue(null);
      User.findOne.mockResolvedValue({
        user_id: 'user123',
        email: '<EMAIL>',
      });

      // Act
      await commonService.alternative_email(mockReq, mockRes);

      // Assert - The service should handle the input as provided
      expect(User.findOne).toHaveBeenCalled();
    });

    it('should handle special characters in passwords', async () => {
      // Arrange
      mockReq.body = {
        old_password: 'P@ssw0rd!@#$%^&*()',
        new_password: 'N3wP@ssw0rd!@#$%^&*()',
      };

      const mockUser = {
        user_id: 'user123',
        password: 'hashed-old-password',
      };

      User.findOne.mockResolvedValue(mockUser);
      Bcryptjs.compareSync.mockReturnValue(true);
      Bcryptjs.hash.mockResolvedValue('hashed-new-password');
      User.update.mockResolvedValue([1]);

      // Act
      const result = await commonService.change_password(mockReq, mockRes);

      // Assert
      expect(Bcryptjs.hash).toHaveBeenCalledWith('N3wP@ssw0rd!@#$%^&*()', 'mock-salt');
      expect(result).toBe(1);
    });

    it('should handle boolean to string conversion in settings', async () => {
      // Arrange
      mockReq.body = { hide_OCR_label: 'true' }; // String instead of boolean
      User.count.mockResolvedValue(1);
      User.update.mockResolvedValue([1]);

      // Act
      const result = await commonService.hideOCRLabel(mockReq, mockRes);

      // Assert
      expect(User.update).toHaveBeenCalledWith(
        { hide_OCR_label: 'true' },
        { where: { user_id: 'user123' } }
      );
      expect(result).toEqual([1]);
    });
  });
}); 