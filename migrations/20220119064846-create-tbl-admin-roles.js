'use strict';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tbl_admin_roles', {
      admin_role_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      role_id: {
        type: Sequelize.INTEGER,
        references: {
          model: "tbl_roles",
          key:"role_id",
        }
      },
      admin_id: {
        type: Sequelize.INTEGER,
        references: {
          model: "tbl_admins",
          key:"admin_id",
        }
      },
      view: {
        allowNull: false,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      edit: {
        allowNull: false,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      add: {
        allowNull: false,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      delete: {
        allowNull: false,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tbl_admin_roles');
  }
};