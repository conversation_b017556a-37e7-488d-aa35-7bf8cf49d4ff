const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const notification_service = require("../../services/cms/notification.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async addNotification(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let add_notification_res = await notification_service.addNotification(
        req,
        res
      );
      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: add_notification_res,
        message: message.SENDNOTIFICATION,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("addNotification -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async listNotificaton(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_notification_res = await notification_service.listNotificaton(
        req,
        res
      );
      if (list_notification_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_notification_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NOTIFICATIONLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listStaticPage -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async resendNotification(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let notification_res = await notification_service.resendNotification(
        req,
        res
      );
      if (!notification_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: notification_res,
          message: message.NOTIFICATIONLISTNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: notification_res,
          message: message.NOTIFICATIONLIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("retrieveNotification -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
