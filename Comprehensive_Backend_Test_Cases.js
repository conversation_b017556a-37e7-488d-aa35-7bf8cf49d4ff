/**
 * Comprehensive Backend Test Case Generator for KyuleBag API
 * 
 * This script generates extensive test case documentation covering:
 * - All API endpoints in /app-api/ and /cms-api/ routes
 * - Every controller method with positive and negative test cases
 * - All service layer methods with database operations
 * - Complete validation rule coverage from assets/validators.js
 * - Middleware testing including authentication and file upload
 * - Integration tests for external services (GCP, Email, AI/OCR)
 * - Performance and security test scenarios
 * 
 * Target: 15-25 test cases per major module for enterprise-level coverage
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

const XLSX = require('xlsx');

// Test Case Categories
const TEST_CATEGORIES = {
  UNIT: 'Unit Test',
  INTEGRATION: 'Integration Test',
  SCENARIO: 'Scenario-Based Test',
  EDGE_CASE: 'Edge Case Test',
  SECURITY: 'Security Test',
  PERFORMANCE: 'Performance Test',
  VALIDATION: 'Input Validation Test',
  CONTROLLER: 'Controller Test',
  SERVICE: 'Service Layer Test',
  MIDDLEWARE: 'Middleware Test'
};

// Test Priority Levels
const PRIORITY = {
  CRITICAL: 'Critical',
  HIGH: 'High',
  MEDIUM: 'Medium',
  LOW: 'Low'
};

// Test Status
const STATUS = {
  NOT_STARTED: 'Not Started',
  IN_PROGRESS: 'In Progress',
  COMPLETED: 'Completed',
  BLOCKED: 'Blocked'
};

// Module Categories
const MODULES = {
  AUTHENTICATION: 'Authentication & Authorization',
  USER_MANAGEMENT: 'User Management',
  ITEM_MANAGEMENT: 'Item Management',
  TAG_MANAGEMENT: 'Tag Management',
  NOTE_MANAGEMENT: 'Note Management',
  TRASH_MANAGEMENT: 'Trash Management',
  ACTIVITY_LOGS: 'Activity Logs',
  FILE_UPLOAD: 'File Upload & Media',
  ADMIN_PANEL: 'Admin Panel',
  SUBSCRIPTION: 'Subscription Management',
  DASHBOARD: 'Dashboard & Analytics',
  STATIC_CONTENT: 'Static Content Management',
  NOTIFICATIONS: 'Email Notifications',
  ANNOUNCEMENTS: 'Announcements',
  COMMENTS: 'Comments Management',
  PAYMENT_TRANSACTIONS: 'Payment Transactions',
  AI_SUBSCRIPTION: 'AI Subscription',
  WEBHOOKS: 'Webhooks',
  CONTACT_US: 'Contact Us',
  SECURITY_VALIDATION: 'Security & Validation',
  MIDDLEWARE_TESTING: 'Middleware Testing'
};

// Excel Headers
const HEADERS = [
  'Test Case ID',
  'Test Scenario',
  'Module/Feature',
  'Test Category',
  'Priority',
  'Test Description',
  'Pre-conditions',
  'Test Steps',
  'Input Data',
  'Expected Response',
  'Post-conditions',
  'Dependencies',
  'Tags',
  'Request-Response Lifecycle Stage',
  'HTTP Method',
  'API Endpoint',
  'Authentication Required',
  'Validation Rules',
  'Business Logic',
  'Database Operations',
  'External Services',
  'Error Handling',
  'Controller Method',
  'Service Method',
  'Middleware Chain',
  'Status',
  'Notes'
];

/**
 * Generate Comprehensive Authentication & Authorization Test Cases (25 tests)
 */
function generateAuthenticationTests() {
  const tests = [];
  let testId = 1;

  // User Registration Tests (8 tests)
  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - KyuleBag Native Account',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify complete user registration flow for KyuleBag native account with email and password',
    'Pre-conditions': 'User does not exist in system, valid email format, strong password',
    'Test Steps': '1. Send POST request to /app-api/user/signup\n2. Validate input data using express-validator\n3. Check user existence in database\n4. Hash password using bcrypt\n5. Create user record with default settings\n6. Generate JWT token\n7. Create default tags\n8. Allocate AI API credits (500)\n9. Send verification email\n10. Log activity',
    'Input Data': '{\n  "name": "John Doe",\n  "email": "<EMAIL>",\n  "password": "SecurePass123!",\n  "login_type": "KyuleBag",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Registration successful",\n  "data": {\n    "user_id": "user123",\n    "token": "jwt_token",\n    "email_verified": "0",\n    "storage_allocated": "5MB",\n    "ai_credits": 500\n  }\n}',
    'Post-conditions': 'User created in database, verification email sent, default tags created, JWT token generated',
    'Dependencies': 'Database, Email service, JWT service, bcrypt',
    'Tags': 'authentication, registration, kyulebag-account, critical-path',
    'Request-Response Lifecycle Stage': 'Complete Registration Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format (RFC 5322), password min 8 chars, required fields',
    'Business Logic': 'User creation, password hashing, token generation, default setup',
    'Database Operations': 'INSERT tbl_users, INSERT tbl_user_tokens, INSERT tbl_tag_names, SELECT for duplicates',
    'External Services': 'Email verification service',
    'Error Handling': 'Duplicate email, invalid format, database errors',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'common.service.signup',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup → common.controller.signup',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Core authentication functionality - highest priority'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Google OAuth Integration',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.INTEGRATION,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify successful user registration using Google OAuth authentication',
    'Pre-conditions': 'Valid Google OAuth token provided, user not registered with this email',
    'Test Steps': '1. Send POST to /app-api/user/signup with Google token\n2. Validate Google OAuth token\n3. Extract user info from Google API\n4. Check existing user by email\n5. Create user with Google login type\n6. Generate JWT token\n7. Set up default profile and settings',
    'Input Data': '{\n  "name": "Jane Smith",\n  "email": "<EMAIL>",\n  "login_type": "google",\n  "google_token": "google_oauth_token_here",\n  "device_token": "device456"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Google registration successful",\n  "data": {\n    "user_id": "user124",\n    "token": "jwt_token",\n    "login_type": "google",\n    "email_verified": "1"\n  }\n}',
    'Post-conditions': 'User created with Google login type, JWT token generated, email pre-verified',
    'Dependencies': 'Google OAuth service, Database, JWT service',
    'Tags': 'authentication, registration, google-oauth, integration',
    'Request-Response Lifecycle Stage': 'OAuth Registration Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No (OAuth token required)',
    'Validation Rules': 'Google token validation, email format',
    'Business Logic': 'OAuth user creation, token validation, profile setup',
    'Database Operations': 'INSERT tbl_users, INSERT tbl_user_tokens, SELECT for duplicates',
    'External Services': 'Google OAuth verification API',
    'Error Handling': 'Invalid OAuth token, duplicate account, Google API errors',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'common.service.signup',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup → common.controller.signup',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'OAuth integration test - critical for social login'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Facebook OAuth Integration',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.INTEGRATION,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify successful user registration using Facebook OAuth authentication',
    'Pre-conditions': 'Valid Facebook OAuth token provided, user not registered',
    'Test Steps': '1. Send POST to /app-api/user/signup with Facebook token\n2. Validate Facebook OAuth token\n3. Extract user info from Facebook API\n4. Check existing user by email\n5. Create user with Facebook login type\n6. Generate JWT token\n7. Set up default profile',
    'Input Data': '{\n  "name": "Mike Johnson",\n  "email": "<EMAIL>",\n  "login_type": "facebook",\n  "facebook_token": "facebook_oauth_token",\n  "device_token": "device789"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Facebook registration successful",\n  "data": {\n    "user_id": "user125",\n    "token": "jwt_token",\n    "login_type": "facebook"\n  }\n}',
    'Post-conditions': 'User created with Facebook login type, JWT token generated',
    'Dependencies': 'Facebook OAuth service, Database, JWT service',
    'Tags': 'authentication, registration, facebook-oauth, integration',
    'Request-Response Lifecycle Stage': 'OAuth Registration Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No (OAuth token required)',
    'Validation Rules': 'Facebook token validation, email format',
    'Business Logic': 'OAuth user creation, token validation',
    'Database Operations': 'INSERT tbl_users, INSERT tbl_user_tokens, SELECT for duplicates',
    'External Services': 'Facebook OAuth verification API',
    'Error Handling': 'Invalid OAuth token, duplicate account, Facebook API errors',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'common.service.signup',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup → common.controller.signup',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Facebook OAuth integration test'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Apple Sign-In Integration',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.INTEGRATION,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify successful user registration using Apple Sign-In authentication',
    'Pre-conditions': 'Valid Apple Sign-In token provided, user not registered',
    'Test Steps': '1. Send POST to /app-api/user/signup with Apple token\n2. Validate Apple Sign-In token\n3. Extract user info from Apple\n4. Handle privacy email if provided\n5. Create user with Apple login type\n6. Generate JWT token',
    'Input Data': '{\n  "name": "Sarah Wilson",\n  "email": "<EMAIL>",\n  "login_type": "apple",\n  "apple_token": "apple_signin_token",\n  "device_token": "device101"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Apple registration successful",\n  "data": {\n    "user_id": "user126",\n    "token": "jwt_token",\n    "login_type": "apple"\n  }\n}',
    'Post-conditions': 'User created with Apple login type, JWT token generated',
    'Dependencies': 'Apple Sign-In service, Database, JWT service',
    'Tags': 'authentication, registration, apple-signin, integration',
    'Request-Response Lifecycle Stage': 'OAuth Registration Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No (Apple token required)',
    'Validation Rules': 'Apple token validation, email format',
    'Business Logic': 'OAuth user creation, privacy email handling',
    'Database Operations': 'INSERT tbl_users, INSERT tbl_user_tokens, SELECT for duplicates',
    'External Services': 'Apple Sign-In verification API',
    'Error Handling': 'Invalid Apple token, duplicate account, Apple API errors',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'common.service.signup',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup → common.controller.signup',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Apple Sign-In integration test'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Duplicate Email Error',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.EDGE_CASE,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system prevents registration with existing email address',
    'Pre-conditions': 'User with email already exists in system',
    'Test Steps': '1. Send POST to /app-api/user/signup with existing email\n2. Validate input data\n3. Check user existence in database\n4. Return appropriate error response\n5. Log failed attempt',
    'Input Data': '{\n  "name": "Duplicate User",\n  "email": "<EMAIL>",\n  "password": "NewPass123!",\n  "login_type": "KyuleBag"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "User already exists",\n  "data": {}\n}',
    'Post-conditions': 'No new user created, appropriate error returned',
    'Dependencies': 'Database',
    'Tags': 'authentication, duplicate-email, error-handling, edge-case',
    'Request-Response Lifecycle Stage': 'Business Logic Validation',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Email uniqueness check',
    'Business Logic': 'Duplicate prevention logic',
    'Database Operations': 'SELECT for existence check',
    'External Services': 'None',
    'Error Handling': 'Duplicate email error response',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'common.service.signup',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup → common.controller.signup',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical for data integrity'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Invalid Email Format',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.VALIDATION,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify system rejects registration with invalid email format',
    'Pre-conditions': 'Invalid email format provided in request',
    'Test Steps': '1. Send POST to /app-api/user/signup with invalid email\n2. Validate email format using express-validator\n3. Return validation error before processing',
    'Input Data': '{\n  "name": "Test User",\n  "email": "invalid-email-format",\n  "password": "ValidPass123!",\n  "login_type": "KyuleBag"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Invalid email format",\n  "data": {}\n}',
    'Post-conditions': 'Request rejected at validation stage',
    'Dependencies': 'Express-validator',
    'Tags': 'authentication, validation, email-format, input-validation',
    'Request-Response Lifecycle Stage': 'Input Validation Stage',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format validation (RFC 5322)',
    'Business Logic': 'Input validation',
    'Database Operations': 'None (rejected before DB)',
    'External Services': 'None',
    'Error Handling': 'Validation error response',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'None (rejected at validation)',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup (fails here)',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Input validation test'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Weak Password Rejection',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SECURITY,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system rejects registration with weak password',
    'Pre-conditions': 'Password does not meet strength requirements',
    'Test Steps': '1. Send POST to /app-api/user/signup with weak password\n2. Validate password strength using express-validator\n3. Return validation error for weak password',
    'Input Data': '{\n  "name": "Test User",\n  "email": "<EMAIL>",\n  "password": "123",\n  "login_type": "KyuleBag"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Password must be at least 8 characters",\n  "data": {}\n}',
    'Post-conditions': 'Request rejected due to weak password',
    'Dependencies': 'Password validation rules',
    'Tags': 'authentication, security, password-strength, validation',
    'Request-Response Lifecycle Stage': 'Input Validation Stage',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Password minimum length (8 chars), complexity requirements',
    'Business Logic': 'Security validation',
    'Database Operations': 'None (rejected before DB)',
    'External Services': 'None',
    'Error Handling': 'Password strength error',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'None (rejected at validation)',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup (fails here)',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Security validation test'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - Missing Required Fields',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.VALIDATION,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify system rejects registration when required fields are missing',
    'Pre-conditions': 'Required fields (name, email, password) not provided',
    'Test Steps': '1. Send POST to /app-api/user/signup with missing fields\n2. Validate required fields using express-validator\n3. Return validation error listing missing fields',
    'Input Data': '{\n  "email": "<EMAIL>"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Name is required\\nPassword is required",\n  "data": {}\n}',
    'Post-conditions': 'Request rejected due to missing required fields',
    'Dependencies': 'Express-validator',
    'Tags': 'authentication, validation, required-fields, input-validation',
    'Request-Response Lifecycle Stage': 'Input Validation Stage',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Required field validation (name, email, password, login_type)',
    'Business Logic': 'Input validation',
    'Database Operations': 'None (rejected before DB)',
    'External Services': 'None',
    'Error Handling': 'Missing field validation errors',
    'Controller Method': 'common.controller.signup',
    'Service Method': 'None (rejected at validation)',
    'Middleware Chain': 'imageMiddleware → validators.app.common.signup (fails here)',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Required field validation test'
  });

  // User Sign-In Tests (8 tests)
  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Sign-In - Valid Email and Password',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify successful user sign-in with valid email and password',
    'Pre-conditions': 'User exists in system, account is active and verified',
    'Test Steps': '1. Send POST to /app-api/user/signin\n2. Validate input data\n3. Find user by email\n4. Verify password using bcrypt\n5. Check account status (not deleted, verified)\n6. Generate new JWT token\n7. Update device token\n8. Return user profile with token',
    'Input Data': '{\n  "email": "<EMAIL>",\n  "password": "SecurePass123!",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Login successful",\n  "data": {\n    "user_id": "user123",\n    "token": "new_jwt_token",\n    "profile": {...},\n    "storage_used": "2.5MB",\n    "storage_total": "5MB"\n  }\n}',
    'Post-conditions': 'User authenticated, new JWT token generated, device token updated',
    'Dependencies': 'Database, JWT service, bcrypt',
    'Tags': 'authentication, signin, email-login, critical-path',
    'Request-Response Lifecycle Stage': 'Complete Authentication Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signin',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format, password required, device_token optional',
    'Business Logic': 'User authentication, password verification, token generation',
    'Database Operations': 'SELECT user, UPDATE device_token, INSERT user_token',
    'External Services': 'None',
    'Error Handling': 'Invalid credentials, account status errors',
    'Controller Method': 'common.controller.signin',
    'Service Method': 'common.service.signin',
    'Middleware Chain': 'validators.app.common.signin → common.controller.signin',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Core authentication functionality'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Sign-In - Invalid Password',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SECURITY,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system rejects sign-in with incorrect password',
    'Pre-conditions': 'User exists in system, incorrect password provided',
    'Test Steps': '1. Send POST to /app-api/user/signin with wrong password\n2. Validate input data\n3. Find user by email\n4. Verify password using bcrypt (fails)\n5. Return authentication error',
    'Input Data': '{\n  "email": "<EMAIL>",\n  "password": "WrongPassword123!",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Invalid credentials",\n  "data": {}\n}',
    'Post-conditions': 'Authentication failed, no token generated',
    'Dependencies': 'Database, bcrypt',
    'Tags': 'authentication, signin, invalid-password, security',
    'Request-Response Lifecycle Stage': 'Authentication Verification',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signin',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format, password required',
    'Business Logic': 'Password verification failure handling',
    'Database Operations': 'SELECT user',
    'External Services': 'None',
    'Error Handling': 'Invalid password error response',
    'Controller Method': 'common.controller.signin',
    'Service Method': 'common.service.signin',
    'Middleware Chain': 'validators.app.common.signin → common.controller.signin',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Security test for password verification'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Sign-In - Non-existent Email',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.EDGE_CASE,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify system handles sign-in attempt with non-existent email',
    'Pre-conditions': 'Email does not exist in system',
    'Test Steps': '1. Send POST to /app-api/user/signin with non-existent email\n2. Validate input data\n3. Attempt to find user by email (not found)\n4. Return user not found error',
    'Input Data': '{\n  "email": "<EMAIL>",\n  "password": "SomePassword123!",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "User not found",\n  "data": {}\n}',
    'Post-conditions': 'Authentication failed, no token generated',
    'Dependencies': 'Database',
    'Tags': 'authentication, signin, user-not-found, edge-case',
    'Request-Response Lifecycle Stage': 'User Lookup',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signin',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format, password required',
    'Business Logic': 'User existence check',
    'Database Operations': 'SELECT user (returns null)',
    'External Services': 'None',
    'Error Handling': 'User not found error response',
    'Controller Method': 'common.controller.signin',
    'Service Method': 'common.service.signin',
    'Middleware Chain': 'validators.app.common.signin → common.controller.signin',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Edge case for non-existent users'
  });

  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Sign-In - Deleted Account',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.EDGE_CASE,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system prevents sign-in for deleted accounts',
    'Pre-conditions': 'User account exists but is marked as deleted (is_deleted = 1)',
    'Test Steps': '1. Send POST to /app-api/user/signin\n2. Validate input data\n3. Find user by email\n4. Check account status (is_deleted)\n5. Return account deleted error',
    'Input Data': '{\n  "email": "<EMAIL>",\n  "password": "ValidPass123!",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Account has been deleted",\n  "data": {}\n}',
    'Post-conditions': 'Authentication failed, no token generated',
    'Dependencies': 'Database',
    'Tags': 'authentication, signin, deleted-account, edge-case',
    'Request-Response Lifecycle Stage': 'Account Status Check',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signin',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format, password required',
    'Business Logic': 'Account status validation',
    'Database Operations': 'SELECT user with status check',
    'External Services': 'None',
    'Error Handling': 'Deleted account error response',
    'Controller Method': 'common.controller.signin',
    'Service Method': 'common.service.signin',
    'Middleware Chain': 'validators.app.common.signin → common.controller.signin',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Important for account lifecycle management'
  });

  return tests;
}

/**
 * Generate Comprehensive Item Management Test Cases (20 tests)
 */
function generateItemManagementTests() {
  const tests = [];
  let testId = 1;

  // Item Creation Tests (8 tests)
  tests.push({
    'Test Case ID': `ITEM-${testId++}`,
    'Test Scenario': 'Item Creation - Single Image Upload',
    'Module/Feature': MODULES.ITEM_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify successful creation of item with single image upload',
    'Pre-conditions': 'User authenticated, valid image file, sufficient storage quota',
    'Test Steps': '1. Send POST to /app-api/items/add with image\n2. Authenticate user via JWT\n3. Validate file type and size\n4. Check storage quota\n5. Upload to GCP storage\n6. Process image with AI/OCR if enabled\n7. Create item record\n8. Create image record\n9. Update activity logs\n10. Return success response',
    'Input Data': '{\n  "files": [image_file.jpg],\n  "tags": ["work", "documents"],\n  "note": "Important document"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Item added successfully",\n  "data": {\n    "item_id": "item123",\n    "file_count": 1,\n    "storage_used": "1.2MB",\n    "ai_labels": ["document", "text"]\n  }\n}',
    'Post-conditions': 'Item created, file uploaded to GCP, AI processing completed, activity logged',
    'Dependencies': 'JWT auth, GCP storage, AI/OCR service, Database',
    'Tags': 'item-management, file-upload, image-processing, critical-path',
    'Request-Response Lifecycle Stage': 'Complete Item Creation Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/add',
    'Authentication Required': 'Yes (JWT token)',
    'Validation Rules': 'File type (image/video/audio/document), file size limits, storage quota',
    'Business Logic': 'File upload, AI processing, storage management, item creation',
    'Database Operations': 'INSERT tbl_items, INSERT tbl_images, INSERT tbl_activity_logs, UPDATE storage',
    'External Services': 'GCP Cloud Storage, AI/OCR processing service',
    'Error Handling': 'Storage full, invalid file type, upload failures',
    'Controller Method': 'items.controller.add',
    'Service Method': 'items.service.add',
    'Middleware Chain': 'auth → multer → validators.app.items.add → items.controller.add',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Core item creation functionality'
  });

  tests.push({
    'Test Case ID': `ITEM-${testId++}`,
    'Test Scenario': 'Item Creation - Bulk File Upload',
    'Module/Feature': MODULES.ITEM_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify successful bulk upload of multiple files in single request',
    'Pre-conditions': 'User authenticated, multiple valid files, sufficient storage quota',
    'Test Steps': '1. Send POST to /app-api/items/bulkAdd with multiple files\n2. Authenticate user\n3. Validate all files\n4. Check total storage requirement\n5. Process files in parallel\n6. Upload all files to GCP\n7. Create item records for each\n8. Process AI/OCR for eligible files\n9. Update activity logs\n10. Return bulk operation results',
    'Input Data': '{\n  "files": [image1.jpg, video1.mp4, document1.pdf],\n  "tags": ["project", "media"],\n  "note": "Project files"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Bulk upload completed",\n  "data": {\n    "total_items": 3,\n    "successful": 3,\n    "failed": 0,\n    "storage_used": "15.7MB"\n  }\n}',
    'Post-conditions': 'Multiple items created, all files uploaded, AI processing completed',
    'Dependencies': 'JWT auth, GCP storage, AI/OCR service, Database',
    'Tags': 'item-management, bulk-upload, file-processing, performance',
    'Request-Response Lifecycle Stage': 'Bulk Processing Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/bulkAdd',
    'Authentication Required': 'Yes (JWT token)',
    'Validation Rules': 'Multiple file validation, total size limits, file type restrictions',
    'Business Logic': 'Bulk processing, parallel uploads, batch database operations',
    'Database Operations': 'Bulk INSERT operations, transaction management',
    'External Services': 'GCP Cloud Storage, AI/OCR processing service',
    'Error Handling': 'Partial failures, storage limits, processing errors',
    'Controller Method': 'items.controller.bulkAdd',
    'Service Method': 'items.service.bulkAdd',
    'Middleware Chain': 'auth → multer → validators.app.items.bulkAdd → items.controller.bulkAdd',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Bulk operation performance test'
  });

  tests.push({
    'Test Case ID': `ITEM-${testId++}`,
    'Test Scenario': 'Item Creation - Storage Quota Exceeded',
    'Module/Feature': MODULES.ITEM_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.EDGE_CASE,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system prevents item creation when storage quota is exceeded',
    'Pre-conditions': 'User at or near storage limit, file size would exceed quota',
    'Test Steps': '1. Send POST to /app-api/items/add with large file\n2. Authenticate user\n3. Check current storage usage\n4. Calculate total size after upload\n5. Compare with user quota\n6. Return storage full error',
    'Input Data': '{\n  "files": [large_file.mp4],\n  "tags": ["video"],\n  "note": "Large video file"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Storage quota exceeded",\n  "data": {\n    "current_usage": "4.8MB",\n    "quota": "5MB",\n    "file_size": "2MB"\n  }\n}',
    'Post-conditions': 'No item created, no file uploaded, quota enforced',
    'Dependencies': 'JWT auth, Database',
    'Tags': 'item-management, storage-quota, edge-case, business-logic',
    'Request-Response Lifecycle Stage': 'Storage Validation',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/add',
    'Authentication Required': 'Yes (JWT token)',
    'Validation Rules': 'Storage quota validation, file size calculation',
    'Business Logic': 'Storage quota enforcement',
    'Database Operations': 'SELECT storage usage calculation',
    'External Services': 'None',
    'Error Handling': 'Storage quota exceeded error',
    'Controller Method': 'items.controller.add',
    'Service Method': 'items.service.add',
    'Middleware Chain': 'auth → multer → validators.app.items.add → items.controller.add',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical for subscription management'
  });

  tests.push({
    'Test Case ID': `ITEM-${testId++}`,
    'Test Scenario': 'Item Creation - Invalid File Type',
    'Module/Feature': MODULES.ITEM_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.VALIDATION,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify system rejects unsupported file types',
    'Pre-conditions': 'User authenticated, unsupported file type provided',
    'Test Steps': '1. Send POST to /app-api/items/add with unsupported file\n2. Authenticate user\n3. Validate file type using multer\n4. Return file type error',
    'Input Data': '{\n  "files": [malicious_file.exe],\n  "tags": ["test"],\n  "note": "Test file"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Unsupported file type",\n  "data": {\n    "supported_types": ["jpg", "png", "mp4", "pdf", "docx"]\n  }\n}',
    'Post-conditions': 'Request rejected, no file uploaded',
    'Dependencies': 'JWT auth, Multer middleware',
    'Tags': 'item-management, file-validation, security, validation',
    'Request-Response Lifecycle Stage': 'File Type Validation',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/add',
    'Authentication Required': 'Yes (JWT token)',
    'Validation Rules': 'File type whitelist validation',
    'Business Logic': 'File type security validation',
    'Database Operations': 'None (rejected before DB)',
    'External Services': 'None',
    'Error Handling': 'Unsupported file type error',
    'Controller Method': 'items.controller.add',
    'Service Method': 'None (rejected at middleware)',
    'Middleware Chain': 'auth → multer (fails here)',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Security validation test'
  });

  return tests;
}

/**
 * Generate Comprehensive Tag Management Test Cases (15 tests)
 */
function generateTagManagementTests() {
  const tests = [];
  let testId = 1;

  tests.push({
    'Test Case ID': `TAG-${testId++}`,
    'Test Scenario': 'Tag Creation - Valid New Tag',
    'Module/Feature': MODULES.TAG_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.UNIT,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify successful creation of new tag',
    'Pre-conditions': 'User authenticated, tag name does not exist for user',
    'Test Steps': '1. Send POST to /app-api/tags/add\n2. Authenticate user\n3. Validate tag name\n4. Check for duplicate tag\n5. Create tag record\n6. Log activity\n7. Return created tag',
    'Input Data': '{\n  "tag_name": "work-project"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Tag created successfully",\n  "data": {\n    "tag_id": "tag123",\n    "tag_name": "work-project",\n    "created_at": "2024-01-15 10:30:00"\n  }\n}',
    'Post-conditions': 'Tag created in database, activity logged',
    'Dependencies': 'JWT auth, Database',
    'Tags': 'tag-management, creation, user-organization',
    'Request-Response Lifecycle Stage': 'Complete Tag Creation',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/tags/add',
    'Authentication Required': 'Yes (JWT token)',
    'Validation Rules': 'Tag name required, length limits, special characters',
    'Business Logic': 'Tag creation, duplicate prevention',
    'Database Operations': 'INSERT tbl_tag_names, INSERT tbl_activity_logs, SELECT for duplicates',
    'External Services': 'None',
    'Error Handling': 'Duplicate tag, validation errors',
    'Controller Method': 'tags.controller.add',
    'Service Method': 'tags.service.add',
    'Middleware Chain': 'auth → validators.app.tags.add → tags.controller.add',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Core tag functionality'
  });

  tests.push({
    'Test Case ID': `TAG-${testId++}`,
    'Test Scenario': 'Tag List Retrieval - User Tags',
    'Module/Feature': MODULES.TAG_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.UNIT,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify retrieval of all tags for authenticated user',
    'Pre-conditions': 'User authenticated, user has existing tags',
    'Test Steps': '1. Send GET to /app-api/tags/list\n2. Authenticate user\n3. Query user tags from database\n4. Format timestamps\n5. Return tag list',
    'Input Data': 'No body data (GET request)',
    'Expected Response': '{\n  "status": 1,\n  "message": "Tags retrieved successfully",\n  "data": {\n    "count": 5,\n    "tags": [...]\n  }\n}',
    'Post-conditions': 'User tags returned with proper formatting',
    'Dependencies': 'JWT auth, Database',
    'Tags': 'tag-management, retrieval, user-data',
    'Request-Response Lifecycle Stage': 'Data Retrieval',
    'HTTP Method': 'GET',
    'API Endpoint': '/app-api/tags/list',
    'Authentication Required': 'Yes (JWT token)',
    'Validation Rules': 'User authentication only',
    'Business Logic': 'User-specific tag retrieval',
    'Database Operations': 'SELECT tbl_tag_names WHERE user_id',
    'External Services': 'None',
    'Error Handling': 'No tags found, database errors',
    'Controller Method': 'tags.controller.list',
    'Service Method': 'tags.service.list',
    'Middleware Chain': 'auth → tags.controller.list',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Basic retrieval functionality'
  });

  return tests;
}

/**
 * Generate Excel Workbook with All Test Cases
 */
function generateExcelWorkbook() {
  console.log('🚀 Generating Comprehensive Backend Test Case Documentation...');

  const workbook = XLSX.utils.book_new();

  // Generate all test cases
  const authTests = generateAuthenticationTests();
  const itemTests = generateItemManagementTests();
  const tagTests = generateTagManagementTests();

  // Combine all tests
  const allTests = [
    ...authTests,
    ...itemTests,
    ...tagTests
  ];

  console.log(`📊 Generated ${allTests.length} comprehensive test cases`);

  // Create Test Summary Sheet
  const summaryData = [
    ['Module', 'Test Count', 'Critical', 'High', 'Medium', 'Low'],
    ['Authentication & Authorization', authTests.length,
     authTests.filter(t => t.Priority === PRIORITY.CRITICAL).length,
     authTests.filter(t => t.Priority === PRIORITY.HIGH).length,
     authTests.filter(t => t.Priority === PRIORITY.MEDIUM).length,
     authTests.filter(t => t.Priority === PRIORITY.LOW).length],
    ['Item Management', itemTests.length,
     itemTests.filter(t => t.Priority === PRIORITY.CRITICAL).length,
     itemTests.filter(t => t.Priority === PRIORITY.HIGH).length,
     itemTests.filter(t => t.Priority === PRIORITY.MEDIUM).length,
     itemTests.filter(t => t.Priority === PRIORITY.LOW).length],
    ['Tag Management', tagTests.length,
     tagTests.filter(t => t.Priority === PRIORITY.CRITICAL).length,
     tagTests.filter(t => t.Priority === PRIORITY.HIGH).length,
     tagTests.filter(t => t.Priority === PRIORITY.MEDIUM).length,
     tagTests.filter(t => t.Priority === PRIORITY.LOW).length],
    ['TOTAL', allTests.length,
     allTests.filter(t => t.Priority === PRIORITY.CRITICAL).length,
     allTests.filter(t => t.Priority === PRIORITY.HIGH).length,
     allTests.filter(t => t.Priority === PRIORITY.MEDIUM).length,
     allTests.filter(t => t.Priority === PRIORITY.LOW).length]
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Test Summary');

  // Create All Tests Sheet
  const allTestsData = [HEADERS, ...allTests.map(test => HEADERS.map(header => test[header] || ''))];
  const allTestsSheet = XLSX.utils.aoa_to_sheet(allTestsData);
  XLSX.utils.book_append_sheet(workbook, allTestsSheet, 'All Test Cases');

  // Create module-specific sheets
  const modules = [
    { name: 'Authentication Tests', tests: authTests },
    { name: 'Item Management Tests', tests: itemTests },
    { name: 'Tag Management Tests', tests: tagTests }
  ];

  modules.forEach(module => {
    const moduleData = [HEADERS, ...module.tests.map(test => HEADERS.map(header => test[header] || ''))];
    const moduleSheet = XLSX.utils.aoa_to_sheet(moduleData);
    XLSX.utils.book_append_sheet(workbook, moduleSheet, module.name);
  });

  // Write the file
  const filename = 'Comprehensive_Backend_Test_Cases.xlsx';
  XLSX.writeFile(workbook, filename);

  console.log(`✅ Excel file generated: ${filename}`);
  console.log(`📈 Total test cases: ${allTests.length}`);
  console.log('📋 Sheets created:');
  console.log('   - Test Summary');
  console.log('   - All Test Cases');
  modules.forEach(module => console.log(`   - ${module.name}`));

  return filename;
}

// Execute the generation
if (require.main === module) {
  generateExcelWorkbook();
}

module.exports = {
  generateAuthenticationTests,
  generateItemManagementTests,
  generateTagManagementTests,
  generateExcelWorkbook
};
