"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class tbl_item_tags extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      tbl_item_tags.belongsTo(models.tbl_tag_names, {
        targetKey: "tag_name_id",
        foreignKey: "tag_name_id",
        as: "items_tag_names",
      });
    }
  }
  tbl_item_tags.init(
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      item_id: DataTypes.STRING,
      tag_name_id: DataTypes.INTEGER,
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize,
      modelName: "tbl_item_tags",
    }
  );
  return tbl_item_tags;
};
