const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const adminRole_service = require("../../services/cms/adminRole.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  // add role
  async addNewRole(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let add_role_res = await adminRole_service.addNewRole(req, res);
      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: add_role_res,
        message: message.ROLEADDED,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("add roles -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // edit role
  async editRole(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let edit_role_res = await adminRole_service.editRole(req, res);
      if (edit_role_res === 0) {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          data: [],
          message: message.ROLENOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          // data: [],
          data: edit_role_res,
          message: message.ROLEUPDATED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("edit role -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // delete admin
  async deleteRole(req, res) {
    try {
      let delete_role = await adminRole_service.deleteRole(req, res);
      if (delete_role) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ROLEDELETED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // listing roles
  async listAdminRole(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_admin_role_res = await adminRole_service.listAdminRole(req, res);
      if (list_admin_role_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_role_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ROLENOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listAdminRole -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // subadminAssignRole
  async subadminAssignRole(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let subadmin_assignrole_res = await adminRole_service.subadminAssignRole(
        req,
        res
      );
      if (!subadmin_assignrole_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: subadmin_assignrole_res,
          message: message.ROLENOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: subadmin_assignrole_res,
          message: message.ASSIGNROLES,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("subadminAssignRole -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // subadmin assign rolelist
  async subadminAssignRolelist(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_admin_role_res = await adminRole_service.subadminAssignRolelist(
        req,
        res
      );
      if (list_admin_role_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_role_res,
          message: message.ASSIGNROLELIST,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_role_res,
          message: message.ROLENOTFOUND,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("retrieveAdminRole -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // retrive role
  async retriveAdminRole(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_admin_role_res = await adminRole_service.retriveAdminRole(
        req,
        res
      );
      if (!list_admin_role_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_role_res,
          message: message.ROLENOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_role_res,
          message: message.ROLELIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("retrieveAdminRole -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
