const User = require("../../database/models").tbl_users;
const Activity_logs = require("../../database/models").tbl_activity_logs;
const Bcryptjs = require("bcryptjs"); /* For encryption and decryption */
const generatedSalt = Bcryptjs.genSaltSync(10);
const randomStringHelper = require("../../helper/general.helper");
const Mail = require("../../helper/sendmail");
const Sequelize = require("sequelize");
const Op = Sequelize.Op;

module.exports = {
  /* add */
  async private_note_pswd_add(req, res) {
    let { private_note_password } = req.body;
    var show_private_password = {};

    var private_note = await User.findOne({
      where: {
        user_id: req.user_id,
        is_deleted: "0",
      },
    });
    if (private_note.dataValues.private_note_password !== null) {
      return 0;
    }
    let encrypted_password = await Bcryptjs.hash(
      private_note_password,
      generatedSalt
    );

    var private_note_pswd = await User.update(
      {
        // private_note_password: encrypted_password,
        private_note_password: private_note_password,

      },
      {
        where: {
          user_id: req.user_id,
          is_deleted: "0",

        },
      }
    );
    var find_private_pswd = await User.findOne({
      where: {
        user_id: req.user_id,
        is_deleted: "0",

      },
    });
    show_private_password.private_note_password = private_note_password;
    var item_activity_log = await Activity_logs.create({
      user_id: req.user_id,
      title: "Add Private-notes Pin",
      // description: `Pin for private-notes has been created.`
      description: `PIN created for Private mode.`
    });

    return show_private_password;
  },

  //change private note password
  async change_private_note_password(req, res) {
    let { old_private_note_password, new_private_note_password } = req.body;
    var change_private_note_password = {};
    var find_user = await User.findOne({
      where: {
        user_id: req.user_id,
        is_deleted: "0",

      },
      attributes: ["user_id", "private_note_password"],
    });


    if (!find_user) {

      return 3;
    } else {
      if (
        find_user.private_note_password === null ||
        find_user.private_note_password === ""
      ) {
        return 4;
      }
      if (
        // !Bcryptjs.compareSync(
        //   old_private_note_password,
        //   find_user.private_note_password
        old_private_note_password !== find_user.private_note_password

      ) {

        // check password
        return 2; //password not valid
      }

      let encrypted_new_password = await Bcryptjs.hash(
        new_private_note_password,
        generatedSalt
      );
      var updateOtp = await User.update(
        // { private_note_password: encrypted_new_password },
        { private_note_password: new_private_note_password },

        {
          where: {
            user_id: req.user_id,
            is_deleted: "0",

          },
        }
      );
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Update Private-notes Pin",
        // description:`Password for private-notes changed to ${new_private_note_password}.`
        // description: `Pin for private-notes changed to old pin.`
        description: `Private mode PIN changed.`
      });
      change_private_note_password.old_private_note_password =
        old_private_note_password;
      change_private_note_password.new_private_note_password =
        new_private_note_password;

      return 1;
    }
  },

  /* forgot private note password */
  async forgot_private_note_password(req, res) {
    let phone =
      req.body.phone === undefined || req.body.phone === ""
        ? ""
        : req.body.phone;
    let email =
      req.body.email === undefined || req.body.email === ""
        ? ""
        : req.body.email;

    if (phone) {
      var find_user = await User.findOne({
        where: {
          [Op.or]: {
            phone: phone,
          },
        },
        attributes: [
          "email",
          "first_name",
          "last_name",
          "user_id",
          "country_code",
          "phone",
          "email_verified",
        ],
      });
      if (!find_user) {
        var find_user = await AlternativeEmail.findOne({
          where: {
            [Op.or]: {
              phone_number: phone,
            }
          }
        });
      }
    } else if (email) {
      var find_user = await User.findOne({
        where: {
          [Op.or]: {
            email: email,
          },
        },
        attributes: [
          "email",
          "first_name",
          "last_name",
          "user_id",
          "country_code",
          "phone",
          "email_verified",
        ],
      });
      if (!find_user) {
        var find_user = await AlternativeEmail.findOne({
          where: {
            [Op.or]: {
              email: email,
            }
          }
        });
      }
    }

    if (!find_user) {
      return 0;
    } else {
      if (find_user.email_verified === "0") {
        return 0;
      }
      let get_otp = randomStringHelper.generateOtp();
      if (email) {
        var subject = "Kyulebag : Forgot Private Mode PIN OTP";
        var mailbody =
          "<div>" +
          "<p>Hello " +
          find_user.first_name +
          "," +
          "</p>" +
          "<p>Your KyuleBag Private Mode PIN OTP is : <b>" +
          get_otp +
          "</b></p>" +
          "<p>Kindly use the OTP code for change Private Mode PIN.</p>" +
          "<p>" +
          process.env.APPNAME +
          "," +
          "</p>" +
          "</div>";

        await Mail.sendmail(res, find_user.email, subject, mailbody);
      } else {
        var telnyx = require("telnyx")(constant.TELNYX_DETAILS.API_KEY);
        telnyx.messages.create(
          {
            from: constant.TELNYX_DETAILS.PRIMARY_NUMBER, // Your Telnyx number
            to: "+" + find_user.country_code + find_user.phone,
            text:
              "Your KyuleBag Privet Mode PIN OTP is " +
              get_otp +
              ". Kindly use the OTP code for change Privet Mode PIN.",
          },
          function (err, response) {
            // asynchronously called
            console.log(response);
          }
        );
      }
      await User.update(
        {
          verification_code: get_otp,
        },
        { where: { user_id: find_user.user_id } }
      );
      return find_user;
    }
  },

  /* reset private note password */
  async reset_private_note_password(req, res) {
    let { new_password, otp } = req.body;
    let find_user = await User.findOne({
      where: {
        user_id: req.user_id,
        // verification_code: otp,
      },
      attributes: [
        "email",
        "first_name",
        "last_name",
        "user_id",
        "password",
        "verification_code",
      ],
    });
    if (!find_user) {
      return 2;
    } else {
      if (find_user.verification_code !== otp) {
        return 3;
      }
      // let encrypted_password = await Bcryptjs.hash(new_password, generatedSalt);
      let updateOtp = await User.update(
        { private_note_password: new_password, verification_code: null },
        {
          where: {
            user_id: find_user.user_id,
          },
        }
      );
      return 1;
    }
  },
};
