const { DataTypes, where } = require("sequelize");
const { sequelize } = require("../../database/models");
const Admin = require("../../database/models/tbl_admins")(sequelize, DataTypes);
const AdminToken = require("../../database/models/tbl_admin_tokens")(sequelize, DataTypes);
const AssignRolelist = require("../../database/models/tbl_admin_roles")(sequelize, DataTypes);
const Bcryptjs = require("bcryptjs");
const generatedSalt = Bcryptjs.genSaltSync(10);
const constant = require("../../config/constant");
const JwtToken = require("jsonwebtoken");
const gcpUpload = require("../../middleware/multer_gcp_upload");
const gcpDelete = require("../../middleware/multer_gcp_delete");
const randomStringHelper = require("../../helper/general.helper");
const Mail = require("../../helper/sendmail");
const gcpUtils = require("../../helper/gcpUtils");

module.exports = {
    /* Login */
  async logIn(req, res) {
    let find_admin = await Admin.findOne({
      where: {
        email: req.body.email,
        is_deleted: "0",
      },

    });

    if (find_admin) {
      if (Bcryptjs.compareSync(req.body.password, find_admin.password)) {
        // if (md5(req.body.password, find_admin.password)) {
        // check password and its correct

        // generate JWT token with email and admin id
        var jwt_string = JwtToken.sign(
          {
            email: find_admin.dataValues.email,
            admin_id: find_admin.dataValues.admin_id,
          },
          constant.JWTTOKEN.secret,
          {
            expiresIn: "30m",  // Client said to change Admin token expire in 30 min  
            algorithm: constant.JWTAPPTOKEN.algo,
          }
        );
        let admin_token = await AdminToken.findOne({
          where : {
            admin_id: find_admin.dataValues.admin_id,
          }
        })
        if (admin_token === null) {
          let abc_create = await AdminToken.create({
            access_token: jwt_string,
            admin_id: find_admin.dataValues.admin_id,
          });
        } else {
          let upadte_token = await AdminToken.update(
            { access_token: jwt_string },
            {
              where: {
                admin_id: find_admin.dataValues.admin_id,
              }
            }
          )
        }
        let find_admin_rolelist = await AssignRolelist.findAll({
          where: { admin_id: find_admin.dataValues.admin_id },
        });
        // await Admin.update({ is_active: find_admin.dataValues.is_active },{ where : {admin_id:req.body.admin_id}})
        find_admin.dataValues.token = jwt_string;
        // if (find_admin.dataValues.photo) {
        //   // find_admin.dataValues.photo =
        //   //   constant.GCP_URL +
        //   //   constant.GCP_BUCKET_NAME +
        //   //   "/" +
        //   //   constant.GCP_BUCKET_FOLDER +
        //   //   constant.GCP_ADMIN_FOLDER +
        //   //   find_admin.dataValues.admin_id +
        //   //   "/" +
        //   //   find_admin.dataValues.photo;
        //   find_admin.dataValues.photo = await gcpUtils.getPrivateUrl(
        //     constant.GCP_BUCKET_NAME,
        //     constant.GCP_BUCKET_FOLDER +
        //     constant.GCP_ADMIN_FOLDER +
        //     find_admin.dataValues.admin_id +
        //     "/" +
        //     find_admin.photo, /*expires: '2023-12-02' */
        //   )
        // } 
        find_admin.dataValues.photo = null;
        delete find_admin.dataValues.password;
        find_admin.dataValues.admin_assignrole = find_admin_rolelist;

        let passKey = randomStringHelper.generateOTP();

        var subject = "Two-Factor Authentication";
        var mailbody =
          "<div style={{ fontFamily: 'Arial, sans-serif' }}>" +
          "<p>Hello " +
          find_admin.firstName +
          "," +
          "</p>" +
          "<p><br/>Please use the following security code to complete the authentication: </p>" +
          "<p>Security Code: <b>" +
          passKey +
          "</b></p>" +
          "<p>If you did not request this code or are unsure why you received this email, please contact our support team immediately.</p>" +
          "<p>" +
          process.env.APPNAME +
          "</p>" +
          "</div>";

        let send_mail = await Mail.sendmail(
          res,
          req.body.email,
          subject,
          mailbody
        );

        find_admin.dataValues.passKey = ((passKey + 7832) * 7832)

        return find_admin;
      } else {
        return 1; //password not correct
      }
    } else {
      return 0;
    }
  },

  /**  logout */
  async lognOut(req) {
    let logOut = await AdminToken.destroy({
      where: {
        access_token: req.headers.authorization,
      },
    });
    return logOut;
  },

  /** forgot password */
  async forgotPassword(req, res) {
    let find_admin = await Admin.findOne({ where: { email: req.body.email } });

    if (find_admin) {
      let new_password = randomStringHelper.generateOTP();
      // let new_password = generatePassword.generateRandomString(8);
      // let newPassword = await Bcryptjs.hash(
      //   new_password,
      //   generatedSalt
      // );
      // await find_admin.update({
      //   password: newPassword,
      // },
      // { where: { admin_id: find_admin.admin_id } });

      var subject = 'Reset password';
      var mailbody =
        '<div><p>Hello ' +
        find_admin.firstName +
        ', <br/>' +
        '<br/> OTP ' +
        '<b>' +
        new_password +
        '</b>' +
        // "</p><p>Use the OTP to Login</p>" +
        '</p>' +
        // "<p>Ignore this if you did not request to  reset password.</p><p>" +
        '<p></p><p>' +
        process.env.APPNAME +
        '</p></div>';
      let send_mail = await Mail.sendmail(
        res,
        req.body.email,
        subject,
        mailbody
      );

      new_password = (new_password + 7832) * 7832;
      return new_password;
    } else {
      return 0;
    }
  },

  /* reset password */
  async resetPassword(req) {
    let find_admin = await Admin.findOne({ where: { email: req.body.email, is_deleted: "0" }});
    if (find_admin) {
      // let checkOldPassword = Bcryptjs.compareSync(
      //   req.body.old_password,
      //   find_admin.password
      // );
      let checkNewPassword =
        req.body.new_password === req.body.confirm_password;
      // if (!checkOldPassword) return 0;
      if (!checkNewPassword) return 1;
      if (/*checkOldPassword && */ checkNewPassword) {
        let newPassword = await Bcryptjs.hash(
          req.body.new_password,
          generatedSalt
        );
        await Admin.update(
          { password: newPassword, },
            {
              where: {
                admin_id: find_admin.dataValues.admin_id,
              }
            },
        //   {
        //   password: newPassword,
        // }
      );
        return 2;
      }
    }
  },

  /* edit profile */
  async updateProfile(req) {
    console.log("updateProfile -> req", req.file);
    /* updating profile firstname and lastname */
    //finding admin
    let admin = await Admin.findOne({
      where: {
        admin_id: req.body.admin_id,
      },
    });

    // if (admin.dataValues && req.file) {
    //   if (admin.dataValues.photo) {
    //     await gcpDelete(
    //       constant.GCP_BUCKET_FOLDER +
    //       constant.GCP_ADMIN_FOLDER +
    //       admin.admin_id +
    //       "/" +
    //       admin.dataValues.photo
    //     );
    //   }

    //   //profile img
    //   await gcpUpload(
    //     req.file,
    //     constant.GCP_BUCKET_FOLDER +
    //     constant.GCP_ADMIN_FOLDER +
    //     admin.admin_id +
    //     "/" +
    //     // constant.admin_id +
    //     // admin.admin_id +
    //     // "_" +
    //     req.file.originalname
    //   );

    //   // update profile img
    //   await Admin.update(
    //     {
    //       // photo: admin.admin_id + "_" + req.file.originalname,
    //       photo: req.file.originalname,
    //     },
    //     { where: { admin_id: admin.admin_id } }
    //     );
    // }

    await Admin.update(
      {
        firstName: req.body.first_name,
        lastName: req.body.last_name,
        // photo: req.file.originalname,
      },
      { where: { admin_id: req.body.admin_id } }
    );
    var edit_res = await Admin.findOne(
      // { attributes: ["admin_id", "firstName", "lastName", "email", "photo"] },
      { where: { admin_id: req.body.admin_id } }
    );
    // edit_res.dataValues.photo =
    //   constant.GCP_URL +
    //   constant.GCP_BUCKET_NAME +
    //   "/" +
    //   constant.GCP_BUCKET_FOLDER +
    //   constant.GCP_ADMIN_FOLDER +
    //   edit_res.dataValues.admin_id +
    //   "/" +
    //   edit_res.dataValues.photo;
    // edit_res.dataValues.photo = await gcpUtils.getPrivateUrl(
    //   constant.GCP_BUCKET_NAME,
    //   constant.GCP_BUCKET_FOLDER +
    //   constant.GCP_ADMIN_FOLDER +
    //   edit_res.dataValues.admin_id +
    //   "/" +
    //   edit_res.dataValues.photo
    // );
    edit_res.dataValues.photo = null;
    let find_admin_rolelist = await AssignRolelist.findAll({
       where: { admin_id: req.body.admin_id },
    });
    edit_res.dataValues.admin_assignrole = find_admin_rolelist;
    return edit_res;
  },

  /* change password */
  async changePassword(req, res) {
    //decrypt and compare password in database
    let admin_find = await Admin.findOne({
      where: {
        // admin_id: req.authId,
        admin_id: req.body.admin_id,
      },
    });
    if (admin_find) {
      let result = Bcryptjs.compareSync(
        req.body.old_password,
        admin_find.password
      );
      if (result) {
        //if it is true then take new password decrypt and update in DB
        let new_password = await Bcryptjs.hash(
          req.body.new_password,
          generatedSalt
        );
        return await Admin.update(
          {
            password: new_password,
          },
          {
            where: {
              admin_id: admin_find.admin_id,
            },
          }
        );
      }
    }
  },
}
