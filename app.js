/**
 * KyuleBag API - Main Application Entry Point
 *
 * This file serves as the central entry point for the KyuleBag digital content management platform.
 * It initializes the Express.js server, configures middleware stack, sets up real-time Socket.io
 * communication, and establishes the routing system for both client and admin APIs.
 *
 * Business Logic Connection:
 * - Enables dual API structure serving mobile clients (/app-api) and admin panel (/cms-api)
 * - Provides real-time features for live notifications and collaborative content management
 * - Handles large file uploads (50MB limit) for multimedia content storage
 * - Supports both HTTP and HTTPS protocols based on environment configuration
 *
 * Key Features:
 * - Express.js web server with comprehensive middleware stack
 * - Socket.io integration for real-time bidirectional communication
 * - CORS support for cross-origin requests from mobile and web clients
 * - Large payload support for multimedia content uploads
 * - Environment-based HTTP/HTTPS configuration
 *
 * Default Behaviors:
 * - Runs on HTTP by default (configurable via SHOULD_RUN_ON_HTTP environment variable)
 * - Body parser limit set to 50MB for large file uploads
 * - CORS enabled for all origins
 * - Socket.io connection logging enabled for debugging
 * - Server port configured via APP_PORT environment variable
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

// Load environment variables from .env file - must be first import
require('dotenv').config();

// Core Node.js and Express imports
const express = require('express');
const body_parser = require('body-parser');
const logger = require('morgan');
const cors = require('cors');
const fs = require('fs');

// Internal modules
const index_router = require('./routes');

// HTTP/HTTPS server configuration based on environment
// Allows switching between HTTP (development) and HTTPS (production) protocols
const SHOULD_RUN_ON_HTTP = process.env.SHOULD_RUN_ON_HTTP;
const http = SHOULD_RUN_ON_HTTP == 'true' ? require('http') : require('https');

// Initialize Express application instance
const app = express();

// Load global configuration settings
// This makes STATUS_CODES available globally throughout the application
require('./config/global'); // GLOBAL SETTINGS FILES

// Create HTTP/HTTPS server instance
// Conditionally creates HTTP server for development or HTTPS server for production
// HTTPS server requires SSL certificate options (not shown here for security)
const server =
  SHOULD_RUN_ON_HTTP == 'true'
    ? http.createServer(app)
    : http.createServer(options, app);

// Initialize Socket.io for real-time communication
// Enables features like live notifications, real-time collaboration, and instant updates
const io = require('socket.io')(server);

// Socket.io connection handler
// Manages real-time WebSocket connections for live features
io.on('connection', () => {
  // Handle new client connections
  /* Real-time event handlers would be implemented here for:
   * - Live notifications
   * - Collaborative editing
   * - File upload progress
   * - User presence tracking
   */
  console.log('user connected');
});

// Get server port from environment configuration
const port = process.env.APP_PORT;

/**
 * MIDDLEWARE CONFIGURATION
 * Setting up the Express.js middleware stack in proper order
 */

// HTTP request logging middleware
// Logs all incoming requests in 'dev' format for debugging and monitoring
app.use(logger('dev'));

// CORS (Cross-Origin Resource Sharing) middleware
// Allows requests from mobile apps and web clients from different domains
app.use(cors());

// Body parsing middleware configuration
// Handles JSON and URL-encoded request bodies with large payload support
// 50MB limit accommodates large file uploads and multimedia content

// JSON body parser with 50MB limit for API requests
app.use(body_parser.json({ limit: '50mb' }));

// URL-encoded body parser with 50MB limit for form submissions
app.use(body_parser.urlencoded({ limit: '50mb', extended: true }));

/**
 * ROUTING CONFIGURATION
 * Initialize the main routing system that handles both client and admin APIs
 *
 * Routes are organized as:
 * - /app-api/* - Mobile/Client application endpoints
 * - /cms-api/* - Content Management System (Admin) endpoints
 */
index_router(app);

/**
 * SERVER INITIALIZATION
 * Start the HTTP/HTTPS server and begin listening for incoming requests
 */
server.listen(port, () => {
  console.log(`🚀 KyuleBag API Server running at port ${port}`);
  console.log(
    `📡 Protocol: ${SHOULD_RUN_ON_HTTP == 'true' ? 'HTTP' : 'HTTPS'}`
  );
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Export the Express app instance for testing purposes
module.exports = app;
