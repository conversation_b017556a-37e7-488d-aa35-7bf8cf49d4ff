/**
 * Item Privacy Service - Content Privacy Management
 *
 * This service handles privacy-related operations for content items in the KyuleBag platform.
 * It manages private content access, visibility controls, and privacy state changes.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

const Items = require('../../database/models').tbl_items;
const gcpUpload = require('../../middleware/multer_gcp_upload');
const gcpDelete = require('../../middleware/multer_gcp_delete');

module.exports = {
  /* add */
  async item_is_private_edit(req, res) {
    let { item_id, item_is_private } = req.body;
    let items = await Items.findOne({
      where: {
        item_id: item_id,
      },
    });
    if (items) {
      await Items.update(
        {
          is_private: item_is_private,
        },
        {
          where: {
            item_id: item_id,
            is_deleted:0

          },
        }
      );
      var private_item = await Items.findOne({
        where: {
          item_id: item_id,
          is_deleted:0

        },
        attributes: {
          include: ["createdAt"],
        },
      });
      // var item_activity_log = await Activity_logs.create({
      //   user_id :req.user_id,
      //   title:"Private Item",
      //   // description:`Private Item set to ${private_item.dataValues.is_bookmarked} from ${items.is_private} of item ${private_item.item_id}.`
      //   description:`Private Item set to ${private_item.dataValues.is_bookmarked} from ${items.is_private} of item.`
      // });
      private_item.dataValues.createdAt =private_item.dataValues.createdAt.toISOString()
      .substr(0, 19)
      .replace("T", " ");
      return private_item;
    }
  },
};
