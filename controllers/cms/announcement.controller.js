const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const announcement_service = require("../../services/cms/announcement.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async addAnnouncement(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let add_announcement_res = await announcement_service.addAnnouncement(
        req,
        res
      );
      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: add_announcement_res,
        message: message.SENDANNOUNCEMENT,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("addAnnouncement -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async deleteAnnouncement(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let announcement = await announcement_service.deleteAnnouncement(req, res);
      if (announcement) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ANNOUNCEMENTDELETED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ANNOUNCEMENTLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async editCommentSettings(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await announcement_service.editCommentSettings(req, res);

      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: [],
        message: message.SENDANNOUNCEMENT,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("editCommentSettings -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async listAnnouncement(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_announcement_res = await announcement_service.listAnnouncement(
        req,
        res
      );
      if (list_announcement_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_announcement_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ANNOUNCEMENTLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listAnnouncement -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async allListAnnouncement(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_announcement_res = await announcement_service.allListAnnouncement(
        req,
        res
      );
      if (list_announcement_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_announcement_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ANNOUNCEMENTLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listAnnouncement -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async resendAnnouncement(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let announcement_res = await announcement_service.resendAnnouncement(
        req,
        res
      );
      if (!announcement_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: announcement_res,
          message: message.ANNOUNCEMENTLISTNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: announcement_res,
          message: message.ANNOUNCEMENTLIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("resendAnnouncement -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
