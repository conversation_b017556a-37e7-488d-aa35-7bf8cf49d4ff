const { check, query, param, checkIf } = require("express-validator");
const falsify = { checkFalsy: true };
module.exports = {
  app: {
    users: {
      signin: [
        check("email")
          // .exists()
          // .withMessage("Please enter email.")
          .optional(true)
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),
        check("password")
          .exists()
          .withMessage("Password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Password field cannot be blank."),
        // .isLength({ min: 8 })
        // .withMessage("Password field must be atleast 8 characters long.")
        // .matches(
        //   /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/,
        //   "i"
        // )
        // .withMessage(
        //   "Password must include one lowercase character, one uppercase character, a number, and a special character."
        // ),

        check("device_type")
          .exists()
          .withMessage("Please enter device type.")
          .isIn(["android", "ios"])
          .withMessage("Device type is not valid."),

        check("version")
          .exists()
          .withMessage("Version field is required.")
          .trim()
          .notEmpty()
          .withMessage("Version field cannot be blank."),

        check("device_token")
          .optional(true)
          .isLength({ max: 255 })
          .withMessage(
            "Device token field cannot be more than 255 characters."
          ),

        check("login_type")
          .exists()
          .withMessage("Please enter login type.")
          .isIn(["KyuleBag", "Google", "FaceBook"])
          .withMessage("Login type is not valid."),

        check("country_code")
          .optional(true)
          .isLength({ max: 15 })
          .withMessage("Country Code field cannot be more than 15 characters."),

        check("phone")
          .optional(true)
          .isLength({ max: 12 })
          .withMessage("Phone field cannot be more than 12 characters."),
      ],

      signup: [
        check("name")
          .exists()
          .withMessage("Please enter name.")
          .isLength({ max: 255 })
          .withMessage("Name field cannot be more than 255 characters."),
        check("email")
          .exists()
          .withMessage("Please enter email.")
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),
        check("password")
          // .exists()
          // .withMessage("Password field cannot be blank.")
          // .trim()
          .optional(true)
          .isLength({ min: 8 })
          .withMessage("Password field must be atleast 8 characters long.")
          .matches(
            /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/,
            "i"
          )
          .withMessage(
            "Password must include one lowercase character, one uppercase character, a number, and a special character."
          ),

        check("device_type")
          .exists()
          .withMessage("Please enter device type.")
          .isIn(["android", "ios"])
          .withMessage("Device type is not valid."),

        check("version")
          .exists()
          .withMessage("Version field is required.")
          .trim()
          .notEmpty()
          .withMessage("Version field cannot be blank."),

        check("device_token")
          .exists()
          .withMessage("Device Token field is required.")
          .isLength({ max: 255 })
          .withMessage(
            "Device Token field cannot be more than 255 characters."
          ),

        check("login_type")
          .exists()
          .withMessage("Please enter login type.")
          .isIn(["KyuleBag", "Google", "FaceBook"])
          .withMessage("Login type is not valid."),

        check("country_code")
          // .exists()
          // .withMessage("Country code require.")
          .optional(true)
          .isLength({ max: 3 })
          .withMessage("Country code field cannot be more than 3 characters."),

        check("phone")
          // .exists()
          // .withMessage("phone number require.")
          .optional(true)
          .isLength({ max: 12 })
          .withMessage("Phone field cannot be more than 12 characters."),

        check("facebook_id")
          .optional(true)
          .isLength({ max: 255 })
          .withMessage("facebook_id field cannot be more than 255 characters."),

        check("google_id")
          .optional(true)
          .isLength({ max: 255 })
          .withMessage("google_id field cannot be more than 255 characters."),
      ],

      verify_otp: [
        check("otp")
          .exists()
          .withMessage("OTP field cannot be blank.")
          .trim()
          .isLength({ min: 6, max: 6 })
          .withMessage("OTP field must be 6 characters long.")
          .isInt()
          .withMessage("OTP field only accept numeric value!"),
      ],

      forgot_password: [
        check("email")
          // .exists()
          // .withMessage("Please enter email.")
          .optional(true)
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),

        check("phone")
          .optional(true)
          .isLength({ max: 12 })
          .withMessage("Phone field cannot be more than 12 characters."),
      ],

      verify_code: [
        check("userId")
          .exists()
          .withMessage("userId field is require.")
          .trim()
          .notEmpty()
          .withMessage("userId field cannot be blank."),
        check("verifyCode")
          .exists()
          .withMessage("verifyCode field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("verifyCode field cannot be blank."),
      ],

      check_user: [
        check("email")
          .exists()
          .withMessage("Please enter email.")
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),
        check("googleId")
          .trim()
          .notEmpty()
          .isString()
          .withMessage("Please Provide valid googleId!"),
      ],  

      reset_password: [
        check("otp")
          .exists()
          .withMessage("OTP field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("OTP field cannot be blank."),
          // .isInt()
          // .withMessage("Verification code only accept numeric value."),
        check("new_password")
          .exists()
          .withMessage("New password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("New password field cannot be blank."),
        check("confirm_password")
          .exists()
          .withMessage("Confirm password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Confirm password field cannot be blank."),
      ],

      verify_email: [
        param("code")
          .exists()
          .withMessage("Verification code field is required.")
          .notEmpty()
          .withMessage("Verification code field cannot be blank.")
          .isInt()
          .withMessage("Verification code only accept numeric value."),
      ],

      edit_profile: [
        check("name")
          .exists()
          .withMessage("Please enter name.")
          .isLength({ max: 255 })
          .withMessage("Name field cannot be more than 255 characters."),
      ],

      change_password: [
        check("old_password")
          .exists()
          .withMessage("Old Password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Old Password field cannot be blank."),
        check("new_password")
          .exists()
          .withMessage("New password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("New password field cannot be blank."),
        check("confirm_password")
          .exists()
          .withMessage("Confirm password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Confirm password field cannot be blank."),
      ],

      private_note_password: [
        check("private_note_password")
          .exists()
          .withMessage("Private Note Password field is required.")
          .trim()
          .notEmpty()
          .withMessage("Private Note Password field cannot be blank."),
      ],
      change_private_note_password: [
        check("old_private_note_password")
          .exists()
          .withMessage("Old Private Note Password field is required.")
          .trim()
          .notEmpty()
          .withMessage("Old Private Note Password field cannot be blank."),
        check("new_private_note_password")
          .exists()
          .withMessage("New Private Note Password field is required.")
          .trim()
          .notEmpty()
          .withMessage("New Private Note Password field cannot be blank."),
        check("confirm_private_note_password")
          .exists()
          .withMessage("Confirm Private Note Password field is required.")
          .trim()
          .notEmpty()
          .withMessage("Confirm Private Note Password field cannot be blank."),
      ],
      forgot_private_note_password: [
        check("email")
          // .exists()
          // .withMessage("Please enter email.")
          .optional(true)
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),
        check("phone")
          .optional(true)
          .isLength({ max: 12 })
          .withMessage("Phone field cannot be more than 12 characters."),
      ],
      reset_private_note_password: [
        check("otp")
          .exists()
          .withMessage("OTP field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("OTP field cannot be blank.")
          .isInt()
          .withMessage("Verification code only accept numeric value."),
        check("new_password")
          .exists()
          .withMessage("New password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("New password field cannot be blank."),
        check("confirm_password")
          .exists()
          .withMessage("Confirm password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Confirm password field cannot be blank."),
      ],
      subscription: [
        check("subscriptionId")
          .exists()
          .withMessage("subscriptionId field is require.")
          .trim()
          .notEmpty()
          .withMessage("subscriptionId field cannot be blank."),
        check("subscriptionType")
          .exists()
          .withMessage(`subscriptionType field is require, value should be in ${Object.values(["month", "year"]).join(", ")}`)
          .isIn(["month", "year"])
          .withMessage(`subscriptionType value should be in ${Object.values(["month", "year"]).join(", ")}`),
        check("orderId")
          .exists()
          .withMessage("orderId field is require.")
          .trim()
          .notEmpty()
          .withMessage("orderId field cannot be blank."),
        check("subscriptionAmount")
          .exists()
          .withMessage("subscriptionAmount field is require.")
          .trim()
          .notEmpty()
          .withMessage("subscriptionAmount field cannot be blank."),
        check("purchaseToken")
          .exists()
          .withMessage("purchaseToken field is require.")
          .trim()
          .notEmpty()
          .withMessage("purchaseToken field cannot be blank."),
        check("purchaseTime")
          .exists()
          .withMessage("purchaseTime field is require.")
          .trim()
          .notEmpty()
          .withMessage("purchaseTime field cannot be blank."),
        check("purchaseStatus")
          .exists()
          .withMessage(`purchaseStatus field is require, value should be in ${Object.values(["completed", "pending"]).join(", ")}`)
          .isIn(["completed", "pending"])
          .withMessage(`purchaseStatus value should be in ${Object.values(["completed", "pending"]).join(", ")}`),
      ],
      update_subscription: [
        check("transactionId")
          .exists()
          .trim()
          .withMessage("transactionId field is require."),
        check("purchaseToken")
          .exists()
          .trim()
          .withMessage("purchaseToken field is require."),
        check("isCancel")
          .exists()
          .withMessage("isCancel field is require.")
          .trim()
          .notEmpty()
          .withMessage("isCancel field cannot be blank.")
          .isBoolean()
          .withMessage("item_bookmarked only accept boolean value."),
      ],
      ai_subscription: [
        check("aiSubscriptionId")
          .exists()
          .withMessage("aiSubscriptionId field is require.")
          .trim()
          .notEmpty()
          .withMessage("aiSubscriptionId field cannot be blank."),
        check("orderId")
          .exists()
          .withMessage("orderId field is require.")
          .trim()
          .notEmpty()
          .withMessage("orderId field cannot be blank."),
        check("purchaseToken")
          .exists()
          .trim()
          .withMessage("purchaseToken field is require."),
        check("purchaseStatus")
          .exists()
          .withMessage(`purchaseStatus field is require, value should be in ${Object.values(["completed", "pending"]).join(", ")}`)
          .isIn(["completed", "pending"])
          .withMessage(`purchaseStatus value should be in ${Object.values(["completed", "pending"]).join(", ")}`),

      ],
      cancel_subscription: [
        check("subscriptionId")
          .exists()
          .withMessage("subscriptionId field is require.")
          .trim()
          .notEmpty()
          .withMessage("subscriptionId field cannot be blank."),
        check("orderId")
          .exists()
          .withMessage("orderId field is require.")
          .trim()
          .notEmpty()
          .withMessage("orderId field cannot be blank.")
      ],
      alternative_email: [
        // check("alternative_email")
        //   .trim()
        //   .notEmpty()
        //   .withMessage("Email field cannot be blank.")
        //   .normalizeEmail()
        //   .isEmail()
        //   .withMessage("Please enter valid email."),
        check("country_code")
          .optional(true)
          .isLength({ max: 15 })
          .withMessage("Country Code field cannot be more than 15 characters."),
        check("alternative_phone")
          .optional(true)
          .isLength({ max: 10 })
          .withMessage("Phone field cannot be more than 10 characters."),
      ],
      delete_alternative: [
        check("type")
          .exists()
          .withMessage("type field is required!")
          .notEmpty()
          .withMessage("type field cannot be blank, type value should be in EMAIL or PHONE.")
          .isIn(["EMAIL", "PHONE"])
          .withMessage("type value should be in EMAIL or PHONE."),
      ],
    },
    notes: {
      add: [
        check("note_description")
          .exists()
          .withMessage("Please enter note name.")
          .trim()
          .notEmpty()
          .withMessage("Note name field cannot be blank."),
        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank.")
          .isInt()
          .withMessage("Item id only accept numeric value."),
      ],
      edit: [
        check("note_description")
          .exists()
          .withMessage("Please enter note name.")
          .trim()
          .notEmpty()
          .withMessage("Note name field cannot be blank."),

        check("note_id")
          .exists()
          .withMessage("Note id field is required.")
          .notEmpty()
          .withMessage("Note id field cannot be blank.")
          .isInt()
          .withMessage("Note id only accept numeric value."),

        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank.")
          .isInt()
          .withMessage("Item id only accept numeric value."),
      ],

      delete: [
        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank.")
          .isInt()
          .withMessage("Item id only accept numeric value."),

        check("note_id")
          .exists()
          .withMessage("Note id field is required.")
          .notEmpty()
          .withMessage("Note id field cannot be blank.")
          .isInt()
          .withMessage("Note id only accept numeric value."),
      ],
    },
    item_bookmark: {
      edit: [
        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank."),
        // .isBoolean()
        // .withMessage("Item id only accept numeric value."),

        check("item_bookmarked")
          .exists()
          .withMessage("item_bookmarked field is required.")
          .notEmpty()
          .withMessage("item_bookmarked field cannot be blank.")
          .isBoolean()
          .withMessage("item_bookmarked only accept boolean value."),
      ],
    },
    item_is_private: {
      edit: [
        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank."),
        // .isBoolean()
        // .withMessage("Item id only accept numeric value."),

        check("item_is_private")
          .exists()
          .withMessage("is_private field is required.")
          .notEmpty()
          .withMessage("is_private field cannot be blank.")
          .isBoolean()
          .withMessage("is_private only accept boolean value."),
      ],
    },
    item_tags: {
      add: [
        check("tag_name_id")
          .exists()
          .withMessage("Tag id field is required.")
          .notEmpty()
          .withMessage("Tag id field cannot be blank."),
        // .isInt()
        // .withMessage("Tag id only accept numeric value."),
        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank."),
        // .isInt()
        // .withMessage("Item id only accept numeric value."),
      ],
    },
    tags: {
      add: [
        check("tag_name")
          .exists()
          .withMessage("Please enter tag name.")
          .trim()
          .notEmpty()
          .withMessage("Tag name field cannot be blank.")
          .isLength({ max: 255 })
          .withMessage("Tag name field cannot be more than 255 characters."),
      ],

      edit: [
        check("tag_name")
          .exists()
          .withMessage("Please enter tag name.")
          .trim()
          .notEmpty()
          .withMessage("Tag name field cannot be blank.")
          .isLength({ max: 255 })
          .withMessage("Tag name field cannot be more than 255 characters."),

        check("tag_name_id")
          .exists()
          .withMessage("Tag id field is required.")
          .notEmpty()
          .withMessage("Tag id field cannot be blank.")
          .isInt()
          .withMessage("Tag id only accept numeric value."),
      ],

      delete: [
        check("tag_name_id")
          .exists()
          .withMessage("Tag id field is required.")
          .notEmpty()
          .withMessage("Tag id field cannot be blank.")
          .isInt()
          .withMessage("Tag id only accept numeric value."),
      ],
    },
    items: {
      add: [
        check("media_type")
          .exists()
          .withMessage("Please add media_type.")
          .trim()
          .notEmpty()
          .withMessage("media_type field cannot be blank."),
      ],
      list: [
        check("page")
          .optional(true)
          .isInt()
          .withMessage("Page parameter only accept numeric value!"),
        check("order")
          .exists()
          .withMessage("order should contain valid value")
          .isIn(["ASC", "DESC"])
          .withMessage("order can contain only ASC or DESC"),
      ],
      delete: [
        // check("item_id")
        //   .exists()
        //   .withMessage("Item id field is required.")
        //   .notEmpty()
        //   .withMessage("Item id field cannot be blank.")
        // .isInt()
        // .withMessage("Item id only accept numeric value."),
      ],
      // delete AI label
      deleteAILabel: [
        check("id")
          .exists()
          .withMessage("id field is required.")
          .notEmpty()
          .withMessage("id field cannot be blank.")
          .isInt()
          .withMessage("id only accept numeric value."),
        check("name")
          .exists()
          .withMessage("Please add name.")
          .trim()
          .notEmpty()
          .withMessage("name field cannot be blank."),
      ],
      // reset AI label
      resetAILabel: [
        check("id")
          .exists()
          .withMessage("id field is required.")
          .notEmpty()
          .withMessage("id field cannot be blank.")
          .isInt()
          .withMessage("id only accept numeric value."),
      ],
      //reobserve AI
      reobserveAILabel: [
        check("mediaId")
          .exists()
          .withMessage("mediaId field is required.")
          .notEmpty()
          .withMessage("mediaId field cannot be blank.")
          .isInt()
          .withMessage("mediaId only accept numeric value."),
      ],
      // delete OCR label
      deleteOCRLabel: [
        check("id")
          .exists()
          .withMessage("id field is required.")
          .notEmpty()
          .withMessage("id field cannot be blank.")
          .isInt()
          .withMessage("id only accept numeric value."),
        check("name")
          .exists()
          .withMessage("Please add name.")
          .trim()
          .notEmpty()
          .withMessage("name field cannot be blank."),
      ],
      //reobserve OCR
      reobserveOCRLabel: [
        check("mediaId")
          .exists()
          .withMessage("mediaId field is required.")
          .notEmpty()
          .withMessage("mediaId field cannot be blank.")
          .isInt()
          .withMessage("mediaId only accept numeric value."),
      ],
      edit: [
        check("media_type")
          .exists()
          .withMessage("Please add media_type.")
          .trim()
          .notEmpty()
          .withMessage("media_type field cannot be blank."),

        check("item_id")
          .exists()
          .withMessage("Item id field is required.")
          .notEmpty()
          .withMessage("Item id field cannot be blank.")
          .isInt()
          .withMessage("Item id only accept numeric value."),
      ],
    },
    contactUs: {
      add: [
        check("name")
          .exists()
          .withMessage("Please enter name."),
        check("email")
          .exists()
          .withMessage("Please enter email."),
        check("message")
          .exists()
          .withMessage("Please enter message."),
      ]
    },
    comment: {
      addUpdate:[
        check("content")
          .trim()
          .notEmpty()
          .withMessage("Please enter content."),
      ]
    }
  },

  cms: {
    admin: {
      login: [
        check("email")
          .exists()
          .withMessage("Please enter email.")
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),
        check("password").exists().withMessage("Please field cannot be blank."),
      ],
      forgot_password: [
        check("email")
          .exists()
          .withMessage("Please enter email.")
          .trim()
          .normalizeEmail()
          .isEmail()
          .withMessage("Please enter valid email."),
      ],
      reset_password: [
        // check("old_password")
        //   .exists()
        //   .withMessage("Please enter old password")
        //   .notEmpty()
        //   .withMessage("old password field cannot be blank."),
        check("new_password")
          .exists()
          .withMessage("Please enter new_password.")
          .notEmpty()
          .withMessage("new_password field cannot be blank.")
          .trim()
          .isLength({ min: 8 })
          .withMessage("new_password field must be atleast 8 characters long.")
          .matches(
            /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/,
            "i"
          )
          .withMessage(
            "new_password must include one lowercase character, one uppercase character, a number, and a special character."
          ),
        check("confirm_password")
          .exists()
          .withMessage("Please enter confirm_password")
          .notEmpty()
          .withMessage("confirm_password field cannot be blank."),
      ],
      edit_profile: [
        check("first_name")
          .exists()
          .withMessage("Please enter firstname")
          .notEmpty()
          .withMessage("name field cannot be blank."),
        check("last_name")
          .exists()
          .withMessage("Please enter lastname")
          .notEmpty()
          .withMessage("name field cannot be blank."),
      ],
      change_password: [
        check("old_password")
          .exists()
          .withMessage("Old Password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Old Password field cannot be blank."),
        check("new_password")
          .exists()
          .withMessage("New password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("New password field cannot be blank."),
        check("confirm_password")
          .exists()
          .withMessage("Confirm password field cannot be blank.")
          .trim()
          .notEmpty()
          .withMessage("Confirm password field cannot be blank."),
      ],
    },
    users: {
      user_list: [
        check("page")
          .optional(falsify)
          .isInt()
          .withMessage("Page parameter only accept numeric value!"),
        check("order")
          .exists()
          .withMessage("order should contain valid value")
          .isIn(["ASC", "DESC"])
          .withMessage("order can contain only ASC or DESC"),
        check("user_type")
          .isIn(["general", "other"])
          .withMessage("user_type can contain only general or other"),
      ],
    },
    staticContent: {
      listStaticPage: [
        check("order")
          .optional(falsify)
          .isIn(["ASC", "DESC"])
          .withMessage("order can contain only ASC or DESC"),
        check("page")
          .optional(falsify)
          .isInt()
          .withMessage("Page parameter only accept numeric value!"),
      ],
      addStaticPage: [
        check("title")
          .exists()
          .withMessage("please enter title of the static page")
          .trim()
          .notEmpty()
          .withMessage("title cannot be blank"),
        check("content")
          .exists()
          .withMessage("please enter content of the static page")
          .trim()
          .notEmpty()
          .withMessage("content cannot be blank"),
        check("type")
          .optional(falsify)
          .isIn(["web", "app"])
          .withMessage("type can contain only web or app"),
      ],
      updateStaticPage: [
        check("title")
          .exists()
          .withMessage("please enter title of the static page")
          .trim()
          .notEmpty()
          .withMessage("title cannot be blank"),
        check("content")
          .exists()
          .withMessage("please enter content of the static page")
          .trim()
          .notEmpty()
          .withMessage("content cannot be blank"),
        check("type")
          .optional(falsify)
          .isIn(["web", "app"])
          .withMessage("type can contain only web or app"),
      ],
    },
    notification: {
      addNotification: [
        check("admin_id")
          .exists()
          .withMessage("please enter admin of the notification")
          .trim()
          .notEmpty()
          .withMessage("admin is require"),
        check("subject")
          .exists()
          .withMessage("please enter subject of the notification")
          .trim()
          .notEmpty()
          .withMessage("subject cannot be blank"),
        check("description")
          .exists()
          .withMessage("please enter description of the notification")
          .trim()
          .notEmpty()
          .withMessage("description cannot be blank"),
      ],
    },
    announcement: {
      addAnnouncement: [
        check("admin_id")
          .exists()
          .withMessage("please enter admin of the announcement")
          .trim()
          .notEmpty()
          .withMessage("admin is require"),
        check("content")
          .exists()
          .withMessage("please enter content of the announcement")
          .trim()
          .notEmpty()
          .withMessage("content cannot be blank"),
      ],
    },
  },
};
