/*
 * Summary:     tags.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const item_tags_services = require("../../services/app/item.tags.services");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* add */
  async add(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let item_tags = await item_tags_services.item_tag_add(req, res);
      //   let items = await items_service.add(req, res);
      if (item_tags.tagType) {
        const mess = item_tags.tagType === 1 ? message.TAGUPDATE : item_tags.tagType === 2 ? message.TAGADDED : message.TAGSADDED;
        res.status(status.SUCCESSSTATUS).send({
          data: item_tags.item,
          message: mess,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.TAGSALREADY,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.errorStatus).send({
        data: [],
        message: message.TAGSIDITEMIDNOTFOUND,
        status: status.ERROR,
      });
    }
  },
};
