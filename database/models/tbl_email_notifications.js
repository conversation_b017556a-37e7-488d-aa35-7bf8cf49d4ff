'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_email_notifications extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_email_notifications.init({
    email_notification_id:{
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    admin_id: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    subject: DataTypes.TEXT,
    description: DataTypes.TEXT,
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_email_notifications',
  });
  return tbl_email_notifications;
};