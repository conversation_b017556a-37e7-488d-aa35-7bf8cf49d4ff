const message = require("../../config/cms.message").cmsMessage;
const appMessage = require("../../config/web.message").appMessage;
const status = require("../../config/status").status;
const announcement_service = require("../../services/app/announcement.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async listAnnouncement(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_announcement_res = await announcement_service.listAnnouncement(
        req,
        res
      );
      if (list_announcement_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_announcement_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ANNOUNCEMENTLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listAnnouncement -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // To add comment
  async addUserComment(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      const validate_comment_res =
        await announcement_service.validateWithCommentSetting(req, "add");
      if (validate_comment_res.isValid === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: appMessage.COMMENTLENGTHEXCEED,
          status: status.ERROR,
        });
      } else if (validate_comment_res.isValid === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: appMessage.COMMENTISDISABLE,
          status: status.ERROR,
        });
      } else if (validate_comment_res.isValid === 4) {
        const comment = await announcement_service.addComment(req);
        if (comment) {
          res.status(status.SUCCESSSTATUS).send({
            data: comment,
            message: appMessage.COMMENTADDED,
            status: status.SUCCESS,
          });
        } else {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: appMessage.COMMENTNOTADDED,
            status: status.ERROR,
          });
        }
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: appMessage.COMMENTNOTADDED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: appMessage.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // To edit comment
  async editUserComment(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      const validate_comment_res =
        await announcement_service.validateWithCommentSetting(req, "edit");
      if (validate_comment_res.isValid === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: appMessage.COMMENTLENGTHEXCEED,
          status: status.ERROR,
        });
      } else if (validate_comment_res.isValid === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: appMessage.COMMENTISDISABLE,
          status: status.ERROR,
        });
      } else if (validate_comment_res.isValid === 4) {
        const updatedComment = await announcement_service.editComment(req);
        if (updatedComment) {
          res.status(status.SUCCESSSTATUS).send({
            data: updatedComment,
            message: appMessage.COMMENTUPDATED,
            status: status.SUCCESS,
          });
        } else {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: appMessage.COMMENTNOTUPDATED,
            status: status.ERROR,
          });
        }
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: appMessage.COMMENTNOTUPDATED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: appMessage.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
