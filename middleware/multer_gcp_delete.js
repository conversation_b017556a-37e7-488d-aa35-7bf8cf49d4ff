
const { Storage } = require('@google-cloud/storage');
const CONSTANT = require("../config/constant");

const storage = new Storage({
    projectId: CONSTANT.GCP_PROJECT_ID,
    credentials: require("../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json")
});

const bucketName = CONSTANT.GCP_BUCKET_NAME;
const bucket = storage.bucket(bucketName);

function gcpDelete(path) {
    return new Promise((resolve, reject) => {
        bucket.file(path).delete(function (err, data) {
            if (err) {
                console.log("err", err);
                reject({ message: err.message, err: err })
            } else {
                console.log("Successfully deleted media on Google Cloud Storage ");
                resolve();
            }
        })
    })

}

module.exports = gcpDelete;