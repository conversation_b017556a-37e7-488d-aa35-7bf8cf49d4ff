/**
 * CMS Dashboard Service - Analytics and Statistics
 *
 * This service handles dashboard statistics and analytics for the KyuleBag CMS admin panel.
 * It provides user counts, content statistics, and system metrics for administrative oversight.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

const User = require('../../database/models').tbl_users;
const Item = require('../../database/models').tbl_items;
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const constant = require('../../config/constant');

module.exports = {
  async dashboard(req, res) {
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;
    let data = {};
    const where = {
      is_deleted: '0',
    };

    if (req.body.is_verified === '0') {
      where[Op.and] = [
        {
          [Op.or]: [{ is_verified: '0' }, { email_verified: '0' }],
        },
      ];
    } else if (req.body.is_verified === '1') {
      where['is_verified'] = '1';
      where['email_verified'] = '1';
    }

    if (req.body.status !== 'ALL') {
      where['status'] = 'active';
    }

    let user = await User.findAndCountAll({
      where: where,
      attributes: {
        exclude: ['password'],
      },
    });

    let user_items = await Item.count({
      where: {
        delete_type: 'NORMAL',
        is_deleted: '0',
      },
    });

    let deleted_user_items = await Item.count({
      where: {
        delete_type: 'NORMAL',
        is_deleted: '1',
      },
    });

    data.users = user.count;
    data.user_items = user_items;
    data.deleted_user_items = deleted_user_items;
    data.userList = user.rows;

    return data;
  },
};
