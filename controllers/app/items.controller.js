/*
 * Summary:     tags.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const item_service = require("../../services/app/items.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* add */
  async add(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await item_service.newAdd(req, res);
      console.log("🚀 ~ file: items.controller.js:28 ~ add ~ items:", items.dataValues)
      if (items === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERSTORAGEFULL,
          status: status.ERROR,
        });
      } else if (items) {
        res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMADDED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ITEMALREDYADDED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        item_id: req.body.id,
        message: error.message,
        status: status.ERROR,
      });
    }
  },

  async bulkAdd(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await item_service.bulkAdd(req, res);
      if (items === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERSTORAGEFULL,
          status: status.ERROR,
        });
      } else if (items === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMADDED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ITEMALREDYADDED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        item_id: req.body.id,
        message: error.message,
        status: status.ERROR,
      });
    }
  },

  /* uploadMedia into cloud */
  async uploadMedia(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await item_service.uploadMedia(req, res);
      if (items) {
        res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEM_MEDIA_UPLOADED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ITEM_MEDIA_NOT_UPLOADED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        item_id: req.body.id,
        message: error.message,
        status: status.ERROR,
      });
    }
  },

  /* list */
  async list(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await item_service.list(req, res);
      if (items) {
        res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMRETRIVED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: req.body.search
            ? message.NORESULTFOUND
            : message.NOITEMFOUND,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* delete */
  async delete(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await item_service.delete(req, res);
      if (items) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message:
            req.body.delete_type.toLowerCase() === "cloud"
              ? message.DELETEFROMCLOUDORMOBILE
              : message.DELETEFROMMOBILE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOITEMFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* edit */
  async edit(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await item_service.edit(req, res);
      if (items) {
        res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMEDITED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOITEMFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * delete AI label
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async deleteAILabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      let items = await item_service.deleteAILabel(req, res);
      if (items) {
        return res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMAILABELDELETED,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * reset AI label
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async resetAILabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      let items = await item_service.resetAILabel(req, res);
      if (items) {
        return res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMAILABELRESET,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * reobserve AI
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async reobserveAILabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      let items = await item_service.reobserveAILabel(req, res);
      if (items) {
        return res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMAIREOBSERVE,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * reobserve OCR
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async reobserveOCRLabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      let items = await item_service.reobserveOCRLabel(req, res);
      if (items) {
        return res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMOCRREOBSERVE,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * delete OCR label
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async deleteOCRLabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }

    try {
      let items = await item_service.deleteOCRLabel(req, res);
      if (items) {
        return res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.ITEMOCRLABELDELETED,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
