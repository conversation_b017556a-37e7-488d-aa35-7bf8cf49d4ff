var express = require("express");
var router = express.Router();

/* require for Authentication */
const user_auth = require("../../middleware/auth").userAuthentication;
const validators = require("../../assets/validators");

/* require for Controller */
const announcement_controller = require("../../controllers/app/announcement.controller");

router.post("/list", user_auth, announcement_controller.listAnnouncement);

/* API to add comment/message */
router.post(
  "/comment/add",
  user_auth,
  validators.app.comment.addUpdate,
  announcement_controller.addUserComment
);

/* API to add comment/message */
router.put(
  "/comment/edit",
  user_auth,
  validators.app.comment.addUpdate,
  announcement_controller.editUserComment
);

module.exports = router;
