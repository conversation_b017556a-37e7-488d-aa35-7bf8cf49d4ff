"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class tbl_media extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_media.init(
    {
      item_id: DataTypes.STRING,
      media: DataTypes.STRING,
      user_id: DataTypes.INTEGER,
      media_type: {
        type: DataTypes.ENUM("image", "video", "audio", "pdf", "doc", "text", "link", "contact", "location", "create_doc"),
        defaultValue: "image",
      },
      local_path: DataTypes.STRING,
      resolution: DataTypes.STRING,
      source: DataTypes.STRING,
      AI_objects: DataTypes.JSON,
      extention: DataTypes.STRING,
      duration: DataTypes.STRING,
      size: DataTypes.FLOAT,
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize,
      modelName: "tbl_media",
    }
  );
  return tbl_media;
};
