'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_roles extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_roles.init({
    role_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    module_name: DataTypes.STRING,
    is_deleted: {
      allowNull: false, 
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_roles',
  });
  return tbl_roles;
};