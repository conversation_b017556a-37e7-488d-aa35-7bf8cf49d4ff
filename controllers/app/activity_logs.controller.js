/*
 * Summary:     tags.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const activity_log_service = require("../../services/app/activity_logs.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");


module.exports = {
    /* list */
    async list(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            let message = "";
            displayOnlyFirstError(errors.array()).map((el, i, arr) =>
                arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
            );
            return res
                .status(status.SUCCESSSTATUS)
                .send({ data: {}, message: message, status: 0 });
        }
        try {
            let activity_logs = await activity_log_service.list(req, res);

            if (activity_logs) {
                res.status(status.SUCCESSSTATUS).send({
                    data: activity_logs,
                    message: message.ITEMRETRIVED,
                    status: status.SUCCESS,
                });
            } else {
                res.status(status.SUCCESSSTATUS).send({
                    data: {},
                    message: message.NOITEMFOUND,
                    status: status.ERROR,
                });
            }
        } catch (error) {
            console.log("TCL:error", error);
            res.status(status.INTERNALSERVERERRORSTATUS).send({
                data: [],
                message: message.INTERNALSERVERERROR,
                status: status.ERROR,
            });
        }
    },
}