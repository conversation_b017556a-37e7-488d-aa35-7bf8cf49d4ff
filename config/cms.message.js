const cmsMessage = {
  /* Admin response message */
  EMAILNOTFOUND: "Email is not found",
  LOGNINSUCCESS: "Login succesfully",
  INCORRECTPASSWORD: "Password is incorrect",
  INTERNALSERVERERROR: "Internal server error",
  LOGOUTSUCCESS: "Logout successfully",
  SUCCESS: "Success",
  NORECORDFOUND: "No record found",
  INVALIDHEADERS: "Invalid headers",
  TOKENREQUIRED: "Authentication token required",
  PASSWORDCHANGED: "Your password changed successfully",
  EMAILSENTSUCCESSFULLY: "Email sent successfully",
  CONFIRMPASSWORDNOTMATCH: "new password and confirm password don't match",
  INCORRECTOLDPASSWORD: "Old password is incorrect",
  // USERDELETED: "User deleted",
  USERDELETED: "User Deleted successfully.",
  UPDATE: "User update",
  USEREXIST: "User already exits",
  PASSWORDRESET: "Your password reset successfully",
  CONTENTPAGEADDED: "New Content added",
  CONTENTPAGEDUPDATED: "Content updated successfully",
  CONTENTPAGEDNOTFOUND: "Content not found",
  CONTENTPAGEDDELETED: "Content deleted successfully",
  CONTENTPAGEDSTATUS: "Change Content status successfully",
  CONTENTPAGELIST: "Content list successfully",
  ADMINADDED: "New Admin added",
  ADMINUPDATED: "Admin updated successfully",
  ADMINDELETED: "Admin deleted",
  ADMINNOTFOUND: "Admin not found",
  ADMINLIST: "Admin list successfully",
  ADMINEXIST: "Admin already exits",
  ROLEADDED: "New Role added",
  ROLEUPDATED: "Role updated successfully",
  ROLEDELETED: "Role deleted",
  ROLENOTFOUND: "Role not found",
  ROLELIST: "Role list successfully",
  ASSIGNROLES: "Assign Roles",
  ASSIGNROLELIST: "Admin Role list",
  SENDNOTIFICATION: "Send Notification successfully",
  NOTIFICATIONLISTNOTFOUND: "Notification not found",
  NOTIFICATIONLIST: "Notification list successfully",
  SENDANNOUNCEMENT: "Send Announcement successfully",
  ANNOUNCEMENTLISTNOTFOUND: "Announcement not found",
  COMMENTLISTNOTFOUND: "Comment list not found",
  ANNOUNCEMENTLIST: "Announcement list successfully",
  ANNOUNCEMENTDELETED: "Announcement deleted successfully",
  PRODUCTIDEXIST: "Product Id Already exist.",
  PRODUCTIDUSED: "The Product ID was previously used but has been deleted.",
  STORAGEPLANADDED: "New Storage plan added",
  AISTORAGEPLANADDED: "New AI plan added",
  AISTORAGEPLANUPDATED: "AI plan updated successfully.",
  STORAGEPLANNOTFOUND: "Plan not found",
  STORAGEPLANUPDATED: "Storage plan updated successfully",
  STORAGEPLANDELETED: "Storage plan deleted",
  PLANDELETED: "Plan deleted",
  STORAGEPLANLIST: "Storage plan list successfully",
  TRANSACTIONNOTFOUND: "Transaction not found",
};

module.exports = { cmsMessage };
