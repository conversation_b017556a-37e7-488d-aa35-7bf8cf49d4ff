const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const user_service = require("../../services/cms/user.service");

const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* User List */
  async listUser(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user_list = await user_service.listUser(req, res);

      if (user_list) {
        res.status(status.SUCCESSSTATUS).send({
          data: user_list,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("Kyulebag Admin: listUser -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // delete user list
  async getDelUser(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user_list = await user_service.getDelUser(req, res);

      if (user_list) {
        res.status(status.SUCCESSSTATUS).send({
          data: user_list,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("Kyulebag Admin: listUser -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* View provider by ID*/
  async getUserById(req, res) {
    try {
      let user_list = await user_service.getUserById(req, res);
      if (user_list) {
        //response of list category And Sub-category
        res.status(status.SUCCESSSTATUS).send({
          data: user_list,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("viewUser -> error", error);
      //response on internal server error
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /** Update provider or customer status */
  async updateUser(req, res) {
    try {
      let update_user_status = await user_service.updateUser(req, res);
      if (update_user_status) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.UPDATE,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("updateUser -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /*  Delete Provider Or customer status  */
  async deleteUser(req, res) {
    try {
      await user_service.deleteUser(req, res);

      res.status(status.SUCCESSSTATUS).send({
        data: [],
        message: message.USERDELETED,
        status: status.SUCCESS,
      });
      // }
    } catch (error) {
      console.log("deleteUser -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
