# KyuleBag Backend Test Execution Guide

## Overview
This guide provides comprehensive instructions for executing backend test scenarios documented in the Excel file `Backend_Scenario_Test_Cases.xlsx`. The test cases cover complete request-response lifecycles for all API endpoints with unit, integration, and scenario-based testing approaches.

## Prerequisites

### Environment Setup
1. **Node.js Environment**: Version 14.x or higher
2. **Database**: MySQL test database configured
3. **Dependencies**: All npm packages installed
4. **Environment Variables**: Test environment configuration
5. **External Services**: Mock configurations for GCP, Email services

### Required Tools
- Jest testing framework
- Supertest for API testing
- Database seeding tools
- Mock service configurations

## Test Structure Overview

### Test Categories
- **Unit Tests**: Individual function/method testing
- **Integration Tests**: Database and service integration
- **Scenario Tests**: Complete user workflow testing
- **Edge Case Tests**: Boundary and error condition testing
- **Security Tests**: Authentication and authorization validation
- **Performance Tests**: Load and response time validation

### Module Coverage
1. **Authentication & Authorization** (Critical Priority)
2. **User Management** (High Priority)
3. **Item Management** (High Priority)
4. **Tag Management** (Medium Priority)
5. **Admin Panel** (Critical Priority)
6. **Subscription Management** (High Priority)
7. **File Upload & Media** (High Priority)
8. **Security Validation** (Critical Priority)

## Execution Commands

### Basic Test Execution
```bash
# Run all tests
npm test

# Run with coverage report
npm run test:coverage

# Run specific test suite
npm test -- --testNamePattern="Authentication"

# Run integration tests only
npm run test:integration

# Run unit tests only
npm run test:unit
```

### Module-Specific Testing
```bash
# Authentication tests
npm test services/app/__tests__/common.service.signup.test.js
npm test services/app/__tests__/common.service.signin.test.js

# Item management tests
npm test controllers/app/__tests__/items.controller.test.js

# Admin panel tests
npm test services/cms/__tests__/admin.service.test.js
```

### Performance Testing
```bash
# Load testing with artillery
npm run test:load

# Memory usage monitoring
npm run test:memory

# Response time benchmarks
npm run test:performance
```

## Test Data Management

### Database Setup
```bash
# Create test database
npm run db:test:create

# Run migrations
npm run db:test:migrate

# Seed test data
npm run db:test:seed

# Clean test database
npm run db:test:clean
```

### Mock Services Configuration
- **GCP Storage**: Mock file upload/download
- **Email Service**: Mock email sending
- **Payment Gateway**: Mock payment processing
- **AI Services**: Mock AI/OCR processing

## Request-Response Lifecycle Testing

### Authentication Flow Testing
1. **Input Validation**: Email format, password strength
2. **Business Logic**: User existence check, password hashing
3. **Database Operations**: User creation, token generation
4. **External Services**: Email verification sending
5. **Response Generation**: JWT token, user profile data

### Item Management Flow Testing
1. **Authentication Middleware**: JWT validation
2. **File Upload Processing**: Multer middleware, file validation
3. **Storage Operations**: GCP upload, quota checking
4. **AI Processing**: Image/document analysis
5. **Database Operations**: Item creation, metadata storage
6. **Activity Logging**: User action tracking

### Error Handling Validation
- **Validation Errors**: Input format validation
- **Authentication Errors**: Invalid tokens, expired sessions
- **Business Logic Errors**: Duplicate entries, quota exceeded
- **External Service Errors**: GCP failures, email service down
- **Database Errors**: Connection issues, constraint violations

## Test Execution Checklist

### Pre-Execution
- [ ] Test database is clean and seeded
- [ ] Environment variables are set correctly
- [ ] Mock services are configured
- [ ] Dependencies are installed and updated

### During Execution
- [ ] Monitor test coverage metrics
- [ ] Validate response times are within limits
- [ ] Check memory usage patterns
- [ ] Verify error handling scenarios

### Post-Execution
- [ ] Review test results and coverage reports
- [ ] Document any failures or issues
- [ ] Update test cases based on findings
- [ ] Clean up test data and resources

## Coverage Requirements

### Minimum Coverage Targets
- **Unit Tests**: 90% code coverage
- **Integration Tests**: 80% endpoint coverage
- **Scenario Tests**: 100% critical user journey coverage
- **Edge Cases**: 95% error condition coverage

### Critical Path Coverage
- User registration and authentication
- Item creation and management
- File upload and processing
- Subscription and payment flows
- Admin panel operations

## Continuous Integration

### Automated Testing Pipeline
```yaml
# Example CI configuration
test:
  stage: test
  script:
    - npm install
    - npm run db:test:setup
    - npm run test:coverage
    - npm run test:integration
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
```

### Quality Gates
- All tests must pass
- Coverage thresholds must be met
- Performance benchmarks must be satisfied
- Security scans must pass

## Troubleshooting

### Common Issues
1. **Database Connection Errors**: Check test database configuration
2. **Mock Service Failures**: Verify mock configurations
3. **File Upload Issues**: Check GCP mock setup
4. **Authentication Failures**: Validate JWT configuration

### Debug Commands
```bash
# Run tests in debug mode
npm run test:debug

# Verbose output
npm test -- --verbose

# Run single test file
npm test -- --testPathPattern="signup.test.js"
```

## Reporting

### Test Reports Generated
- Coverage reports (HTML format)
- Performance benchmarks
- Security scan results
- Integration test results

### Metrics Tracked
- Test execution time
- Code coverage percentage
- API response times
- Error rates by module

## Next Steps

1. Execute all test scenarios documented in Excel
2. Validate coverage meets requirements
3. Document any gaps or issues found
4. Implement additional test cases as needed
5. Set up automated CI/CD pipeline integration

---

**Note**: This guide should be used in conjunction with the comprehensive test case documentation in `Backend_Scenario_Test_Cases.xlsx` which contains detailed test scenarios, expected responses, and validation criteria for each API endpoint.
