require("dotenv").config();
const AIStoragePlan = require("../../database/models").tbl_ai_subscriptions;
const AINewStoragePlan = require("../../database/models").tbl_new_ai_subscriptions;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");
const { deleteInAppProduct } = require("../../helper/gcpUtils");
const message = require("../../config/cms.message").cmsMessage;

module.exports = {
  async addAIsubscription(req, res) {
    let add_storage_plan = await AIStoragePlan.create({
      number_of_api: req.body.number_of_api,
      price: req.body.price,
      duration: req.body.duration,
      // is_active: req.body.is_active
    });
    return add_storage_plan;
  },

  async listAIsubscription(req, res) {
    let { sort_by, search, order, is_deleted } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search = search === undefined ? "" : search;
    let where = {
        is_deleted: "0",
        [Op.or]: [
          {
            number_of_api: { [Op.like]: `%${search}%` },
          },
          {
            price: { [Op.like]: `%${search}%` },
          },
          // {
          //   duration: { [Op.like]: `%${search}%` },
          // },
        ],
    };

    if (is_deleted === "1") {
      where["is_deleted"] = "1";
      where["is_free"] = "0";
    }

    // if (is_verified) {
    //   searchObj.where.is_verified = parseInt(is_verified);
    // }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* storage plan find query */
    let storage_plan_list = await AINewStoragePlan.findAndCountAll({
      where: where,
      attributes: {
        include: ["createdAt", "updatedAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });
    return storage_plan_list;
  },

  async editAIsubscription(req) {
    let storage_plan_edit_obj = {
      number_of_api: req.body.number_of_api && req.body.number_of_api,
      price: req.body.price && req.body.price,
      duration: req.body.duration && req.body.duration,
      is_deleted: req.body.is_deleted && req.body.is_deleted,
    };
    const storagePlan_id = await AIStoragePlan.findOne({
      where: { ai_subscription_id: req.params.id },
    });
    if (storagePlan_id !== null) {
      let edit_storage = await AIStoragePlan.update(storage_plan_edit_obj, {
        where: { ai_subscription_id: req.params.id },
      });
      var edit_storagePlan = await AIStoragePlan.findOne({
        where: {
          ai_subscription_id: req.params.id,
        },
      });
      return edit_storagePlan;
    } else {
      return 0;
    }
  },

  async deleteAIStoragePlan(req, res) {
    try {
      const packageName = constant.PLAY_CONSOLE_PACKAGE_NAME;
      const findAiPlan = await AINewStoragePlan.findOne(
        {
          where: { ai_subscription_id: req.params.id, }
        }
      )

      if (!findAiPlan) {
        return { data: [], message: message.STORAGEPLANNOTFOUND }
      }

      await deleteInAppProduct(packageName, findAiPlan.ai_subscription_product_id);

      const updateAiPlan = await AINewStoragePlan.update(
        {
          is_deleted: "1",
        },
        {
          where: {
            ai_subscription_id: req.params.id,
          },
        }
      );
      return { data: updateAiPlan, message: message.PLANDELETED };
    } catch (error) {
      throw error;
    }
  },

  async retriveAIsubscription(req, res) {
    let find_storagePlan = await AINewStoragePlan.findOne({
      where: { ai_subscription_id: req.params.id },
    });
    find_storagePlan.dataValues.price = parseFloat(find_storagePlan.price)
    return find_storagePlan;
  },
};
