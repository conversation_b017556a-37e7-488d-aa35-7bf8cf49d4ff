/*
 * Summary:     trash.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const user_auth = require("../../middleware/auth").userAuthentication;
const trash_controller = require("../../controllers/app/trash_management.controller");

router.post("/list", user_auth, trash_controller.list);
router.post("/delete", user_auth, trash_controller.delete);
router.post("/restore", user_auth, trash_controller.restore);

module.exports = router;
