/*
 * Summary:     dashboard.controller file for handling all requests and response of DASHBOARD    - CMS
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/cms.message").cmsMessage;
const dashboardService = require("../../services/cms/dashboard.service");

module.exports = {
  /* List Dashboard */

  async dashboard(req, res) {
    try {
      let dashboard = await dashboardService.dashboard(req, res);
     
        //response of count 
        res.status(status.SUCCESSSTATUS).send({
          count: dashboard,
          message: message.SUCCESS,
          status: status.SUCCESS
        });
      
    } catch (error) {
      console.log(error);
      //response on internal server error
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        
        message: message.INTERNALSERVERERROR,
        status: status.ERROR
      });
    }
  }
};
