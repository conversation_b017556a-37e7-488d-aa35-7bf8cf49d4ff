/**
 * Application Constants and Configuration Module
 * 
 * This file centralizes application-wide constants, configuration values, and service parameters
 * for the KyuleBag platform. It manages settings for external services, storage configurations,
 * authentication parameters, and business logic constants.
 * 
 * Business Logic Connection:
 * - Defines critical parameters for cloud storage operations (GCP)
 * - Manages authentication and security configurations (JWT, API keys)
 * - Sets email service parameters for user communications
 * - Configures AI/ML service integrations for content processing
 * - Establishes payment processing and subscription management settings
 * 
 * Key Features:
 * - Email service configuration (<PERSON><PERSON>, Nodemailer)
 * - Cloud storage paths and bucket configurations
 * - JWT authentication parameters
 * - External API credentials and endpoints
 * - File processing and storage limits
 * 
 * Default Behaviors:
 * - 5MB default storage limit for new users
 * - 24-hour JWT token expiration
 * - Default AI confidence level: 75%
 * - 10 items per page for pagination
 * - URL regex pattern for link detection
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

require('dotenv').config();

/**
 * EMAIL SERVICE CONFIGURATION
 * SMTP and email delivery service parameters for user communications
 * Used for: account verification, password reset, notifications, system emails
 */
const MAIL_FROM = process.env.MAIL_FROM;
const MAIL_FROM_AUTH = process.env.MAIL_FROM_AUTH;
const MAIL_PASSWORD = process.env.MAIL_PASSWORD;
const MAIL_SERVICE = process.env.MAIL_SERVICE;
const MAIL_HOST = process.env.MAIL_HOST;
const MAIL_PORT = process.env.MAIL_PORT;
const MAIL_METHOD = process.env.MAIL_METHOD;
const MAIL_SECURE = process.env.MAIL_SECURE;
const MAIL_SECURE_CONNECTION = process.env.MAIL_SECURE_CONNECTION;
const MAIL_REQUIRE_TLS = process.env.MAIL_REQUIRE_TLS;
const MAIL_DEBUG = process.env.MAIL_DEBUG;

/**
 * APPLICATION URL CONFIGURATION
 * Base URLs for different services and environments
 */
const WEBURL = process.env.WEBURL;
const BUCKETURL = process.env.BUCKETURL;
const DEFAULTMILES = '500';

/**
 * GOOGLE CLOUD PLATFORM CONFIGURATION
 * GCP service parameters for storage, AI, and processing services
 */
const GCP_PROJECT_ID = process.env.GCP_PROJECT_ID;
const GCP_URL = process.env.GCP_URL;
const GCP_BUCKET_NAME = process.env.GCP_BUCKET_NAME;
const GCP_BUCKET_FOLDER = process.env.GCP_BUCKET_FOLDER;
const GCP_ADMIN_FOLDER = process.env.GCP_ADMIN_FOLDER;
const GCP_USER_FOLDER = process.env.GCP_USER_FOLDER;
const GCP_ITEM_FOLDER = process.env.GCP_ITEM_FOLDER;
const GCP_STATIC_FOLDER = process.env.GCP_STATIC_FOLDER;

const PLAY_CONSOLE_PACKAGE_NAME = process.env.PLAY_CONSOLE_PACKAGE_NAME;

/**
 * AI/ML SERVICE CONFIGURATION
 * Google Cloud Video Intelligence API feature types for content analysis
 */
const GCP_INTELLIGENCE_FEATURE_TYPE = {
  LABEL_DETECTION: 'LABEL_DETECTION',
  TEXT_DETECTION: 'TEXT_DETECTION',
};

/**
 * SUBSCRIPTION AND PAYMENT CONFIGURATION
 * Business logic constants for monetization features
 */
const SUBSCRIPTION_TYPE = {
  INAPP: 'INAPP',
  SUBSCRIPTION: 'SUBSCRIPTION',
};

const PAYMENT_HISTORY_TYPE = {
  STORAGE: 'STORAGE',
  AI: 'AI',
};

/**
 * JWT AUTHENTICATION CONFIGURATION
 * JWT tokens are used for:
 * - User session management
 * - API endpoint authentication
 * - Mobile app authorization
 */
const JWTTOKEN = {
  algo: 'HS256',
  secret: process.env.JWT_SECRET || 'NP/ixC#sf4mUzd08',
  expiresIn: '24h',
};

const JWTAPPTOKEN = {
  algo: 'HS256',
  secret: process.env.JWT_APP_SECRET || '=NPsq{RJP]5ELt20',
};

/**
 * EXTERNAL SERVICE API CREDENTIALS
 */
const TELNYX_DETAILS = {
  API_KEY:
    process.env.TELNYX_API_KEY ||
    '**********************************************************',
  PRIMARY_NUMBER: process.env.TELNYX_PRIMARY_NUMBER || '+***********',
};

/**
 * REGEX PATTERNS
 * Regular expressions for data validation and processing
 */
const DETECT_URL_REGEX =
  '([a-zA-Z0-9]+://)?([a-zA-Z0-9_]+:[a-zA-Z0-9_]+@)?([a-zA-Z0-9.-]+\\.[A-Za-z]{2,4})(:[0-9]+)?(/.*)?';

/**
 * EXPORTED CONFIGURATION OBJECT
 * All constants and configurations made available to the application
 */
module.exports = {
  MAIL_FROM: MAIL_FROM,
  MAIL_FROM_AUTH: MAIL_FROM_AUTH,
  MAIL_PASSWORD: MAIL_PASSWORD,
  MAIL_SERVICE: MAIL_SERVICE,
  MAIL_HOST: MAIL_HOST,
  MAIL_PORT: MAIL_PORT,
  MAIL_METHOD: MAIL_METHOD,
  MAIL_SECURE: MAIL_SECURE,
  MAIL_SECURE_CONNECTION: MAIL_SECURE_CONNECTION,
  MAIL_REQUIRE_TLS: MAIL_REQUIRE_TLS,
  MAIL_DEBUG: MAIL_DEBUG,
  WEBURL: WEBURL,

  // Authentication Configuration
  JWTTOKEN: JWTTOKEN,
  JWTAPPTOKEN: JWTAPPTOKEN,
  LIMIT: 10,
  BUCKETURL: BUCKETURL,
  DEFAULTMILES: DEFAULTMILES,
  TELNYX_DETAILS: TELNYX_DETAILS,
  DETECT_URL_REGEX: DETECT_URL_REGEX,

  // Google Cloud Platform Configuration
  GCP_PROJECT_ID: GCP_PROJECT_ID,
  GCP_URL: GCP_URL,
  GCP_BUCKET_NAME: GCP_BUCKET_NAME,
  GCP_BUCKET_FOLDER: GCP_BUCKET_FOLDER,
  GCP_ADMIN_FOLDER: GCP_ADMIN_FOLDER,
  GCP_USER_FOLDER: GCP_USER_FOLDER,
  GCP_ITEM_FOLDER: GCP_ITEM_FOLDER,
  GCP_STATIC_FOLDER: GCP_STATIC_FOLDER,

  // AI/ML and Subscription Configuration
  GCP_INTELLIGENCE_FEATURE_TYPE: GCP_INTELLIGENCE_FEATURE_TYPE,
  SUBSCRIPTION_TYPE: SUBSCRIPTION_TYPE,
  PLAY_CONSOLE_PACKAGE_NAME: PLAY_CONSOLE_PACKAGE_NAME,
  PAYMENT_HISTORY_TYPE: PAYMENT_HISTORY_TYPE,
};
