'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_admins extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  };
  tbl_admins.init({
    admin_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    firstName: DataTypes.STRING,
    lastName: DataTypes.STRING,
    photo: DataTypes.STRING,
    email: {
      unique: true,
      type: DataTypes.STRING
    },
    password: DataTypes.STRING,
    adminType: DataTypes.ENUM('superAdmin', 'subAdmin'),
    isLogin: {
      allowNull: false,
      type: DataTypes.ENUM('false', 'true'),
      defaultValue: 'false',
    },
    is_deleted: {
      allowNull: false, 
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_admins',
  });
  return tbl_admins;
};