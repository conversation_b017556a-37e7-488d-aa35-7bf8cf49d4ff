require("dotenv").config();
const Announcement = require("../../database/models").tbl_announcements;
const User = require("../../database/models").tbl_users;
const CommentSettings = require("../../database/models").tbl_comment_settings;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");

module.exports = {
  async listComments(req, res) {
    let { sort_by, search, order } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search = search === undefined ? "" : search;

    let searchObj = {
      where: {
        [Op.and]: [
          {
            isDeleted: false,
          },
          {
            content: { [Op.like]: `%${search}%` },
          },
          {
            is_announcement: false,
          },
        ],
      },
    };

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* notification find query */
    let comment_list = await Announcement.findAndCountAll({
      where: searchObj.where,
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });
    if (comment_list) {
      comment_list = JSON.parse(JSON.stringify(comment_list));

      await Promise.all([
        ...comment_list.rows.map(async (eachComment, index) => {
          let userDetail = await User.findOne({
            where: {
              user_id: eachComment.user_id,
            },
            attributes: ["first_name", "last_name", "email", "phone"],
            raw: true,
          });

          comment_list.rows[index].first_name = userDetail?.first_name ? userDetail?.first_name : null;
          comment_list.rows[index].last_name = userDetail?.last_name ? userDetail?.last_name : null;
          comment_list.rows[index].email = userDetail?.email ? userDetail?.email : null;
          comment_list.rows[index].phone = userDetail?.phone ? userDetail?.phone : null;          
        }),
      ]);

      return comment_list;
    } else {
      return null;
    }
  },

  async allListComments(req, res) {

    /* notification find query */
    let comment_list = await Announcement.findAndCountAll({
      where: {
        isDeleted: false,
        is_announcement: false,
      },
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`createdAt`), `DESC`]],
    });
    if (comment_list) {
      comment_list = JSON.parse(JSON.stringify(comment_list));

      await Promise.all([
        ...comment_list.rows.map(async (eachComment, index) => {
          let userDetail = await User.findOne({
            where: {
              user_id: eachComment.user_id,
            },
            attributes: ["first_name", "last_name", "email", "phone"],
            raw: true,
          });

          comment_list.rows[index].first_name = userDetail?.first_name ? userDetail?.first_name : null;
          comment_list.rows[index].last_name = userDetail?.last_name ? userDetail?.last_name : null;
          comment_list.rows[index].email = userDetail?.email ? userDetail?.email : null;
          comment_list.rows[index].phone = userDetail?.phone ? userDetail?.phone : null;
        }),
      ]);

      return comment_list;
    } else {
      return null;
    }
  },

  async getCommentSettings(req, res) {
    let comment_settings = await CommentSettings.findAndCountAll({});
    return comment_settings;
  },

  async editCommentSettings(req, res) {
    try {
      await CommentSettings.update(
        {
          ...req.body,
        },
        {
          where: {
            // As of now only one comment setting so it is hardcoded
            commentSettingId: 1,
          },
        }
      );
    } catch (error) {
      throw error;
    }
  },
};
