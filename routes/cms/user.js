/** NPM-modules */
const express = require("express");
const router = express.Router(); 
const user_controller = require("../../controllers/cms/user.controller");
const validation = require("../../assets/validators");
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

router.post("/list", authentication,/*validation.cms.users.user_list,*/ user_controller.listUser );
router.post("/dellist", authentication, user_controller.getDelUser); /** Delete user list API */
router.get("/view/:id", authentication, user_controller.getUserById);
router.put("/update/:id", authentication, user_controller.updateUser); /** User update API */
router.put("/delete/:id", authentication, user_controller.deleteUser); /*Delete User API*/

module.exports = router;