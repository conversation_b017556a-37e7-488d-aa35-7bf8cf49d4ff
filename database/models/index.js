/**
 * Sequelize ORM Models Index and Database Initialization
 *
 * This file serves as the central hub for database connection management and model registration
 * in the KyuleBag application. It initializes the Sequelize ORM, establishes MySQL database
 * connection, and automatically loads all model definitions with their associations.
 *
 * Business Logic Connection:
 * - Provides foundation for all data operations including user management, content storage,
 *   subscription handling, and business analytics
 * - Establishes reliable database connectivity for production workloads
 * - Manages model relationships and associations for complex business queries
 * - Supports multiple environments with proper configuration management
 *
 * Key Features:
 * - Automatic model discovery and registration from files in models directory
 * - Database connection authentication and error handling
 * - Model association setup for foreign key relationships
 * - Environment-based configuration loading
 * - MySQL dialect configuration with connection pooling
 *
 * Model Loading Process:
 * 1. Initialize Sequelize instance with database configuration
 * 2. Authenticate database connection
 * 3. Discover and load all model files (excluding index.js)
 * 4. Register models with Sequelize instance
 * 5. Setup model associations and foreign key relationships
 * 6. Export database object with models and Sequelize instance
 *
 * Default Behaviors:
 * - MySQL dialect with connection pooling
 * - Automatic model file discovery based on .js extension
 * - Association setup after all models are loaded
 * - Connection authentication on startup with error logging
 * - Environment defaults to 'development' if NODE_ENV not set
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

'use strict';

// Core Node.js modules for file system operations
const fs = require('fs');
const path = require('path');

// Sequelize ORM for database operations
const Sequelize = require('sequelize');

// Current file information for model discovery
const basename = path.basename(__filename);

// Environment configuration
// Defaults to 'development' if NODE_ENV environment variable is not set
const env = process.env.NODE_ENV || 'development';

// Load database configuration for current environment
const config = require(__dirname + '/../../config/config.js')[env];

// Database models container object
const db = {};

/**
 * Sequelize Instance Initialization
 *
 * Creates a new Sequelize instance with MySQL database connection.
 * Uses configuration parameters from the environment-specific config
 * to establish connection with proper dialect and connection options.
 */
let sequelize;
sequelize = new Sequelize(
  config.database, // Database name
  config.username, // Database username
  config.password, // Database password
  config, // Main configuration object
  {
    dialect: 'mysql', // MySQL database dialect
    operatorsAliases: false, // Disable operator aliases for security
    dialectOptions: {
      dateStrings: false, // Return dates as Date objects, not strings
      typeCast: true, // Enable MySQL type casting
    },
  }
);

/**
 * Database Connection Authentication
 *
 * Tests the database connection to ensure proper connectivity before
 * proceeding with model loading. Logs success or failure messages
 * for debugging and monitoring purposes.
 */
sequelize
  .authenticate()
  .then(() => {
    console.log(
      `✅ Database connection established successfully to: ${process.env.DB_DATABASE}`
    );
    console.log(
      `🏠 Database host: ${process.env.DB_HOST}:${process.env.DB_PORT}`
    );
    console.log(`🌍 Environment: ${env}`);
  })
  .catch((err) => {
    console.error('❌ Unable to connect to the database:', err);
    console.error('🔍 Check database configuration and network connectivity');
  });

/**
 * Automatic Model Discovery and Registration
 *
 * Scans the current directory for all JavaScript files (excluding index.js)
 * and automatically loads them as Sequelize models. Each model file should
 * export a function that takes (sequelize, DataTypes) parameters and returns
 * a model definition.
 *
 * Model File Requirements:
 * - Must be JavaScript files with .js extension
 * - Must export a function that defines and returns a Sequelize model
 * - Should follow naming convention: tbl_[entity_name].js
 * - Must use Sequelize.DataTypes for field definitions
 */
fs.readdirSync(__dirname)
  .filter((file) => {
    return (
      file.indexOf('.') !== 0 && // Exclude hidden files (starting with .)
      file !== basename && // Exclude current file (index.js)
      file.slice(-3) === '.js' // Include only JavaScript files
    );
  })
  .forEach((file) => {
    // Load each model file and initialize with Sequelize instance
    // Note: sequelize['import'] is deprecated, using require directly
    const model = require(path.join(__dirname, file))(sequelize, Sequelize);

    // Register model in database object using model name as key
    db[model.name] = model;

    console.log(`📋 Loaded model: ${model.name} from ${file}`);
  });

/**
 * Model Association Setup
 *
 * After all models are loaded, establishes relationships and associations
 * between models. This includes foreign key relationships, one-to-many,
 * many-to-many, and other complex associations required for business logic.
 *
 * Association Types Supported:
 * - hasOne: One-to-one relationships
 * - hasMany: One-to-many relationships
 * - belongsTo: Many-to-one relationships
 * - belongsToMany: Many-to-many relationships with junction tables
 */
Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    // Call associate method if defined in model
    db[modelName].associate(db);
    console.log(`🔗 Set up associations for model: ${modelName}`);
  }
});

/**
 * Export Database Object
 *
 * Makes the complete database configuration available to the rest of the application.
 * Includes all registered models, the Sequelize instance, and the Sequelize constructor.
 */

// Add Sequelize instance for direct database operations
db.sequelize = sequelize;

// Add Sequelize constructor for data types and utilities
db.Sequelize = Sequelize;

/**
 * Database object structure:
 * {
 *   // All loaded models (tbl_users, tbl_items, etc.)
 *   [modelName]: ModelClass,
 *
 *   // Sequelize instance for direct queries
 *   sequelize: SequelizeInstance,
 *
 *   // Sequelize constructor for utilities
 *   Sequelize: SequelizeConstructor
 * }
 */
module.exports = db;

console.log(
  `🚀 Database initialization complete with ${
    Object.keys(db).length - 2
  } models loaded`
);
