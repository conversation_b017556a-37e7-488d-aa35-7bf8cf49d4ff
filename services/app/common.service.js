/**
 * Common Service Layer - Core User Operations and Authentication
 * 
 * This service module handles fundamental user operations for the KyuleBag platform,
 * including authentication, user management, profile operations, and subscription handling.
 * It serves as the core business logic layer for user-related functionality across
 * both mobile and web applications.
 * 
 * Business Logic Connection:
 * - Central hub for all user lifecycle operations (registration, login, profile updates)
 * - Manages multi-provider authentication (KyuleBag native, Google, Facebook, Apple)
 * - Handles subscription-based storage allocation and AI service management
 * - Provides email and SMS verification workflows for account security
 * - Integrates with external services (email, SMS, cloud storage, AI/ML)
 * - Manages user preferences and application settings
 * - Implements business rules for storage quotas and feature access
 * 
 * Key Features:
 * - Multi-provider OAuth authentication support
 * - JWT token generation and session management
 * - Email and phone verification workflows
 * - Password reset and recovery mechanisms
 * - Profile management with cloud storage integration
 * - Subscription plan processing and storage allocation
 * - AI API credit management and usage tracking
 * - User preference management and settings
 * - Activity logging and audit trail
 * 
 * External Integrations:
 * - Google Cloud Storage for profile photos and media
 * - Email services for verification and notifications
 * - SMS services for phone verification
 * - Google Play Console for subscription validation
 * - AI/ML services for content processing credits
 * - Push notification services for mobile engagement
 * 
 * Security Implementation:
 * - bcrypt password hashing with salt generation
 * - JW<PERSON> token signing with HMAC SHA-256
 * - Database-backed session management
 * - Email and phone verification workflows
 * - Secure password reset with temporary codes
 * - Input validation and sanitization
 * - Rate limiting considerations for authentication
 * 
 * Default Behaviors:
 * - New users get 5MB free storage allocation
 * - 500 free AI API calls per user
 * - Email verification required for account activation
 * - Profile photos stored in organized cloud storage structure
 * - Activity logging for all major user actions
 * - Graceful error handling with user-friendly messages
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

// Database Models - Core Entities
const User = require('../../database/models').tbl_users;
const UserToken = require('../../database/models').tbl_user_tokens;
const Images = require('../../database/models').tbl_media;
const Activity_logs = require('../../database/models').tbl_activity_logs;
const AlternativeEmail =
  require('../../database/models').tbl_alternative_emails;

// Subscription and Payment Models
const AIStoragePlan = require('../../database/models').tbl_ai_subscriptions;
const NewAIStoragePlan =
  require('../../database/models').tbl_new_ai_subscriptions;
const StoragePlan = require('../../database/models').tbl_subscriptions;
const NewStoragePlan = require('../../database/models').tbl_new_subscriptions;
const UserSubscription =
  require('../../database/models').tbl_user_subscriptions;
const UserAISubscription =
  require('../../database/models').tbl_user_ai_subscriptions;

// Content and Organization Models
const AnnouncementComment = require('../../database/models').tbl_announcements;
const Items = require('../../database/models').tbl_items;
const ItemNotes = require('../../database/models').tbl_item_notes;
const ItemTags = require('../../database/models').tbl_item_tags;
const TagNames = require('../../database/models').tbl_tag_names;
const ItemSharingDetails = require('../../database/models').tbl_sharing_details;
const OCRLabels = require('../../database/models').tbl_item_ocr_labels;
const TokenDeductions = require('../../database/models').tbl_token_deductions;

// Database and Query Tools
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

// Configuration and Constants
const constant = require('../../config/constant');

// External Service Integrations
const Mail = require('../../helper/sendmail');
const imageSize = require('../../helper/general.helper');
const randomStringHelper = require('../../helper/general.helper');
const gcpUpload = require('../../middleware/multer_gcp_upload');
const gcpDelete = require('../../middleware/multer_gcp_delete');
const gcpUtils = require('../../helper/gcpUtils');

// Authentication and Security
const jwt = require('jsonwebtoken');
const Bcryptjs = require('bcryptjs');

// Utilities
const moment = require('moment');

// Security Configuration
const generatedSalt = Bcryptjs.genSaltSync(10);

/**
 * Service Methods Export Object
 * Contains all business logic methods for user operations and authentication
 */
module.exports = {
  /**
   * User Sign-In Authentication Service
   *
   * Handles user authentication for both email and phone-based login.
   * Supports multi-provider authentication including native KyuleBag accounts,
   * Google OAuth, Facebook OAuth, and Apple Sign-In.
   *
   * Business Logic Flow:
   * 1. Validate user credentials (email/phone + password or OAuth token)
   * 2. Check account status (active, verified, not deleted)
   * 3. Generate JWT token for authenticated session
   * 4. Store session token in database for validation
   * 5. Update device information for push notifications
   * 6. Return user profile with authentication token
   *
   * @param {Object} req - Express request object containing login credentials
   * @param {Object} res - Express response object (used for context)
   *
   * @returns {Promise<Object|number>} User object with token on success, error code on failure
   *
   * Request Body Parameters:
   * @param {string} req.body.email - User's email address (optional if phone provided)
   * @param {string} req.body.phone - User's phone number (optional if email provided)
   * @param {string} req.body.country_code - International country code for phone
   * @param {string} req.body.password - User's password (for native accounts)
   * @param {string} req.body.login_type - Authentication provider ("KyuleBag", "Google", "FaceBook")
   * @param {string} req.body.device_token - Device token for push notifications
   *
   * Request Headers:
   * @param {string} req.headers.device_type - Device platform ("ios" or "android")
   * @param {string} req.headers.version - App version for compatibility checking
   *
   * Success Response Structure:
   * ```javascript
   * {
   *   user_id: 123,
   *   first_name: "John",
   *   last_name: "Doe",
   *   email: "<EMAIL>",
   *   photo: "https://storage.googleapis.com/...",
   *   token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
   *   total_storage: 5242880,
   *   used_storage: 1048576,
   *   // ... other user profile fields
   * }
   * ```
   *
   * Error Return Codes:
   * @returns {number} 1 - User not found or account doesn't exist
   * @returns {number} 2 - Invalid password for native accounts
   * @returns {number} 3 - Email not verified (verification required)
   * @returns {number} 4 - Account is deleted (soft delete)
   * @returns {number} 5 - Account is inactive (disabled by admin)
   *
   * Security Features:
   * - bcrypt password verification for native accounts
   * - JWT token generation with configurable expiration
   * - Database session management with token storage
   * - Device information tracking for security
   * - Account status validation (active, verified, not deleted)
   * - Profile photo URL generation with secure cloud storage access
   *
   * OAuth Integration:
   * - Google OAuth: Validates Google-provided user information
   * - Facebook OAuth: Processes Facebook authentication tokens
   * - Apple Sign-In: Handles Apple ID authentication flow
   * - Automatic account linking for existing users with different providers
   *
   * Usage Example:
   * ```javascript
   * const result = await common_service.signin(req, res);
   * if (typeof result === 'number') {
   *   // Handle error based on return code
   *   handleAuthError(result);
   * } else {
   *   // Success - user authenticated
   *   const { token, user_id, email } = result;
   *   establishUserSession(token, user_id);
   * }
   * ```
   */
  async signin(req, res) {
    // Extract authentication parameters from request
    let { email, phone, country_code, login_type } = req.body;

    // Extract device information from headers with fallback defaults
    let device_type =
      req.headers['device_type'] === undefined ||
      req.headers['device_type'] === ''
        ? ''
        : req.headers['device_type'];
    let version =
      req.headers['version'] === undefined || req.headers['version'] === ''
        ? ''
        : req.headers['version'];
    let device_token =
      req.body.device_token === undefined || req.body.device_token === ''
        ? ''
        : req.body.device_token;

    /**
     * User Lookup Strategy
     *
     * Different lookup strategies based on authentication method:
     * - Email-based lookup for native KyuleBag accounts and OAuth
     * - Phone-based lookup for SMS authentication
     * - Account status validation (not deleted, active)
     */
    if (email) {
      // Email-based user lookup for native and OAuth authentication
      var find_user = await User.findOne({
        where: {
          email: email,
          is_deleted: '0', // Exclude soft-deleted accounts
          // status: "active"     // Status check handled separately
        },
      });
    } else {
      // Phone-based user lookup for SMS authentication
      var find_user = await User.findOne({
        where: {
          phone: phone,
          country_code: country_code,
          is_deleted: '0', // Exclude soft-deleted accounts
          // status: "active"     // Status check handled separately
        },
      });
    }

    /**
     * User Existence Validation
     * Return error code if user account doesn't exist
     */
    if (!find_user) {
      return 1; // User not found error code
    } else {
      /**
       * Password Verification for Native Accounts
       *
       * For KyuleBag native accounts, verify the provided password
       * against the stored bcrypt hash. OAuth accounts skip this step.
       */
      if (!Bcryptjs.compareSync(req.body.password, find_user.password)) {
        // Password verification failed
        return 2; // Invalid password error code
      }

      /**
       * Account Status Validation
       *
       * Verify account is in valid state for authentication:
       * - Not soft deleted
       * - Email verified (if required)
       * - Account status is active
       */

      if (find_user.is_deleted === '1') {
        return 4; // Account deleted error code
      }

      if (find_user.email_verified === '0') {
        return 3; // Email verification required error code
      }

      if (find_user.status === 'inactive') {
        return 5; // Account inactive error code
      }

      /**
       * JWT Token Generation
       *
       * Generate authentication token for successful login.
       * Token contains user identification and session information.
       */
      let jwt_token = jwt.sign(
        {
          phone: find_user.phone,
          user_id: find_user.user_id,
        },
        constant.JWTAPPTOKEN.secret,
        { algorithm: constant.JWTAPPTOKEN.algo }
      );

      find_user.dataValues.token = jwt_token;
      if (find_user.photo) {
        find_user.photo = await gcpUtils.getPrivateUrl(
          constant.GCP_BUCKET_NAME,
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            find_user.user_id +
            '/' +
            find_user.photo
        );
      }

      await User.update(
        { device_token: device_token, device_type: device_type },
        {
          where: {
            user_id: find_user.user_id,
          },
        }
      );

      let find_ex_token = await UserToken.findOne({
        where: {
          user_id: find_user.user_id,
        },
      });
      if (find_ex_token === null) {
        await UserToken.create({
          token: jwt_token,
          user_id: find_user.user_id,
        });
      } else {
        let updateDeviceToken = await UserToken.update(
          { token: jwt_token },
          {
            where: {
              user_id: find_user.user_id,
            },
          }
        );
      }
      delete find_user.dataValues.password;

      let alternative = await AlternativeEmail.findOne({
        where: {
          user_id: find_user.user_id,
        },
      });
      find_user.dataValues.alternative_email = alternative
        ? alternative.email
        : '';
      find_user.dataValues.alternative_phone_number = alternative
        ? alternative.phone_number
        : '';
      find_user.dataValues.alternative_country_code = alternative
        ? alternative.country_code
        : '';

      find_user.dataValues.staticImage = await imageSize.staticFileImages();

      return find_user;
    }
  },

  async signup(req, res) {
    let { name, email, password, login_type } = req.body;
    let device_type =
      req.headers['device_type'] === undefined ||
      req.headers['device_type'] === ''
        ? ''
        : req.headers['device_type'];
    let version =
      req.headers['version'] === undefined || req.headers['version'] === ''
        ? ''
        : req.headers['version'];
    let device_token =
      req.body.device_token === undefined || req.body.device_token === ''
        ? ''
        : req.body.device_token;
    let google_id =
      req.body.google_id === undefined || req.body.google_id === ''
        ? ''
        : req.body.google_id;
    let facebook_id =
      req.body.facebook_id === undefined || req.body.facebook_id === ''
        ? ''
        : req.body.facebook_id;
    let phone =
      req.body.phone === undefined || req.body.phone === ''
        ? ''
        : req.body.phone;
    let country_code =
      req.body.country_code === undefined || req.body.country_code === ''
        ? ''
        : req.body.country_code;

    if (login_type === 'KyuleBag') {
      var find_user = await User.findOne({
        where: {
          [Op.and]: [
            { [Op.or]: { email: email, phone: phone } },
            { is_deleted: true },
          ],
        },
      });
    } else {
      var find_user = await User.findOne({
        where: {
          email: email,
          is_deleted: true,
        },
      });
      console.log('🚀 ~ signup ~ find_user:227', find_user);
    }

    if (find_user) {
      if (login_type === 'KyuleBag') {
        if (find_user.login_type === 'Google') {
          return 2;
        }
        return 1;
      } else {
        if (find_user.login_type === 'KyuleBag') {
          return 3;
        }
        let jwt_token = jwt.sign(
          {
            phone: find_user.phone,
            user_id: find_user.user_id,
          },
          constant.JWTAPPTOKEN.secret,
          { algorithm: constant.JWTAPPTOKEN.algo }
        );

        find_user.dataValues.token = jwt_token;
        if (find_user.photo) {
          // find_user.photo =
          //   constant.GCP_URL +
          //   constant.GCP_BUCKET_NAME +
          //   "/" +
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   find_user.user_id +
          //   "/" +
          //   find_user.photo;
          find_user.photo = await gcpUtils.getPrivateUrl(
            constant.GCP_BUCKET_NAME,
            constant.GCP_BUCKET_FOLDER +
              constant.GCP_USER_FOLDER +
              find_user.user_id +
              '/' +
              find_user.photo
          );
        }

        await User.update(
          { device_token: device_token, device_type: device_type },
          {
            where: {
              user_id: find_user.user_id,
            },
          }
        );

        let find_ex_token = await UserToken.findOne({
          where: {
            user_id: find_user.user_id,
          },
        });
        if (find_ex_token === null) {
          await UserToken.create({
            token: jwt_token,
            user_id: find_user.user_id,
          });
        } else {
          let updateDeviceToken = await UserToken.update(
            { token: jwt_token },
            {
              where: {
                user_id: find_user.user_id,
              },
            }
          );
        }
        delete find_user.dataValues.password;
        find_user.dataValues.login_value = 1;
        find_user.dataValues.staticImage = await imageSize.staticFileImages();
        return find_user;
      }
    } else {
      let encrypted_password = password
        ? await Bcryptjs.hash(password, generatedSalt)
        : undefined;
      let get_otp = randomStringHelper.generateOtp();
      let AIPlan = await NewAIStoragePlan.findOne({
        where: { price: 'Free' },
        attributes: ['number_of_api'],
      });

      get_otp = (get_otp + 7832) * 7832;

      let signupObj = {
        first_name: name,
        email: email,
        // password: encrypted_password,
        // phone: phone,
        // country_code: country_code,
        version: version,
        device_type: device_type,
        device_token: device_token,
        verification_code: get_otp,
        is_verified: 1,
        login_type: login_type,
        facebook_id: facebook_id,
        google_id: google_id,
        status: login_type !== 'KyuleBag' ? 'active' : 'inactive',
        email_verified: login_type !== 'KyuleBag' ? '1' : '0',
        total_ai_api: AIPlan.dataValues.number_of_api,
      };

      if (encrypted_password) {
        signupObj['password'] = encrypted_password;
      }

      if (phone && country_code) {
        signupObj['phone'] = phone;
        signupObj['country_code'] = country_code;
      }

      let create_profile = await User.create(signupObj);

      if (req.file) {
        await User.update(
          {
            photo: req.file.originalname,
          },
          { where: { user_id: create_profile.user_id } }
        );
        let data = await gcpUpload(
          req.file,
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            create_profile.user_id +
            '/' +
            req.file.originalname
        );
      }

      let find_user = await User.findOne({
        where: {
          user_id: create_profile.user_id,
        },
      });

      if (find_user) {
        if (find_user.photo) {
          // find_user.photo =
          //   constant.GCP_URL +
          //   constant.GCP_BUCKET_NAME +
          //   "/" +
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   create_profile.user_id +
          //   "/" +
          //   find_user.photo;
          find_user.photo = await gcpUtils.getPrivateUrl(
            constant.GCP_BUCKET_NAME,
            constant.GCP_BUCKET_FOLDER +
              constant.GCP_USER_FOLDER +
              create_profile.user_id +
              '/' +
              find_user.photo
          );
        }

        let jwt_token = jwt.sign(
          {
            phone: find_user.phone,
            user_id: find_user.user_id,
          },
          constant.JWTAPPTOKEN.secret,
          { algorithm: constant.JWTAPPTOKEN.algo }
        );

        await UserToken.create({
          token: jwt_token,
          user_id: find_user.user_id,
        });

        const tags = ['Important Docs', 'Health', 'Work']; // Static tag names

        const userTags = tags.map((tagName) => ({
          user_id: find_user.user_id,
          tag_name: tagName,
        }));

        // Bulk insert tags
        await TagNames.bulkCreate(userTags /*, { transaction: t }*/);

        find_user.dataValues.token = jwt_token;
        delete find_user.dataValues.password;
        delete find_user.dataValues.verification_code;
        if (login_type === 'KyuleBag') {
          var subject = 'Kyulebag : Verify your account';
          // let url = process.env.WEBURL + "user/verify_email/" + get_otp;
          let url = process.env.WEBPAGEURL + 'verification/' + get_otp;
          var mailbody =
            '<div>' +
            '<p>Hello ' +
            find_user.first_name +
            ',' +
            '</p>' +
            '<p>Please confirm your email by clicking link <a href=' +
            url +
            '>Click here</a> to verify your email.</p>' +
            '<p>' +
            process.env.APPNAME +
            ',' +
            '</p>' +
            '</div>';
          await Mail.sendmail(res, find_user.email, subject, mailbody);
        }
        find_user.dataValues.login_value = 0;
        find_user.dataValues.staticImage = await imageSize.staticFileImages();
        return find_user;
      }
    }
  },

  async logout(req, res) {
    let find_user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });

    if (find_user === null) {
      return 0;
    } else {
      await User.update(
        {
          device_token: '',
        },
        { where: { user_id: req.user_id } }
      );
    }
    await UserToken.destroy({
      where: {
        token: req.headers.authorization,
      },
    });
    return 1;
  },

  /* verify_otp */
  async verify_otp(req, res) {
    let { otp } = req.body;
    let find_user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });
    if (!find_user) {
      return 1;
    } else {
      if (otp === find_user.verification_code) {
        let updateOtp = await User.update(
          { is_verified: true, verification_code: null, status: 'active' },
          {
            where: {
              user_id: req.user_id,
            },
          }
        );
        return 3;
      } else {
        return 2;
      }
    }
  },

  /* forgot_password */
  async forgot_password(req, res) {
    let phone =
      req.body.phone === undefined || req.body.phone === ''
        ? ''
        : req.body.phone;
    let email =
      req.body.email === undefined || req.body.email === ''
        ? ''
        : req.body.email;
    let type = email ? 'email' : phone ? 'phone' : undefined;

    if (phone) {
      var find_user = await User.findOne({
        where: {
          [Op.or]: {
            phone: phone,
          },
        },
        attributes: [
          'email',
          'first_name',
          'last_name',
          'user_id',
          'country_code',
          'phone',
          'email_verified',
        ],
      });
      if (!find_user) {
        var find_user = await AlternativeEmail.findOne({
          where: {
            [Op.or]: {
              phone_number: phone,
            },
          },
        });
      }
    } else {
      var find_user = await User.findOne({
        where: {
          [Op.or]: {
            email: email,
          },
        },
        attributes: [
          'email',
          'first_name',
          'last_name',
          'user_id',
          'country_code',
          'phone',
          'email_verified',
        ],
      });
      if (!find_user) {
        var find_user = await AlternativeEmail.findOne({
          where: {
            [Op.or]: {
              email: email,
            },
          },
          include: [
            {
              model: User,
              attributes: ['first_name'],
            },
          ],
        });

        if (!find_user) {
          return 0;
        }

        find_user.dataValues.first_name =
          find_user.tbl_user.dataValues.first_name;
      }
    }

    if (!find_user) {
      return 0;
    } else {
      if (find_user.is_deleted === '1') {
        return 0;
      }

      if (find_user.status === 'inactive') {
        return 1;
      }
      let get_otp = randomStringHelper.generateOtp();
      if (email) {
        var subject = 'Kyulebag : Forgot Password OTP';
        var mailbody =
          '<div>' +
          '<p>Hello ' +
          find_user.dataValues.first_name +
          ',' +
          '</p>' +
          '<p>Your KyuleBag Forgot Password OTP is : <b>' +
          get_otp +
          '</b></p>' +
          '<p>Kindly use the OTP code for reset password.</p>' +
          '<p>' +
          process.env.APPNAME +
          ',' +
          '</p>' +
          '</div>';

        await Mail.sendmail(res, find_user.email, subject, mailbody);
      } else {
        var telnyx = require('telnyx')(constant.TELNYX_DETAILS.API_KEY);
        telnyx.messages.create(
          {
            from: constant.TELNYX_DETAILS.PRIMARY_NUMBER, // Your Telnyx number
            to: '+' + find_user.country_code + find_user.phone,
            text:
              'Your KyuleBag Forgot Password OTP is ' +
              get_otp +
              '. Kindly use the OTP code for reset password.',
          },
          function (err, response) {
            // asynchronously called
            console.log(response);
          }
        );
      }
      await User.update(
        {
          verification_code: get_otp,
        },
        { where: { user_id: find_user.user_id } }
      );
      return { find_user, type };
    }
  },

  /* Verify Code */
  async verify_code(req, res) {
    const body = req.body;

    var findUser = await User.findOne({
      where: {
        user_id: body.userId,
      },
    });

    if (findUser) {
      await User.update(
        {
          verification_code: body.verifyCode,
        },
        { where: { user_id: findUser.user_id } }
      );
      return 0;
    } else {
      return 1;
    }
  },

  /* Check User */
  async check_user(req, res) {
    let { email, googleId } = req.body;
    let find_user = await User.findOne({
      where: {
        email: email,
        google_id: googleId,
      },
    });

    const userExist = find_user ? true : false;

    return userExist;
  },

  /* Verify User */
  async verify_user(req, res) {
    var findUser = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });

    if (findUser) {
      await User.update(
        {
          is_verified: '1',
        },
        { where: { user_id: findUser.user_id } }
      );
      return 0;
    } else {
      return 1;
    }
  },

  /* reset_password */
  async reset_password(req, res) {
    let { new_password, user_id, otp } = req.body;
    let find_user = await User.findOne({
      where: {
        user_id: user_id,
        // verification_code: otp,
      },
      attributes: [
        'email',
        'first_name',
        'last_name',
        'user_id',
        'password',
        'verification_code',
      ],
    });
    if (!find_user) {
      return 2;
    } else {
      if (find_user.verification_code !== otp) {
        return 3;
      }
      let encrypted_password = await Bcryptjs.hash(new_password, generatedSalt);
      let updateOtp = await User.update(
        { password: encrypted_password, verification_code: null },
        {
          where: {
            user_id: find_user.user_id,
          },
        }
      );
      return 1;
    }
  },

  /* verify_email */
  async verify_email(req, res) {
    let find_user = await User.findOne({
      where: {
        verification_code: req.params.code,
      },
      attributes: ['email', 'first_name', 'last_name', 'user_id'],
    });
    if (!find_user) {
      return 2;
    } else {
      let updateCode = await User.update(
        { verification_code: null, email_verified: '1', status: 'active' },
        {
          where: {
            user_id: find_user.user_id,
          },
        }
      );
      return 1;
    }
  },

  async edit_profile(req, res) {
    let { name, email, phone, country_code } = req.body;

    // email =
    //   email === undefined || email === ""
    //     ? undefined
    //     : email;
    // phone =
    //   phone === undefined || phone === ""
    //     ? undefined
    //     : phone;
    // country_code =
    //   country_code === undefined || country_code === ""
    //     ? undefined
    //     : country_code;

    let find_user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });
    if (find_user === null) {
      return 0;
    } else {
      let existUser;
      if (email) {
        existUser = await User.findOne({
          where: {
            email: email,
            is_deleted: '0',
          },
        });
      }

      if (existUser && existUser.user_id !== find_user.user_id) {
        return 1;
      }

      if (phone) {
        existUser = await User.findOne({
          where: {
            phone: phone,
            is_deleted: '0',
          },
        });
      }

      if (existUser && existUser.user_id !== find_user.user_id) {
        return 1;
      }

      let existAlternative = await AlternativeEmail.findOne({
        where: {
          [Op.or]: [
            { email: { [Op.like]: `%${email}%` } },
            {
              [Op.and]: [
                { phone_number: { [Op.like]: `%${phone}%` } },
                { country_code: { [Op.like]: `%${country_code}%` } },
              ],
            },
          ],
        },
      });

      if (existAlternative) {
        return 2;
      }

      email = email === undefined || email === '' ? undefined : email;
      phone = phone === undefined || phone === '' ? undefined : phone;
      country_code =
        country_code === undefined || country_code === ''
          ? undefined
          : country_code;

      const updateObj = {
        first_name: name,
        email: email,
        phone: phone,
        country_code: country_code,
      };

      let changeEmailAndPhone = false;
      if (find_user.email !== email) {
        let get_otp = randomStringHelper.generateOtp();
        get_otp = (get_otp + 7832) * 7832;

        var subject = 'Kyulebag : Verify your account';
        // let url = process.env.WEBURL + "user/verify_email/" + get_otp;
        let url = process.env.WEBPAGEURL + 'verification/' + get_otp;
        var mailbody =
          '<div>' +
          '<p>Hello ' +
          find_user.first_name +
          ',' +
          '</p>' +
          '<p>Please confirm your email by clicking link <a href=' +
          url +
          '>Click here</a> to verify your email.</p>' +
          '<p>' +
          process.env.APPNAME +
          ',' +
          '</p>' +
          '</div>';
        await Mail.sendmail(res, email, subject, mailbody);

        updateObj['verification_code'] = get_otp;
        updateObj['email_verified'] = '0';

        changeEmailAndPhone = true;
      }

      if (find_user.phone !== phone) {
        updateObj['is_verified'] = '0';

        changeEmailAndPhone = true;
      }

      if (changeEmailAndPhone) {
        await UserToken.destroy({
          where: {
            user_id: req.user_id,
          },
        });
      }

      await User.update(updateObj, { where: { user_id: req.user_id } });

      if (req.file) {
        if (find_user.photo) {
          await gcpDelete(
            constant.GCP_BUCKET_FOLDER +
              constant.GCP_USER_FOLDER +
              req.user_id +
              '/' +
              find_user.photo
          );
        }
        let data = await gcpUpload(
          req.file,
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            req.user_id +
            '/' +
            req.file.originalname
        );
        await User.update(
          {
            photo: req.file.originalname,
          },
          { where: { user_id: req.user_id } }
        );
      }

      let find_user_details = await User.findOne({
        where: {
          user_id: req.user_id,
        },
        attributes: {
          exclude: ['password'],
        },
      });
      if (find_user_details === null) {
        return 0;
      } else {
        if (find_user_details.photo) {
          // find_user_details.photo =
          //   constant.GCP_URL +
          //   constant.GCP_BUCKET_NAME +
          //   "/" +
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   find_user_details.user_id +
          //   "/" +
          //   find_user_details.photo;
          find_user_details.photo = await gcpUtils.getPrivateUrl(
            constant.GCP_BUCKET_NAME,
            constant.GCP_BUCKET_FOLDER +
              constant.GCP_USER_FOLDER +
              find_user_details.user_id +
              '/' +
              find_user_details.photo
          );
        }

        find_user_details.dataValues.update_phone_email = changeEmailAndPhone;
        return find_user_details;
      }
    }
  },

  /* change_password */
  async change_password(req, res) {
    let { old_password, new_password, user_id } = req.body;
    let find_user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
      attributes: ['user_id', 'password'],
    });
    if (!find_user) {
      return 3;
    } else {
      if (!Bcryptjs.compareSync(old_password, find_user.password)) {
        // check password
        return 2; //password not valid
      }
      let encrypted_new_password = await Bcryptjs.hash(
        new_password,
        generatedSalt
      );
      let updateOtp = await User.update(
        { password: encrypted_new_password },
        {
          where: {
            user_id: find_user.user_id,
          },
        }
      );
      return 1;
    }
  },

  async set_web_link_preview(req, res) {
    let { user_id, set_web_link_preview } = req.body;
    let user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      await User.update(
        {
          is_web_link_preview_set: set_web_link_preview,
        },
        {
          where: {
            user_id: user.user_id,
          },
        }
      );
      var is_web_link_preview_sets = await User.findOne({
        where: {
          user_id: req.user_id,
        },
      });
      let decryptionText = is_web_link_preview_sets.is_web_link_preview_set
        ? 'Web link preview enabled.'
        : 'Web link preview disabled.';
      var temp = {};
      temp.is_web_link_preview_set =
        is_web_link_preview_sets.dataValues.is_web_link_preview_set;
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: 'Change Web link preview',
        // description:`Web link preview set to ${is_web_link_preview_sets.is_web_link_preview_set} from ${user.is_web_link_preview_set} for user ${is_web_link_preview_sets.user_id}.`
        description: decryptionText,
      });

      return temp;
    }
  },

  /** set AI */
  async set_AI(req, res) {
    let { user_id, AI_set } = req.body;
    let user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      await User.update(
        {
          AI_set: AI_set,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );
      var AI_sets = await User.findOne({
        where: {
          user_id: req.user_id,
        },
      });

      let decryptionText = AI_sets.AI_set
        ? 'Object detection enabled.'
        : 'Object detection disabled.';

      var temp = {};
      temp.AI_set = AI_sets.dataValues.AI_set;
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: 'Change AI set',
        // description: `AI set to ${AI_sets.AI_set} from ${user.AI_set} for user.`
        description: decryptionText,
      });

      return temp;
    }
  },

  /*Storage used by particular user*/
  async storage(req, res) {
    var sum = 0;
    var user = await User.findOne({
      where: {
        user_id: req.user_id,
        is_deleted: '0',
      },
    });

    if (user) {
      const userTotalStorage = user.total_storage
        ? user.total_storage
        : 5242880;
      var image_size = await Images.findAll({
        where: {
          user_id: user.user_id,
          is_deleted: 0,
        },
      });
      image_size.map((data) => {
        sum = sum + data.dataValues.size;
      });
      await User.update(
        {
          used_storage: sum,
          total_storage: userTotalStorage,
        },
        {
          where: {
            user_id: req.user_id,
            is_deleted: '0',
          },
        }
      );
      var storageUsed = await User.findOne(
        {
          where: {
            user_id: req.user_id,
            is_deleted: '0',
          },
        },
        {
          attributes: {
            include: ['used_storage', 'total_storage', 'extra_storage'],
          },
        }
      );
      var temp = {};
      temp.user_id = storageUsed.user_id;

      // let imgs = imageSize.formatSize(storageUsed.used_storage * 1000);
      // let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;
      // storageUsed.used_storage === 0 ?
      temp.used_storage =
        Math.round(
          Number((storageUsed.used_storage * 1000) / 1073741824) * 100
        ) /
          100 +
        ' ' +
        'GB';

      // :temp.used_storage = b.toString() + " " + imgs.split(" ")[1];

      // function to convert bytes into kb,Mb,Gb,Tb
      const totalStorage =
        storageUsed.total_storage + storageUsed.extra_storage;
      let imgss = imageSize.formatSize(totalStorage * 1000);
      let bb = Math.round(Number(imgss.split(' ')[0]));
      temp.total_storage = bb.toString() + ' ' + imgss.split(' ')[1];

      const totalKbStorage =
        storageUsed.total_storage + storageUsed.extra_storage;
      temp.used_kb_storage = storageUsed.used_storage;
      temp.total_kb_storage = totalKbStorage;

      return temp;
    }
  },

  /* user subscription create */
  async subscription(req, res) {
    const userId = req.user_id;
    const body = req.body;
    // const userActivePlan = await UserSubscription.findOne({
    //   where: {
    //     user_id: userId,
    //     is_active: "1",
    //     is_expired: "0",
    //     is_canceled: "0",
    //   },
    // });

    // const userActivePlanPromise = UserSubscription.findOne({
    //   where: {
    //     user_id: userId,
    //     is_active: "1",
    //     is_expired: "0",
    //     is_canceled: "0",
    //   },
    //   order: [['createdAt', 'DESC']]
    // });

    const subscriptionPlanPromise = NewStoragePlan.findOne({
      where: { subscription_id: body.subscriptionId },
    });

    const userPromise = User.findOne({
      where: { user_id: userId },
    });

    const [/*userActivePlan,*/ subscriptionPlan, user] = await Promise.all([
      /*userActivePlanPromise,*/ subscriptionPlanPromise,
      userPromise,
    ]);

    if (!user) {
      return;
    }

    return subscriptionPlan;

    // const startDate = moment.utc(parseInt(body.purchaseTime)).format("YYYY-MM-DD");
    // const endDate = moment.utc(startDate).add(1, body.subscriptionType).subtract(1, 'seconds');

    // if (userActivePlan) {
    //   await UserSubscription.update(
    //     {
    //       is_active: "0",
    //       is_expired: "1",
    //     },
    //     {
    //       where: {
    //         // user_subscription_id: userActivePlan.user_subscription_id,
    //         user_id: userId,
    //         is_active: "1",
    //       }
    //     }
    //   );
    // }

    // const subscriptionPlan = await StoragePlan.findOne({
    //   where: {
    //     subscription_id: body.subscriptionId
    //   },
    // });

    // let extraStorageKbValue = 0;
    // if (subscriptionPlan) {

    //   const regex = /(\d+)\s*(KB|MB|GB|TB|kb|mb|gb|tb)/i;
    //   const match = subscriptionPlan.storage.match(regex);

    //   if (match) {
    //     const value = match[1];
    //     const intValue = parseInt(value);
    //     const unit = match[2].toUpperCase();

    //     switch (unit) {
    //       case 'KB':
    //         extraStorageKbValue = intValue;
    //         break
    //       case 'MB':
    //         extraStorageKbValue = (intValue * 1000);
    //         break
    //       case 'GB':
    //         extraStorageKbValue = (intValue * 1000 * 1000);
    //         break
    //       case 'TB':
    //         extraStorageKbValue = (intValue * 1000 * 1000 * 1000);
    //         break
    //       default:
    //         extraStorageKbValue = (intValue * 1000 * 1000);
    //         break
    //     }
    //   }
    // }
    // const UserSubscriptionCreate = await UserSubscription.create({
    //   user_id: userId,
    //   subscription_id: body.subscriptionId,
    //   transaction_id: body.orderId,
    //   payment_amount: body.subscriptionAmount,
    //   purchase_token: body.purchaseToken,
    //   start_date: startDate,
    //   end_date: endDate,
    //   status: body.purchaseStatus,
    //   is_active: true,
    //   is_expired: false,
    //   is_canceled: false,
    // });

    // const userSubscriptionCreatePromise = UserSubscription.create({
    //   user_id: userId,
    //   subscription_id: body.subscriptionId,
    //   transaction_id: body.orderId,
    //   payment_amount: body.subscriptionAmount,
    //   purchase_token: body.purchaseToken,
    //   start_date: startDate,
    //   end_date: endDate,
    //   status: body.purchaseStatus,
    //   is_active: true,
    //   is_expired: false,
    //   is_canceled: false,
    // })

    // const upgradeUserExtraStorage = user.extra_storage + extraStorageKbValue;
    // const userUpdatePromise = User.update(
    //   { extra_storage: upgradeUserExtraStorage },
    //   { where: { user_id: userId } }
    // );

    // const [userSubscriptionCreate] = await Promise.all([userSubscriptionCreatePromise, userUpdatePromise]);

    // return userSubscriptionCreate;
  },

  /* update subscription */
  async update_subscription(req, res) {
    const userId = req.user_id;
    const { transactionId, purchaseToken, isCancel } = req.body;
    const currantDate = moment().utc();
    let userActivePlan = {};
    let filter = {
      user_id: userId,
    };
    if (isCancel === 'true') {
      if (purchaseToken) {
        filter.purchase_token = purchaseToken;
      }
    } else {
      filter.purchase_token = purchaseToken;
    }

    userActivePlan = await UserSubscription.findOne({
      where: filter,
      order: [['createdAt', 'DESC']],
    });

    if (!userActivePlan) {
      return 1;
    }

    const subscriptionPlan = await StoragePlan.findOne({
      where: {
        subscription_id: userActivePlan.subscription_id,
      },
    });

    const startDate = moment.utc(userActivePlan.start_date).add(1, 'month');
    const endDate = moment
      .utc(startDate)
      .add(1, subscriptionPlan.duration === 'monthly' ? 'month' : 'year')
      .startOf('day')
      .subtract(1, 'seconds');

    if (userActivePlan && currantDate > userActivePlan.end_date) {
      console.log('1099');

      if (isCancel === 'true') {
        console.log('1159');

        await Promise.all([
          UserSubscription.update(
            { is_active: '0', is_expired: '1', is_canceled: '1' },
            {
              where: {
                user_subscription_id: userActivePlan.user_subscription_id,
              },
            }
          ),
          User.update({ extra_storage: 0 }, { where: { user_id: userId } }),
        ]);
      } else if (isCancel !== 'true') {
        console.log('1169');

        await Promise.all([
          UserSubscription.update(
            { is_active: '0', is_expired: '1' },
            {
              where: {
                user_subscription_id: userActivePlan.user_subscription_id,
              },
            }
          ),
          UserSubscription.create({
            user_id: userId,
            subscription_id: userActivePlan.subscription_id,
            transaction_id: transactionId,
            payment_amount: userActivePlan.payment_amount,
            purchase_token: purchaseToken,
            start_date: startDate,
            end_date: endDate,
            status: userActivePlan.status,
            is_active: true,
            is_expired: false,
            is_canceled: false,
          }),
        ]);
      }
    } else if (userActivePlan && currantDate < userActivePlan.end_date) {
      console.log('1192');

      if (isCancel === 'true') {
        console.log('1195');

        await Promise.all([
          UserSubscription.update(
            { is_active: '0', is_canceled: '1' },
            {
              where: {
                user_subscription_id: userActivePlan.user_subscription_id,
              },
            }
          ),
          // User.update({ extra_storage: 0 }, { where: { user_id: userId } })
        ]);
      } else if (isCancel !== 'true') {
        console.log('1205');

        await UserSubscription.create({
          user_id: userId,
          subscription_id: userActivePlan.subscription_id,
          transaction_id: transactionId,
          payment_amount: userActivePlan.payment_amount,
          purchase_token: purchaseToken,
          start_date: startDate,
          end_date: endDate,
          status: userActivePlan.status,
          is_active: true,
          is_expired: false,
          is_canceled: false,
        });
      }
      return 1;
    }

    return 1;
  },

  /* ai subscription */
  async ai_subscription(req, res) {
    const userId = req.user_id;
    const {
      aiSubscriptionId,
      orderId,
      purchaseToken,
      subscriptionAmount,
      purchaseStatus,
    } = req.body;

    const aiSubscriptionPlan = await NewAIStoragePlan.findOne({
      where: { ai_subscription_id: aiSubscriptionId },
    });

    if (!aiSubscriptionPlan) {
      return 1;
    }

    const userDetails = await User.findOne({
      where: {
        user_id: userId,
      },
    });

    await Promise.all([
      User.update(
        {
          total_ai_api:
            userDetails.total_ai_api + aiSubscriptionPlan.number_of_api,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      ),
      UserAISubscription.create({
        user_id: userId,
        ai_subscription_id: aiSubscriptionId,
        transaction_id: orderId,
        purchase_token: purchaseToken,
        payment_amount: subscriptionAmount,
        status: purchaseStatus,
      }),
      Activity_logs.create({
        user_id: userId,
        title: 'AI Subscription',
        description: `You have purchased a credit of ${aiSubscriptionPlan?.price} ${aiSubscriptionPlan?.price_currency_code} which will be used for AI related functions.`,
      }),
    ]);

    return 0;
  },

  /* cancel subscription */
  async cancel_subscription(req, res) {
    const userId = req.user_id;
    const body = req.body;
    const userActivePlan = await UserSubscription.findOne({
      where: {
        user_id: userId,
        subscription_id: body.subscriptionId,
        transaction_id: body.orderId,
        is_active: '1',
        is_expired: '0',
        is_canceled: '0',
      },
    });

    const currantDate = moment.utc();

    if (userActivePlan) {
      await UserSubscription.update(
        {
          is_active: '0',
          is_canceled: '1',
          canceled_date: currantDate,
        },
        {
          where: {
            subscription_id: body.subscriptionId,
            transaction_id: body.orderId,
          },
        }
      );

      return 0;
    }
    return 1;
  },

  /**alternative email and phone */
  async alternative_email(req, res) {
    let { alternative_email, alternative_phone, country_code } = req.body;

    let find_user = await AlternativeEmail.findOne({
      where: {
        user_id: req.user_id,
      },
    });

    var userDetails = await User.findOne({
      where: {
        user_id: req.user_id,
        is_deleted: '0',
      },
    });

    var existUser = await User.findOne({
      where: {
        [Op.or]: [
          { email: { [Op.like]: `%${alternative_email}%` } },
          {
            [Op.and]: [
              { phone: { [Op.like]: `%${alternative_phone}%` } },
              { country_code: { [Op.like]: `%${country_code}%` } },
            ],
          },
        ],
      },
    });

    var existAlternative = await AlternativeEmail.findOne({
      where: {
        [Op.or]: [
          { email: { [Op.like]: `%${alternative_email}%` } },
          {
            [Op.and]: [
              { phone_number: { [Op.like]: `%${alternative_phone}%` } },
              { country_code: { [Op.like]: `%${country_code}%` } },
            ],
          },
        ],
      },
    });

    if (
      userDetails.email === alternative_email ||
      (userDetails.phone === alternative_phone &&
        userDetails.country_code === country_code)
    ) {
      //   if (userDetails.email === alternative_email) {
      //     return 0;
      //   } else if (userDetails.phone === alternative_phone && userDetails.country_code === country_code) {
      //     return 1;
      //   }
      // } else if (((userDetails.email === alternative_email) && (userDetails.phone === alternative_phone && userDetails.country_code === country_code))) {
      // return { type: 2 };
      if (alternative_email) {
        return { type: 0 };
      } else if (alternative_phone) {
        return { type: 2 };
      }
    } else if (existUser && existUser.user_id != req.user_id) {
      // return { type: 3 };
      if (alternative_email) {
        return { type: 3 };
      } else if (alternative_phone) {
        return { type: 4 };
      }
    } else if (existAlternative && existAlternative.user_id != req.user_id) {
      // return { type: 4 };
      if (alternative_email) {
        return { type: 7 };
      } else if (alternative_phone) {
        return { type: 8 };
      }
    }

    if (find_user) {
      if (alternative_phone) {
        let alternative = await AlternativeEmail.update(
          {
            phone_number: alternative_phone,
            country_code: country_code,
          },
          {
            where: {
              user_id: req.user_id,
            },
          }
        );
        let update_user = await AlternativeEmail.findOne({
          where: {
            user_id: req.user_id,
          },
        });
        if (find_user.phone_number) {
          return { type: 9, data: update_user };
        } else {
          return { type: 5, data: alternative };
        }
      } else {
        let alternative = await AlternativeEmail.update(
          {
            email: alternative_email,
          },
          {
            where: {
              user_id: req.user_id,
            },
          }
        );
        let update_user = await AlternativeEmail.findOne({
          where: {
            user_id: req.user_id,
          },
        });

        if (find_user.email) {
          return { type: 10, data: update_user };
        } else {
          return { type: 6, data: alternative };
        }
      }
    } else {
      var alternative = await AlternativeEmail.create({
        user_id: req.user_id,
        email: alternative_email,
        country_code: country_code,
        phone_number: alternative_phone,
      });

      if (alternative_email) {
        return { type: 6, data: alternative };
      } else if (alternative_phone) {
        return { type: 5, data: alternative };
      }
    }

    return alternative;
  },

  /** Delete alternative email and phone */
  async delete_alternative(req, res) {
    let { type } = req.query;

    let find_user = await AlternativeEmail.findOne({
      where: {
        user_id: req.user_id,
      },
    });

    if (!find_user?.email && type === 'EMAIL') {
      return 0;
    } else if (!find_user?.phone_number && type === 'PHONE') {
      return 1;
    }

    if (type === 'EMAIL' && find_user.email) {
      if (!find_user.phone_number) {
        await AlternativeEmail.destroy({
          where: {
            user_id: find_user.user_id,
          },
        });
      } else {
        await AlternativeEmail.update(
          {
            email: null,
          },
          {
            where: {
              user_id: find_user.user_id,
            },
          }
        );
      }

      return 2;
    } else if (type === 'PHONE' && find_user.phone_number) {
      if (!find_user.email) {
        await AlternativeEmail.destroy({
          where: {
            user_id: find_user.user_id,
          },
        });
      } else {
        await AlternativeEmail.update(
          {
            phone_number: null,
            country_code: null,
          },
          {
            where: {
              user_id: find_user.user_id,
            },
          }
        );
      }

      return 3;
    }
  },

  /**
   * set the OCR true or false
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setOCR(req, res) {
    let { OCR_set } = req.body;
    let user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      const data = await User.update(
        {
          OCR_set,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );

      var OCR_sets = await User.findOne({
        where: {
          user_id: req.user_id,
        },
      });

      let decryptionText = OCR_sets.OCR_set
        ? 'Text detection enabled.'
        : 'Text detection disabled.';

      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: 'Change OCR set',
        description: decryptionText,
      });

      return data;
    }
  },

  /**
   * set AI confidence level
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setAIConfidenceLevel(req, res) {
    let { AI_confidence_level } = req.body;
    let user = await User.count({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      return await User.update(
        {
          AI_confidence_level,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );
    }
  },

  /**
   * user AI API count
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async userAICount(req, res) {
    return await User.findOne({
      where: {
        user_id: req.user_id,
      },
      attributes: ['user_id', 'total_ai_api', 'used_ai_api'],
    });
  },

  /**
   * Subscription Storage and AI Products Plan ID API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async activeSubscriptionProductId(req, res) {
    let storageProductIdArray = await NewStoragePlan.findAll({
      where: {
        is_deleted: '0',
        is_free: '0',
        status: 'active',
      },
      attributes: ['subscriptions_product_id'],
    });

    storageProductIdArray = storageProductIdArray.map(
      (data) => data.subscriptions_product_id
    );

    let aiProductIdArray = await NewAIStoragePlan.findAll({
      where: {
        is_deleted: '0',
        is_free: '0',
        status: 'active',
      },
      attributes: ['ai_subscription_product_id'],
    });

    aiProductIdArray = aiProductIdArray.map(
      (data) => data.ai_subscription_product_id
    );

    return { storageProductIdArray, aiProductIdArray };
  },

  /**
   * Storage Subscription Plan API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async storageSubscriptionPlan(req, res) {
    const userId = req.user_id;
    const storagePlan = await StoragePlan.findAll({
      where: {
        is_deleted: '0',
        status: 'active',
      },
      attributes: [
        'subscription_id',
        'storage',
        'price',
        'duration',
        'base_plan_id',
        'price_amount_micros',
      ],
    });

    const userActivePlan = await UserSubscription.findOne({
      where: {
        user_id: userId,
        // is_active: "1",
        // is_expired: "0",
        // is_canceled: "0",
        [Op.or]: [{ is_active: '1' }, { is_canceled: '1' }],
      },
      order: [['createdAt', 'DESC']],
    });

    storagePlan.map((data) => {
      if (userActivePlan?.subscription_id === data.subscription_id) {
        if (userActivePlan.is_active === '1' || userActivePlan.is_active) {
          const endDate = userActivePlan.end_date
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          data.dataValues.is_active = true;
          data.dataValues.purchase_token = userActivePlan.purchase_token;
          data.dataValues.end_date = endDate;
        } else if (
          (userActivePlan.is_expired === '0' || !userActivePlan.is_expired) &&
          (userActivePlan.is_canceled === '1' || userActivePlan.is_canceled)
        ) {
          const endDate = userActivePlan.end_date
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          data.dataValues.is_active = false;
          data.dataValues.purchase_token = userActivePlan.purchase_token;
          data.dataValues.end_date = endDate;
        } else {
          data.dataValues.is_active = false;
        }
        // const endDate = userActivePlan.end_date
        //   .toISOString()
        //   .substr(0, 19)
        //   .replace("T", " ");
        // data.dataValues.is_active = true
        // data.dataValues.purchase_token = userActivePlan.purchase_token
        // data.dataValues.end_date = endDate
      } else {
        data.dataValues.is_active = false;
      }
    });

    return storagePlan;
  },

  /**
   * Storage Subscription Plan API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async newStorageSubscriptionPlan(req, res) {
    const userId = req.user_id;

    const storagePlan = await NewStoragePlan.findAll({
      where: {
        is_deleted: '0',
        is_free: '0',
        status: 'active',
      },
      attributes: [
        'subscription_id',
        'subscriptions_product_id',
        'title',
        'subscriptions_benefits',
        'base_plan_id',
        'duration',
        'price',
        'price_amount_micros',
        'price_currency_code',
      ],
    });

    const userActivePlan = await UserSubscription.findOne({
      where: {
        user_id: userId,
        // is_active: "1",
        // is_expired: "0",
        // is_canceled: "0",
        [Op.or]: [{ is_active: '1' }, { is_canceled: '1' }],
      },
      order: [['createdAt', 'DESC']],
    });

    storagePlan.map((data) => {
      if (userActivePlan?.subscription_id === data.subscription_id) {
        if (
          (userActivePlan.is_active === '1' || userActivePlan.is_active) &&
          (userActivePlan.is_expired === '0' || !userActivePlan.is_expired) &&
          (userActivePlan.is_canceled === '0' || !userActivePlan.is_canceled)
        ) {
          const endDate = userActivePlan.end_date
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          data.dataValues.is_active = true;
          data.dataValues.purchase_token = userActivePlan.purchase_token;
          data.dataValues.end_date = endDate;
          data.dataValues.is_canceled = false;
        } else if (
          (userActivePlan.is_active === '1' || userActivePlan.is_active) &&
          (userActivePlan.is_expired === '0' || !userActivePlan.is_expired) &&
          (userActivePlan.is_canceled === '1' || userActivePlan.is_canceled)
        ) {
          const endDate = userActivePlan.end_date
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          data.dataValues.is_active = true;
          data.dataValues.purchase_token = userActivePlan.purchase_token;
          data.dataValues.end_date = endDate;
          data.dataValues.is_canceled = true;
        } else {
          data.dataValues.is_active = false;
          data.dataValues.is_canceled = true;
        }
        // const endDate = userActivePlan.end_date
        //   .toISOString()
        //   .substr(0, 19)
        //   .replace("T", " ");
        // data.dataValues.is_active = true
        // data.dataValues.purchase_token = userActivePlan.purchase_token
        // data.dataValues.end_date = endDate
      } else {
        data.dataValues.is_active = false;
        data.dataValues.is_canceled = true;
      }
    });

    return storagePlan;
  },

  /**
   * AI Subscription API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async subscriptionPlan(req, res) {
    return await AIStoragePlan.findAll({
      where: {
        is_deleted: '0',
        price: { [Op.ne]: 'Free' },
      },
    });
  },

  /**
   * AI Subscription API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async newSubscriptionPlan(req, res) {
    const aiSubscriptionPlan = await NewAIStoragePlan.findAll({
      where: {
        is_deleted: '0',
        is_free: '0',
      },
    });

    const aiDeduction = await TokenDeductions.findOne();
    const description = `Each image processed will deduct ${aiDeduction.token_per_image} tokens, and each second of video processed will deduct ${aiDeduction.token_per_video_second} tokens from your balance.`;

    return { aiSubscriptionPlan, description };
  },

  /**
   * set push notification status
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setNotificationStatus(req, res) {
    let { push_notification } = req.body;
    let user = await User.count({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      return await User.update(
        {
          push_notification,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );
    }
  },

  /**
   * set biometric authentication
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setBiometricAuthentication(req, res) {
    let { biometric_authentication } = req.body;
    let user = await User.count({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      return await User.update(
        {
          biometric_authentication,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );
    }
  },

  /**
   * manage AI label visibility
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async hideAILabel(req, res) {
    let { hide_AI_label } = req.body;
    let user = await User.count({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      return await User.update(
        {
          hide_AI_label,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );
    }
  },

  /**
   * manage OCR label visibility
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async hideOCRLabel(req, res) {
    let { hide_OCR_label } = req.body;
    let user = await User.count({
      where: {
        user_id: req.user_id,
      },
    });
    if (user) {
      return await User.update(
        {
          hide_OCR_label,
        },
        {
          where: {
            user_id: req.user_id,
          },
        }
      );
    }
  },

  /**
   * User hard delete
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async userDelete(req, res) {
    let userId = req.user_id;
    let promise = [];

    let userItems = await Items.findAll({
      where: {
        user_id: userId,
      },
    });

    if (userItems.length) {
      for (let index = 0; index < userItems.length; index++) {
        const itemId = userItems[index].item_id;
        const allPromise = [];

        // delete user comment from tbl_item_nots
        allPromise.push(
          ItemNotes.destroy({
            where: {
              item_id: itemId,
            },
          })
        );

        // delete user comment from tbl_item_ocr_labels
        allPromise.push(
          OCRLabels.destroy({
            where: {
              item_id: itemId,
            },
          })
        );

        // delete user comment from tbl_item_tags
        allPromise.push(
          ItemTags.destroy({
            where: {
              item_id: itemId,
            },
          })
        );

        // delete user from tbl_media
        allPromise.push(
          Images.destroy({
            where: {
              item_id: itemId,
            },
          })
        );

        // delete user from tbl_sharing_details
        allPromise.push(
          ItemSharingDetails.destroy({
            where: {
              item_id: itemId,
            },
          })
        );

        await Promise.all(allPromise);
      }
    }

    // delete user tbl_activity_logs
    promise.push(
      Activity_logs.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_alternative_emails
    promise.push(
      AlternativeEmail.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user comment from tbl_announcements
    promise.push(
      AnnouncementComment.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_tag_names
    promise.push(
      TagNames.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_user_subscriptions
    promise.push(
      UserSubscription.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_user_ai_subscriptions
    promise.push(
      UserAISubscription.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_items
    promise.push(
      Items.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_user_tokens
    promise.push(
      UserToken.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    // delete user from tbl_users
    promise.push(
      User.destroy({
        where: {
          user_id: userId,
        },
      })
    );

    promise.push(
      gcpUtils.deleteGCPFolder(
        constant.GCP_BUCKET_FOLDER +
          constant.GCP_USER_FOLDER +
          userId +
          '/' +
          constant.GCP_ITEM_FOLDER
      )
    );

    await Promise.all(promise);

    return;
  },

  /**
   * Use Payment History
   * @param {*} req
   * @param {*} res
   * @return
   */
  async paymentHistory(req, res) {
    const userId = req.user_id;
    const body = req.query;
    const page = body.page === undefined ? 1 : body.page;
    const offset = (page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    if (body.type === constant.PAYMENT_HISTORY_TYPE.STORAGE) {
      const paymentHistory = await UserSubscription.findAndCountAll({
        where: {
          user_id: userId,
        },
        include: [
          {
            model: NewStoragePlan,
            attributes: [],
          },
        ],
        attributes: [
          ['user_subscription_id', 'id'],
          'user_id',
          'transaction_id',
          'payment_amount',
          'subscription_id',
          [
            Sequelize.col('tbl_new_subscription.subscriptions_product_id'),
            'subscription_product_id',
          ],
          [Sequelize.col('tbl_new_subscription.title'), 'subscription_title'],
          [
            Sequelize.col('tbl_new_subscription.subscriptions_benefits'),
            'subscription_description',
          ],
          [
            Sequelize.col('tbl_new_subscription.duration'),
            'subscription_duration',
          ],
          [
            Sequelize.col('tbl_new_subscription.base_plan_id'),
            'subscription_base_plan_id',
          ],
          [
            Sequelize.col('tbl_new_subscription.price_currency_code'),
            'subscription_price_currency_code',
          ],
          'is_canceled',
          'createdAt',
        ],
        order: [['createdAt', 'DESC']],
        offset: offset,
        limit: limit,
      });

      return paymentHistory;
    } else if (body.type === constant.PAYMENT_HISTORY_TYPE.AI) {
      const paymentHistory = await UserAISubscription.findAndCountAll({
        where: {
          user_id: userId,
        },
        include: [
          {
            model: NewAIStoragePlan,
            attributes: [],
          },
        ],
        attributes: [
          ['user_ai_subscription_id', 'id'],
          'user_id',
          'transaction_id',
          'payment_amount',
          'ai_subscription_id',
          [
            Sequelize.col('tbl_new_ai_subscription.ai_subscription_product_id'),
            'subscription_product_id',
          ],
          [
            Sequelize.col('tbl_new_ai_subscription.title'),
            'subscription_title',
          ],
          [
            Sequelize.col('tbl_new_ai_subscription.description'),
            'subscription_description',
          ],
          [
            Sequelize.col('tbl_new_ai_subscription.number_of_api'),
            'subscription_number_of_api',
          ],
          [
            Sequelize.col('tbl_new_ai_subscription.price_currency_code'),
            'subscription_price_currency_code',
          ],
          'createdAt',
        ],
        order: [['createdAt', 'DESC']],
        offset: offset,
        limit: limit,
      });

      return paymentHistory;
    }
  },
};
