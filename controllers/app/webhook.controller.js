
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const webhookService = require("../../services/app/webhook.service");

module.exports = {
  /* add */
  async webhook(req, res) {
    try {
        await webhookService.webhook(req, res);

        return res;
    } catch (error) {
      console.log("Webhook -> error", error);
      // res.status(status.errorStatus).send({
      //   data: [],
      //   message: message.INTERNALSERVERERROR,
      //   status: status.ERROR,
      // });
    }
  },

};