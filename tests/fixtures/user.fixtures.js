/**
 * User Test Fixtures
 * Common test data for user-related tests
 */

// Base user data templates
const baseUserData = {
  kyulebagUser: {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    login_type: 'Kyu<PERSON><PERSON>ag',
    phone: '+*********0',
    country_code: '+1',
  },
  
  googleUser: {
    name: '<PERSON>',
    email: '<EMAIL>',
    login_type: 'Google',
    google_id: 'google_12345',
  },
  
  facebookUser: {
    name: '<PERSON>',
    email: '<EMAIL>',
    login_type: 'Facebook',
    facebook_id: 'facebook_67890',
  },
  
  appleUser: {
    name: '<PERSON>',
    email: '<EMAIL>',
    login_type: 'Apple',
    apple_id: 'apple_54321',
  },
};

// Mock request objects
const mockRequests = {
  kyulebagSignup: {
    body: {
      ...baseUserData.kyulebagUser,
      device_token: 'mock-device-token-123',
    },
    headers: {
      device_type: 'mobile',
      version: '1.0.0',
    },
    file: null,
  },
  
  googleSignup: {
    body: {
      ...baseUserData.googleUser,
      device_token: 'google-device-token-456',
    },
    headers: {
      device_type: 'iOS',
      version: '2.1.0',
    },
    file: null,
  },
  
  withProfilePhoto: {
    body: baseUserData.kyulebagUser,
    headers: {
      device_type: 'android',
      version: '1.5.0',
    },
    file: {
      originalname: 'profile-photo.jpg',
      buffer: Buffer.from('fake-image-data'),
      mimetype: 'image/jpeg',
    },
  },
};

// Mock database responses
const mockUsers = {
  created: {
    user_id: 'user_12345',
    dataValues: {
      user_id: 'user_12345',
      first_name: 'John Doe',
      email: '<EMAIL>',
      status: 'active',
      email_verified: '1',
      login_type: 'KyuleBag',
      photo: null,
    },
  },
  
  existing: {
    user_id: 'user_67890',
    email: '<EMAIL>',
    login_type: 'KyuleBag',
    is_deleted: true,
    dataValues: {
      user_id: 'user_67890',
      email: '<EMAIL>',
      login_type: 'KyuleBag',
    },
  },
  
  withPhoto: {
    user_id: 'user_photo_123',
    dataValues: {
      user_id: 'user_photo_123',
      photo: 'profile-photo.jpg',
    },
  },
};

// Mock AI Plan
const mockAIPlan = {
  dataValues: {
    number_of_api: 500,
  },
};

// Mock JWT Tokens
const mockTokens = {
  jwt: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock.token',
  refreshToken: 'refresh.token.mock',
};

// Mock verification codes
const mockCodes = {
  otp: 1234,
  encoded: (1234 + 7832) * 7832, // Following business logic
};

// Mock GCP responses
const mockGCP = {
  uploadSuccess: 'https://storage.googleapis.com/bucket/file.jpg',
  privateUrl: 'https://storage.googleapis.com/bucket/private/file.jpg?signed=true',
};

// Mock email templates
const mockEmailData = {
  subject: 'Kyulebag : Verify your account',
  verificationUrl: 'https://kyulebag.com/verification/*********',
  template: '<div><p>Hello John,</p><p>Please verify your email...</p></div>',
};

// Default tags
const defaultTags = [
  { user_id: 'user_12345', tag_name: 'Important Docs' },
  { user_id: 'user_12345', tag_name: 'Health' },
  { user_id: 'user_12345', tag_name: 'Work' },
];

// Static images mock
const staticImages = [
  'doc-icon.png',
  'image-icon.png',
  'video-icon.png',
  'audio-icon.png',
];

// Helper functions
const createMockRequest = (overrides = {}) => ({
  body: {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'TestPass123!',
    login_type: 'KyuleBag',
    device_token: 'test-device-token',
    ...overrides.body,
  },
  headers: {
    device_type: 'mobile',
    version: '1.0.0',
    ...overrides.headers,
  },
  file: overrides.file || null,
});

const createMockResponse = () => ({
  status: jest.fn().mockReturnThis(),
  send: jest.fn(),
  json: jest.fn(),
});

const createMockUser = (overrides = {}) => ({
  user_id: 'test_user_123',
  dataValues: {
    user_id: 'test_user_123',
    first_name: 'Test User',
    email: '<EMAIL>',
    login_type: 'KyuleBag',
    status: 'active',
    ...overrides,
  },
});

module.exports = {
  baseUserData,
  mockRequests,
  mockUsers,
  mockAIPlan,
  mockTokens,
  mockCodes,
  mockGCP,
  mockEmailData,
  defaultTags,
  staticImages,
  
  // Helper functions
  createMockRequest,
  createMockResponse,
  createMockUser,
}; 