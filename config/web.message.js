/*
 * Summary:     webMessages file for response messages
 * Author:      Openxcell(empCode-N00039)
 */
const appMessage = {
  SUCCESS: "Success",
  INTERNALSERVERERROR: "Error",

  //USER MESSAGES
  // SIGNUPSUCCESS: "Account registered successfully. Please check email and verify your account.",
  SIG<PERSON>PSUCCESS: "Account registered successfully. Please check and verify your email.",
  USERALREADYEXIST: "User already exist with requested email or phone",
  GOOGLEUSERALREADYEXIST: "User already exist with Google type.",
  NORMALUSERALREADYEXIST: "User already exist with Normal type.",
  ALREADYVERIFY: "Account is already verified",
  VERIFIED: "Your email has been verified successfully",
  PLANPURCHASED: "Plan purchased",
  USERNOTFOUND: "User not found",
  EMAILVERIFICATINPENDING: "Check your inbox to verify your mail",
  LOGINSUCCESS: "Login successfully",
  INVALIDPASSWORD: "Invalid password",
  LOGOUTSUCCESFULLY: "Logout successfully",
  // INVALIDOTP: "Invalid otp or not available",
  INVALIDOTP: "Invalid otp",
  VARIFIEDOTP: "OTP verified successfully",
  USERVARIFIED: "Your mobile number has been verified successfully!",
  RESENDOTP: "OTP resend successfully",
  SOMETHINGWRONG: "Something went wrong,Please contact to support team.",
  SENTOTP: "OTP send successfully",
  SENTOTPBYEMAIL: "OTP sent to your email id",
  SENTOTPBYPHONE: "OTP sent to your phone number",
  PASSWORDNOTSAME: "Password and Confirm Password are not same",
  // PASSWORDRESET: "Password reset successfully",
  PASSWORDRESET: "Password Changed",
  PINRESET: "PIN reset successfully",
  OLDPASSWORDNOTSAME: "Your current password does not match.",
  USEROROTPNOTFOUND: "User or OTP not found",
  PROFILERETRIVE: "Profile data retrieve successfully.",
  PROFILEUPDATED: "Profile updated successfully.",
  NOEMAILFOUND: "Requested email already associated with other user.",
  NOUSERORPASSWORD: "Requested user or user provided password not matching.",
  NOUSERFOUND: "No user found.",
  USERINACTIVE: "User is in 'Inactive' state. Please contact support on our website.",
  // DELETEDUSER: "Admin deleted this user, Please contact to admin.",
  DELETEDUSER: "Account does not exist",
  NOTIIFICATIONCHANGES: "Notification changes applied successfully.",
  NOTIIFICATIONRETRIVE: "Notification count retrieve successfully.",
  VERIFICATIOCODENOTVALID: "Verification code expired or not found.",
  VERIFYEMAILACCOUNT: "Please verify your email account.",
  // ACCOUNTNOTFOUND: "Email account not verified or No user found.",
  ACCOUNTNOTFOUND: "User account doesn't exist!",
  ALTERNATIVEADDED: "Alternative email and number add successfully.",
  ALTERNATIVEEMAILADDED: "Alternative email add successfully.",
  ALTERNATIVEEMAILUPDATE: "Alternative email updated successfully.",
  ALTERNATIVEPHONEADDED: "Alternative number add successfully.",
  ALTERNATIVEPHONEUPDATE: "Alternative number updated successfully.",

  ALTERNATIVE_EMAIL_SAME: "You could not add this email as Alternative email. Please try with different email.",
  ALTERNATIVE_PHONE_SAME: "You could not add this phone number as Alternative phone number. Please try with different phone number.",
  // ALTERNATIVE_LOGIN_EMAIL_SAME: "You are logged in with this email so you can not add this email.",
  ALTERNATIVE_LOGIN_EMAIL_SAME: "You're already logged in with this email so can't add it.",
  // ALTERNATIVE_LOGIN_PHONE_SAME: "You are logged in with this phone number so you can not add this phone number.",
  ALTERNATIVE_LOGIN_PHONE_SAME: "You're already logged in with this number so can't add it.",
  USER_EXIST: "This email or phone number is already used so please add another email or phone number.",
  ALTERNATIVE_EXIST: "This email or phone number is already used in alternative so please add another email or phone number.",
  USER_EMAIL_EXIST: "This email is already used so please add another email.",
  // USER_PHONE_EXIST: "This phone number is already used so please add another phone number.",
  USER_PHONE_EXIST: "Phone number already registered. Use a new one.",
  ALTERNATIVE_EMAIL_EXIST: "This email is already used in alternative so please add another email.",
  ALTERNATIVE_PHONE_EXIST: "This phone number is already used in alternative so please add another phone number.",
  ALTERNATIVE_EMAIL_NOT_EXIST: "Alternative email not found.",
  ALTERNATIVE_PHONE_NOT_EXIST: "Alternative phone not found.",
  ALTERNATIVE_EMAIL_DELETE: "Alternative email deleted successfully.",
  ALTERNATIVE_PHONE_DELETE: "Alternative phone deleted successfully.",

  USERSTORAGEFULL: "Insufficient storage space, Please buy storage.",

  //profile messages
  PROFILEPENDING: "Your profile approval is pending",
  PROFILEREJECTED: "Your profile is rejected",
  PROFILEINACTIVE: "Your profile is inactive",
  PROFILEUPDATED: "Your profile is updated",
  PROFILENOTUPDATED: "Your profile is not updated",
  TOKENNOTMATCHED: "Token not matched",
  TOKENREQUIRED: "Authentication token required",
  NORECORDFOUND: "No record found",
  CONTENTRETRIVE: "Content retrieve successfully.",

  //Tags
  TAGSRETRIVE: "Tags retrieve successfully.",
  NOTAGSFOUND: "No any tags found.",
  NOTAGIDFOUND: "Tag id does not exist.",
  TAGADDED: "Tag added successfully.",
  TAGUPDATE: "Tag update successfully.",
  TAGSADDED: "Tag added successfully.",
  TAGSALREADY: "Tag already exists.",
  TAGSEDITED: "Tag edit successfully.",
  TAGEXIST: "Tag already exists with same name.",
  TAGSNOTFOUND: "Requested tags id not found.",
  TAGSDELETED: "Tag deleted successfully.",
  TAGSIDITEMIDNOTFOUND: "Requested item or tag id does not exist.",

  //Notes
  NOTESADDED: "Notes added successfully.",
  NOTESRETRIVE: "Notes retrieve successfully.",
  NONOTESFOUND: "Notes any notes found.",
  NOTESALREADYDEL: "Note already deleted.",
  NOTESALREADYADDDED: "Notes already added.",
  NOTESDELETED: "Notes deleted successfully.",
  NOTESEDITED: "Notes edit successfully.",
  NOTEIDNOTFOUND: "Requested Note id or Item id does not exist.",

  //Comments
  // COMMENTADDED: "Comment added successfully.",
  COMMENTADDED: "Feedback sent.",
  COMMENTNOTADDED: "Not able to add comment.",
  COMMENTLENGTHEXCEED: "Character length of comment has exceeded.",
  COMMENTLIMITEXCEED: "Comment limit has exceeded.",
  COMMENTISDISABLE: "Comment is disabled.",
  COMMENTUPDATED: "Comment updated successfully.",
  COMMENTNOTUPDATED: "Not able to update comment.",

  //Items
  ITEMALREDYADDED: "Item already added with this title.",
  ITEMADDED: "Item added successfully.",
  ITEMRETRIVED: "Item retrieve successfully.",
  NOITEMFOUND: "No any item found.",
  NORESULTFOUND: "Search found No results.",
  ITEMDELETED: "Item deleted successfully.",
  ITEMEDITED: "Item edited successfully.",
  ITEMIDNOTFOUND: "Requested item or note id does not exist.",
  LINKNOTFOUND: "Link does not exist.",
  ITEMAILABELDELETED: "AI label deleted successfully.",
  ITEMAILABELRESET: "AI label reset successfully.",
  ITEMAIREOBSERVE: "AI reobserved successfully.",
  ITEMOCRREOBSERVE: "OCR reobserved successfully.",
  ITEMOCRLABELDELETED: "OCR label deleted successfully.",
  ITEM_MEDIA_UPLOADED: "Item media uploaded successfully.",
  ITEM_MEDIA_NOT_UPLOADED: "Item media not uploaded.",
  NOHISTORYFOUND: "No any history found.",

  //Private mode password
  PRIVATENOTEPASSWORDNOTFOUND: "Please first set the Private mode pin.",
  PRIVATENOTEPASSWORDNOTMATCHED:
    "Requested old Private mode pin not matched.",
  PRIVATENOTEPASSWORDALREADYADDED: "Private mode pin has already added.",
  PRIVATENOTEPASSWORDADDED: "Private mode pin added successfully.",
  PRIVATENOTEPASSWORDNOTSAME:
    "Private mode pin and Confirm note pin are not same",
  PRIVATENOTEPASSWORDRESET: "Private mode pin reset successfully",

  //IS Private true or false
  ISPRIVATEEDITED: "Item is private field edited successfully.",
  ITEMISPRIVATENOTFOUND: "Item id does not exist.",
  //Bookmark Items
  BOOKMARKEDITEED: "Item bookmarked sucessfully.",
  BOOKMARKEDIDNOTFOUND: "Item id does not exist.",

  //Delete type = mobile,cloud
  DELETEFROMCLOUDORMOBILE:
    "Item get deleted successfully from cloud and mobile.",

  //Delete type = mobile
  DELETEFROMMOBILE: "Item get deleted successfully from mobile.",

  //Item restored
  // ITEMRESTORED: "Item get restored successfully",
  ITEMRESTORED: "Item restored as per its date/time stamp.",

  //Storage used by particular user
  STORAGEUSED: "Used storage details retrieved successfully.",

  //Storage used by particular user
  USERSUBSCRIPTIONADD: "User subscription added successfully.",
  USERSUBSCRIPTIONCANCEL: "Subscription canceled successfully.",
  USERSUBSCRIPTIONNOTCANCEL: "Subscription not canceled.",
  USERSUBSCRIPTIONUPDATE: "Subscription update successfully.",
  SUBSCRIPTIONNOTUPDATE: "Subscription not updated.",
  AIUSERSUBSCRIPTIONADD: "User ai subscription add successfully.",
  AISUBSCRIPTIONNOTFOUND: "AI Subscription not found.",

  //Web-link-preview-set
  WEBLINKSET: "Web Link Preview setted successfully.",

  //Set AI
  AISET: "AI set successfully.",

  //Set OCR
  OCRSET: "OCR set successfully.",

  //Set AI confidence level
  AIConfidenceLevelSET: "AI confidence level set successfully.",

  //Set Notification Status
  SetNotificationStatus: "Notification set successfully.",

  //Set Biometric Authentication
  BiometricAuthenticationSET: "Biometric authentication set successfully.",

  //Hide AI label
  HIDEAILABEl: "AI label set successfully.",

  //Hide OCR label
  HIDEOCRLABEl: "OCR label set successfully.",

  //User hard delete
  USERDELETED: "User Deleted successfully.",

  USERUSEDAIAPICALL: "User get AI api count successfully.",

  //Trash
  TRASHRETRIEVE: "Trash list retrieved successfully.",
};

module.exports = {
  appMessage,
};
