/*
 * Summary:     user.services file for handling all USER related actions.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules,models and constants for configuration */
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const Items = require("../../database/models").tbl_items;
const Notes = require("../../database/models").tbl_item_notes;
const Tags = require("../../database/models").tbl_item_tags;
const Tag_names = require("../../database/models").tbl_tag_names;
const Images = require("../../database/models").tbl_media;
const User = require("../../database/models").tbl_users;
const Sharing_details = require("../../database/models").tbl_sharing_details;
const Activity_logs = require("../../database/models").tbl_activity_logs;
const OCRLabels = require("../../database/models").tbl_item_ocr_labels;
const TokenDeductions = require('../../database/models').tbl_token_deductions;
const Sequelize = require('sequelize');
const constant = require('../../config/constant');
const sizeOf = require('image-size');
const gcpUpload = require('../../middleware/multer_gcp_upload');
const gcpBufferUpload = require('../../middleware/multer_gcp_upload_create_doc');
const gcpDelete = require('../../middleware/multer_gcp_delete');
const image_size = require('../../helper/general.helper');
const Op = Sequelize.Op;
const { getLinkPreview } = require('link-preview-js');
const { getVideoDurationInSeconds } = require('get-video-duration');
const { getAudioDurationInSeconds } = require('get-audio-duration');
var html_to_pdf = require('html-pdf-node');
const extractUrls = require('extract-urls');
const gcpUtils = require('../../helper/gcpUtils');
const moment = require('moment');

module.exports = {
  /* list */
  async list(req, res) {
    let {
      page,
      sort_by,
      order,
      search_tags,
      media_type,
      bookmark,
      search,
      search_type,
      type,
    } = req.body;
    page = page === undefined ? 1 : page;
    const offset = (page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;
    sort_by =
      sort_by === undefined || sort_by === '' || sort_by
        ? sort_by === 'uploaded_at'
          ? 'uploadedAt'
          : sort_by
        : 'uploaded_at';
    order = order === undefined || order === '' ? 'DESC' : order;
    bookmark = bookmark === undefined || bookmark === '' ? '' : bookmark;
    search = search === undefined || search === '' ? '' : search;
    media_type =
      media_type === undefined || media_type === '' ? '' : media_type;
    search_tags =
      search_tags === undefined || search_tags === '' ? '' : search_tags;
    search_type =
      search_type === undefined || search_type === '' ? '' : search_type;
    let whereFiltering = {};
    let whereMedia = media_type.split(',');

    if (search_tags != '') {
      whereFiltering.tag_name_id = { [Op.or]: JSON.parse(search_tags) };
    }

    let query;

    if (
      search_type.toLowerCase() === 'image' ||
      search_type.toLowerCase() === 'link' ||
      search_type.toLowerCase() === 'audio' ||
      search_type.toLowerCase() === 'doc'
    ) {
      query = {
        where: {
          user_id: req.user_id,
          is_deleted: 0,

          [Op.and]: [
            bookmark ? { is_bookmarked: bookmark } : '',
            search_type ? { item_type: search_type } : '',
          ],
          [Op.or]: [
            {
              title: { [Op.like]: `%${search}%` },
            },
            {
              description: { [Op.like]: `%${search}%` },
            },
          ],
        },

        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            where: search_tags ? whereFiltering : '',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],

        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
          'uploadedAt',
        ],
        // logging:console.log,
        order:
          sort_by === 'size'
            ? [['image_details', Sequelize.literal(`${sort_by}`), `${order}`]]
            : [[Sequelize.literal(`${sort_by}`), `${order}`]],

        // offset: offset,
        // limit: limit,
        distinct: `${Items.item_id}`,
        subQuery: false,
      };
    } else if (search_type.toLowerCase() === 'tag') {
      query = {
        where: {
          user_id: req.user_id,
          is_deleted: 0,

          [Op.and]: [bookmark ? { is_bookmarked: bookmark } : ''],
          [Op.or]: [
            {
              '$items_tag_lists.items_tag_names.tag_name$': {
                [Op.like]: `%${search}`,
              },
            },
          ],
        },

        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            where: search_tags ? whereFiltering : '',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],

        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
          'uploadedAt',
        ],
        // logging:console.log,
        order:
          sort_by === 'size'
            ? [['image_details', Sequelize.literal(`${sort_by}`), `${order}`]]
            : [[Sequelize.literal(`${sort_by}`), `${order}`]],

        // offset: offset,
        // limit: limit,
        distinct: `${Items.item_id}`,
        subQuery: false,
      };
    } else if (search_type.toLowerCase() === 'ai') {
      query = {
        where: {
          user_id: req.user_id,
          is_deleted: 0,

          [Op.and]: [bookmark ? { is_bookmarked: bookmark } : ''],
          [Op.or]: {
            '$image_details.AI_objects$': Sequelize.where(
              Sequelize.fn('LOWER', Sequelize.col('image_details.AI_objects')),
              'LIKE',
              '%' + search.toLowerCase() + '%'
            ),
          },
        },

        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            where: search_tags ? whereFiltering : '',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],

        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
          'uploadedAt',
        ],
        // logging:console.log,
        order:
          sort_by === 'size'
            ? [['image_details', Sequelize.literal(`${sort_by}`), `${order}`]]
            : [[Sequelize.literal(`${sort_by}`), `${order}`]],

        // offset: offset,
        // limit: limit,
        distinct: `${Items.item_id}`,
        subQuery: false,
      };
    } else if (search_type.toLowerCase() === 'ocr') {
      query = {
        where: {
          user_id: req.user_id,
          is_deleted: 0,

          [Op.and]: [bookmark ? { is_bookmarked: bookmark } : ''],
          [Op.or]: {
            '$item_ocr_labels.ocr_label$': Sequelize.where(
              Sequelize.fn('LOWER', Sequelize.col('item_ocr_labels.ocr_label')),
              'LIKE',
              '%' + search.toLowerCase() + '%'
            ),
          },
        },

        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            where: search_tags ? whereFiltering : '',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],

        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
          'uploadedAt',
        ],
        // logging:console.log,
        order:
          sort_by === 'size'
            ? [['image_details', Sequelize.literal(`${sort_by}`), `${order}`]]
            : [[Sequelize.literal(`${sort_by}`), `${order}`]],

        // offset: offset,
        // limit: limit,
        distinct: `${Items.item_id}`,
        subQuery: false,
      };
    } else if (search_type.toLowerCase() === 'source') {
      query = {
        where: {
          user_id: req.user_id,
          is_deleted: 0,
          [Op.and]: [bookmark ? { is_bookmarked: bookmark } : ''],
          [Op.or]: {
            '$image_details.source$': {
              [Op.like]: search,
            },
          },
        },

        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            where: search_tags ? whereFiltering : '',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],

        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
          'uploadedAt',
        ],
        // logging:console.log,
        order:
          sort_by === 'size'
            ? [['image_details', Sequelize.literal(`${sort_by}`), `${order}`]]
            : [[Sequelize.literal(`${sort_by}`), `${order}`]],

        // offset: offset,
        // limit: limit,
        distinct: `${Items.item_id}`,
        subQuery: false,
      };
    } else {
      query = {
        where: {
          user_id: req.user_id,
          is_deleted: 0,

          [Op.and]: [
            bookmark ? { is_bookmarked: bookmark } : '',
            media_type ? { item_type: whereMedia } : '',
          ],
          [Op.or]: [
            {
              title: { [Op.like]: `%${search}%` },
            },
            {
              description: { [Op.like]: `%${search}%` },
            },
            {
              '$items_tag_lists.items_tag_names.tag_name$': {
                [Op.like]: `%${search}`,
              },
            },
            {
              '$image_details.AI_objects$': Sequelize.where(
                Sequelize.fn(
                  'LOWER',
                  Sequelize.col('image_details.AI_objects')
                ),
                'LIKE',
                '%' + search.toLowerCase() + '%'
              ),
            },
            {
              '$item_ocr_labels.ocr_label_string$': Sequelize.where(
                Sequelize.fn(
                  'LOWER',
                  Sequelize.col('item_ocr_labels.ocr_label_string')
                ),
                'LIKE',
                '%' + search.toLowerCase() + '%'
              ),
            },
            {
              '$image_details.source$': {
                [Op.like]: search,
              },
            },
          ],
        },

        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            where: search_tags ? whereFiltering : '',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],

        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
          'uploadedAt',
        ],
        // logging:console.log,
        order:
          sort_by === 'size'
            ? [['image_details', Sequelize.literal(`${sort_by}`), `${order}`]]
            : [[Sequelize.literal(`${sort_by}`), `${order}`]],

        // offset: offset,
        // limit: limit,
        distinct: `${Items.item_id}`,
        subQuery: false,
      };
    }

    if (!type) {
      query.offset = offset;
      query.limit = limit;
    }

    var find_items = await Items.findAndCountAll(query);

    if (find_items.count === 0) {
      return 0;
    } else {
      await Promise.all(
        find_items.rows.map((item_result) => {
          item_result.dataValues.createdAt = item_result.dataValues.createdAt
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          item_result.dataValues.uploadedAt = item_result.dataValues.uploadedAt
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');

          let note_list = item_result.dataValues.items_note_lists;
          let tag_list = item_result.dataValues.items_tag_lists;

          let item_sharing_details =
            item_result.dataValues.item_sharing_details;
          if (item_sharing_details) {
            item_sharing_details.dataValues.createdAt =
              item_sharing_details.dataValues.createdAt
                .toISOString()
                .substr(0, 19)
                .replace('T', ' ');
            item_sharing_details.dataValues.updatedAt =
              item_sharing_details.dataValues.updatedAt
                .toISOString()
                .substr(0, 19)
                .replace('T', ' ');
          }
          item_note = {};
          media_details = item_result.dataValues.image_details;
          if (media_details.length === 0) {
            item_result.dataValues.size = '0';
          }
          media_details.map((data) => {
            // data.dataValues.media =
            //   constant.GCP_URL +
            //   constant.GCP_BUCKET_NAME +
            //   "/" +
            //   constant.GCP_BUCKET_FOLDER +
            //   constant.GCP_USER_FOLDER +
            //   data.dataValues.user_id +
            //   "/" +
            //   constant.GCP_ITEM_FOLDER +
            //   data.dataValues.item_id +
            //   "/" +
            //   data.dataValues.media;
            let imgs = image_size.formatSize(data.dataValues.size * 1000);
            let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;

            let size = b.toString() + ' ' + imgs.split(' ')[1];
            item_result.dataValues.size = data.dataValues.size
              ? data.dataValues.size.toString()
              : '0';
            data.dataValues.size = size;
            data.dataValues.mediaName = data.dataValues.media;
          });

          note_list.map((data) => {
            if (data.createdAt) {
              data.dataValues.createdAt = data.dataValues.createdAt
                .toISOString()
                .substr(0, 19)
                .replace('T', ' ');
              data.dataValues.updatedAt = data.dataValues.updatedAt
                .toISOString()
                .substr(0, 19)
                .replace('T', ' ');

              item_note.note_id = data.dataValues.id;
              item_note.note_description = data.dataValues.note_description;
              item_note.createdAt = data.dataValues.createdAt;
              item_note.updatedAt = data.dataValues.updatedAt;
            }

            item_result.dataValues.item_note = item_note;
          });

          tag_list.map((data, index) => {
            if (
              data.createdAt &&
              data.dataValues.is_deleted === false &&
              data.dataValues.items_tag_names !== null
            ) {
              data.dataValues.createdAt = data.dataValues.createdAt
                .toISOString()
                .substr(0, 19)
                .replace('T', ' ');
              data.dataValues.updatedAt = data.dataValues.updatedAt
                .toISOString()
                .substr(0, 19)
                .replace('T', ' ');

              if (data.dataValues.items_tag_names !== null) {
                data.dataValues.tag_name =
                  data.items_tag_names.dataValues.tag_name;
              }
            }
          });
        })
      );

      for (let index = 0; index < find_items.rows.length; index++) {
        const item_id = find_items.rows[index].item_id;
        var find_tags = await Tags.findAll({
          where: {
            item_id: item_id,
          },
          include: [
            {
              model: Tag_names,
              as: 'items_tag_names',
              attributes: {
                exclude: ['tag_name_id', 'user_id'],
              },
            },
          ],
        });
        find_items.rows[index].dataValues.items_tag_lists = find_tags;
      }

      find_items.rows.map((data, index) => {
        data.dataValues.items_tag_lists.map((data1) => {
          if (
            data1.createdAt &&
            data1.dataValues.is_deleted === false &&
            data1.dataValues.items_tag_names !== null
          ) {
            data1.dataValues.createdAt = data1.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
            data1.dataValues.updatedAt = data1.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');

            if (data1.dataValues.items_tag_names !== null) {
              data1.dataValues.tag_name =
                data1.items_tag_names.dataValues.tag_name;
            }
          }

          delete data1.dataValues.items_tag_names;
        });

        delete data.dataValues.items_note_lists;
      });

      // find_items.rows = await image_size.arrayMediaURL(find_items.rows)

      return find_items;
    }
  },

  prepareUrlForLinkPreview(urlsFromDescription) {
    let hyperText = urlsFromDescription[0].split(' ') || '';
    hyperText = hyperText[0] || '';

    let hyperTextPreFix = hyperText;
    if (hyperTextPreFix) {
      hyperTextPreFix = hyperTextPreFix.substring(0, 3);
      if (hyperTextPreFix === 'www') hyperText = 'https://' + hyperText;
    }

    return hyperText;
  },

  /* add */
  async add(req, res) {
    var items_tag_lists = [];
    var item_note = {};
    var OCRLabelRes = {};

    let {
      id,
      title,
      description,
      media_type,
      name,
      phone_number,
      latitude,
      longitude,
      link,
      source,
      uploadFileSize,
      uploadedAt,
      local_path,
      fileName,
      durationInSec,
      imageWidth,
      imageHeight,
      mimeType,
    } = req.body;
    uploadedAt = moment(uploadedAt).utc();
    let activityLogMsg = '';
    //web-link-preview details like image,viedo,title,description..
    if (media_type.toLowerCase() === 'link') {
      try {
        var link_preview = await getLinkPreview(link, {
          headers: {
            'user-agent': 'googlebot',
            'Accept-Language': 'en-US',
          },
          imagesPropertyType: 'og',
          followRedirects: 'follow',
          timeout: 2000,
        });
        console.log(
          '🚀 ~ file: items.service.js:568 ~ add ~ link_preview:',
          link_preview
        );
      } catch (error) {
        console.log('🚀 ~ file: items.service.js:570 ~ add ~ error:', error);
        res.status(status.INTERNALSERVERERRORSTATUS).send({
          data: [],
          message: message.LINKNOTFOUND,
          status: status.ERROR,
        });
      }
    }

    var user = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });

    uploadFileSize = Number(uploadFileSize);

    var find_item = await Items.findOne({
      where: {
        item_id: id,
        // title: title,
        is_deleted: 0,
        user_id: req.user_id,
      },
    });

    if (media_type.toLowerCase() === 'text') {
      if (description) {
        if (
          user &&
          user.dataValues // &&
          // user.dataValues.is_web_link_preview_set
        ) {
          let urlsFromDescription = extractUrls(description) || [];
          if (urlsFromDescription?.length) {
            try {
              let hyperText = null;
              if (urlsFromDescription && urlsFromDescription[0]) {
                hyperText = this.prepareUrlForLinkPreview(urlsFromDescription);
              }
              link_preview = await getLinkPreview(hyperText, {
                headers: {
                  'user-agent': 'googlebot',
                  'Accept-Language': 'en-US',
                },
                imagesPropertyType: 'og',
                followRedirects: 'follow',
                timeout: 2000,
              });
              if (link_preview.description) {
                link_preview.description =
                  link_preview.title +
                  '<br /> ' +
                  `<p>${link_preview.description}<p/>`;
              }
              if (link_preview.title) link_preview.title = description;
              if (hyperText) link = hyperText;
              media_type = 'LINK';
            } catch (error) {
              console.log(
                '🚀 ~ file: items.service.js:616 ~ add ~ error:',
                error
              );
              link_preview = {};
              link_preview.title = title;
              link_preview.description = description;
              // console.log('TCL :: ->', error);
            }
          }
        }
      }
    }

    const extraTotalStorage =
      user.dataValues.total_storage + user.dataValues.extra_storage;
    const totalUploadedStorage = user.dataValues.used_storage + uploadFileSize;
    console.log(
      '🚀 ~ file: items.service.js:928 ~ add ~ totalUploadedStorage:',
      totalUploadedStorage
    );

    if (
      extraTotalStorage <= totalUploadedStorage &&
      (media_type.toLowerCase() === 'image' ||
        media_type.toLowerCase() === 'video' ||
        media_type.toLowerCase() === 'audio' ||
        media_type.toLowerCase() === 'pdf' ||
        media_type.toLowerCase() === 'doc')
    ) {
      console.log(
        '🚀 ~ file: items.service.js:936 ~ add ~ extraTotalStorage:',
        extraTotalStorage
      );
      console.log(
        '🚀 ~ file: items.service.js:936 ~ add ~ user.dataValues.total_storage:',
        user.dataValues.total_storage
      );
      return 1;
    }

    if (find_item) {
      await Items.update(
        {
          user_id: req.user_id,
          title:
            media_type.toLowerCase() === 'link'
              ? // && user.dataValues.is_web_link_preview_set === true
                link_preview.title
              : title,
          description:
            media_type.toLowerCase() === 'link'
              ? // && user.dataValues.is_web_link_preview_set === true
                link_preview.description
              : description,
          item_type: media_type,
          uploadedAt: uploadedAt,
        },
        {
          where: { item_id: id },
        }
      );

      var item = await Items.findOne({
        where: {
          item_id: id,
        },
      });
    } else {
      var item = await Items.create({
        item_id: id, // frontend add id
        user_id: req.user_id,
        title:
          media_type.toLowerCase() === 'link'
            ? // && user.dataValues.is_web_link_preview_set === true
              link_preview.title
            : title,
        description:
          media_type.toLowerCase() === 'link'
            ? // && user.dataValues.is_web_link_preview_set === true
              link_preview.description
            : description,
        item_type: media_type,
        uploadedAt: uploadedAt,
      });
    }

    var find_items = await Items.findAll({
      where: {
        item_id: item.dataValues.item_id,
        is_deleted: 0,
      },
    });

    var img = [];
    var a = find_items.map((data) => {
      return data.dataValues.item_id;
    });
    var item_id = String(a);
    if (find_items) {
      let imageLabels,
        videoLabels,
        buffer,
        base64,
        hours,
        minutes,
        seconds,
        duration_in_sec,
        file_size;
      if (
        media_type.toLowerCase() === 'contact' ||
        media_type.toLowerCase() === 'location' ||
        media_type.toLowerCase() === 'link'
      ) {
        const findItemSharingDetails = await Sharing_details.findOne({
          where: {
            item_id: id,
          },
        });

        if (findItemSharingDetails) {
          await Sharing_details.update(
            {
              name: media_type.toLowerCase() === 'contact' ? name : null,
              phone_number:
                media_type.toLowerCase() === 'contact' ? phone_number : null,
              longitude:
                media_type.toLowerCase() === 'location' ? longitude : null,
              latitude:
                media_type.toLowerCase() === 'location' ? latitude : null,
              link: media_type.toLowerCase() === 'link' ? link : null,
              link_preview_image:
                link_preview &&
                // user.dataValues.is_web_link_preview_set === true &&
                media_type.toLowerCase() === 'link'
                  ? link_preview.images[0]
                  : null,
              link_preview_video:
                link_preview &&
                // user.dataValues.is_web_link_preview_set === true &&
                media_type.toLowerCase() === 'link'
                  ? link_preview.videos[0]
                  : null,
            },
            {
              where: {
                item_id: id,
              },
            }
          );
        } else {
          await Sharing_details.create({
            item_id: item_id,
            name: media_type.toLowerCase() === 'contact' ? name : null,
            phone_number:
              media_type.toLowerCase() === 'contact' ? phone_number : null,
            longitude:
              media_type.toLowerCase() === 'location' ? longitude : null,
            latitude: media_type.toLowerCase() === 'location' ? latitude : null,
            link: media_type.toLowerCase() === 'link' ? link : null,
            link_preview_image:
              link_preview &&
              // user.dataValues.is_web_link_preview_set === true &&
              media_type.toLowerCase() === 'link'
                ? link_preview.images[0]
                : null,
            link_preview_video:
              link_preview &&
              // user.dataValues.is_web_link_preview_set === true &&
              media_type.toLowerCase() === 'link'
                ? link_preview.videos[0]
                : null,
          });
        }
      }

      if (
        media_type.toLowerCase() === 'image' ||
        media_type.toLowerCase() === 'video' ||
        media_type.toLowerCase() === 'audio' ||
        media_type.toLowerCase() === 'pdf' ||
        media_type.toLowerCase() === 'doc' ||
        media_type.toLowerCase() === 'location'
      ) {
        const findMediaDetails = await Images.findOne({
          where: {
            item_id: id,
          },
        });

        if (
          findMediaDetails &&
          findMediaDetails.media &&
          findMediaDetails.is_upload === true
        ) {
          await gcpDelete(
            constant.GCP_BUCKET_FOLDER +
              constant.GCP_USER_FOLDER +
              req.user_id +
              '/' +
              constant.GCP_ITEM_FOLDER +
              findMediaDetails.dataValues.item_id +
              '/' +
              findMediaDetails.dataValues.media
          );
        }

        // if (req.file) {
        //   media = await gcpUpload(
        //     req.file,
        //     constant.GCP_BUCKET_FOLDER +
        //     constant.GCP_USER_FOLDER +
        //     req.user_id +
        //     "/" +
        //     constant.GCP_ITEM_FOLDER +
        //     req.body.id +
        //     "/" +
        //     req.file.originalname
        //   );
        // }
        if (media_type.toLowerCase() === 'video') {
          if (
            user.dataValues.AI_set === true &&
            user.dataValues.total_ai_api > user.dataValues.used_ai_api
          ) {
            await User.update(
              {
                used_ai_api: user.dataValues.used_ai_api + 1,
              },
              {
                where: {
                  user_id: req.user_id,
                },
              }
            );
            // AI error
            imageLabels = await gcpUtils.videoLabelDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                req.body.id +
                '/' +
                fileName, // req.file.originalname,
              user.dataValues.AI_confidence_level / 100
            );
          }

          if (
            user.dataValues.OCR_set === true &&
            user.dataValues.total_ai_api > user.dataValues.used_ai_api
          ) {
            // find and store OCR labels
            videoLabelsOCR = await gcpUtils.videoOCRDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                req.body.id +
                '/' +
                // req.file.originalname
                fileName
            );

            // Storing detected text as string in database for faster search query
            let ocrLabelString = '';
            if (videoLabelsOCR) {
              videoLabelsOCR.forEach((each) => {
                if (each.Type == 'LINE') {
                  ocrLabelString += ` ${each.DetectedText}`;
                }
              });
            }

            OCRLabelRes = await OCRLabels.create({
              item_id: item_id,
              ocr_label: videoLabelsOCR,
              ocr_label_string: ocrLabelString,
            });
          }

          // let data = await getVideoDurationInSeconds(req.file.path);
          // duration_in_sec = data;
          hours = Math.floor(durationInSec / 3600);
          minutes = Math.floor(durationInSec / 60);
          seconds = Math.floor(durationInSec % 60);
        }

        if (media_type.toLowerCase() === 'audio') {
          // let data = await getAudioDurationInSeconds(req.file.path);
          // duration_in_sec = data;
          hours = Math.floor(durationInSec / 3600);
          minutes = Math.floor(durationInSec / 60);
          seconds = Math.floor(durationInSec % 60);
        }

        if (media_type.toLowerCase() === 'image') {
          if (
            user.dataValues.AI_set === true &&
            user.dataValues.total_ai_api > user.dataValues.used_ai_api
          ) {
            await User.update(
              {
                used_ai_api: user.dataValues.used_ai_api + 1,
              },
              {
                where: {
                  user_id: req.user_id,
                },
              }
            );

            imageLabels = await gcpUtils.imageLabelDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                req.body.id +
                '/' +
                fileName, //req.file.originalname,
              user.dataValues.AI_confidence_level / 100
            );
          }

          if (user.dataValues.OCR_set === true) {
            // find and store OCR labels
            OCR = await gcpUtils.imageOCRDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                req.body.id +
                '/' +
                fileName // req.file.originalname
            );

            // Storing detected text as string in database for faster search query
            let ocrLabelString = '';
            if (OCR) {
              OCR.forEach((each) => {
                if (each.Type == 'LINE') {
                  ocrLabelString += ` ${each.DetectedText}`;
                }
              });
            }

            OCRLabelRes = await OCRLabels.create({
              item_id: item_id,
              ocr_label: OCR,
              ocr_label_string: ocrLabelString,
            });
          }
        } else {
          imageLabels =
            user.dataValues.AI_set === true &&
            user.dataValues.total_ai_api > user.dataValues.used_ai_api
              ? imageLabels
              : null; // commit for node-rekognition library
        }

        // const dimensions =
        //   media_type.toLowerCase() === "image" ? sizeOf(req.file.path) : null;
        var img = [];
        const path =
          constant.GCP_BUCKET_FOLDER +
          constant.GCP_USER_FOLDER +
          req.user_id +
          '/' +
          constant.GCP_ITEM_FOLDER +
          req.body.id +
          '/' +
          // filename;
          fileName;
        console.log('🚀 ~ file: items.service.js:1247 ~ add ~ path:', path);
        // let file_size;
        if (
          media_type.toLowerCase() === 'image' ||
          media_type.toLowerCase() === 'video' ||
          media_type.toLowerCase() === 'audio' ||
          media_type.toLowerCase() === 'pdf' ||
          media_type.toLowerCase() === 'doc' ||
          media_type.toLowerCase() === 'location'
        ) {
          file_size = await image_size.sizeOfGCPFile(path);
          // let file_size = await image_size.getFileSize(path);
          console.log(
            '🚀 ~ file: items.service.js:1257 ~ add ~ file_size:',
            file_size.metadata.size
          );
        }

        if (findMediaDetails) {
          await Images.update(
            {
              user_id: req.user_id,
              resolution:
                media_type.toLowerCase() === 'image'
                  ? // ? dimensions.width + "*" + dimensions.height
                    imageWidth + '*' + imageHeight
                  : null,
              AI_objects:
                (media_type.toLowerCase() === 'video' ||
                  media_type.toLowerCase() === 'image') &&
                user.dataValues.AI_set === true &&
                user.dataValues.total_ai_api > user.dataValues.used_ai_api
                  ? imageLabels
                  : null, // commit for node-rekognition library
              duration:
                media_type.toLowerCase() === 'video' ||
                media_type.toLowerCase() === 'audio'
                  ? hours + ':' + minutes + ':' + seconds
                  : null,

              media_type: media_type,
              media: fileName, // req.file.originalname,
              source: source ? source : '',
              extention: mimeType, // req.file.mimetype,
              size:
                media_type.toLowerCase() === 'image' ||
                media_type.toLowerCase() === 'video' ||
                media_type.toLowerCase() === 'audio' ||
                media_type.toLowerCase() === 'pdf' ||
                media_type.toLowerCase() === 'doc' ||
                media_type.toLowerCase() === 'location'
                  ? Math.round(Number(file_size.metadata.size / 1000) * 100) /
                    100
                  : null,
            },
            {
              where: {
                id: findMediaDetails.id,
              },
            }
          );
        } else {
          await Images.create({
            item_id: item_id,
            user_id: req.user_id,
            local_path: local_path ? local_path : null,
            resolution:
              media_type.toLowerCase() === 'image'
                ? // ? dimensions.width + "*" + dimensions.height
                  imageWidth + '*' + imageHeight
                : null,
            AI_objects:
              (media_type.toLowerCase() === 'video' ||
                media_type.toLowerCase() === 'image') &&
              user.dataValues.AI_set === true &&
              user.dataValues.total_ai_api > user.dataValues.used_ai_api
                ? imageLabels
                : null, // commit for node-rekognition library
            duration:
              media_type.toLowerCase() === 'video' ||
              media_type.toLowerCase() === 'audio'
                ? hours + ':' + minutes + ':' + seconds
                : null,

            media_type: media_type,
            media: fileName, // req.file.originalname,
            source: source ? source : '',
            extention: mimeType, // req.file.mimetype,
            // size: Math.round(Number((file_size.metadata.size) / 1000) * 100) / 100,
            size:
              media_type.toLowerCase() === 'image' ||
              media_type.toLowerCase() === 'video' ||
              media_type.toLowerCase() === 'audio' ||
              media_type.toLowerCase() === 'pdf' ||
              media_type.toLowerCase() === 'doc' ||
              media_type.toLowerCase() === 'location'
                ? Math.round(Number(file_size.metadata.size / 1000) * 100) / 100
                : null,
          });
        }

        var image_details = await Images.findOne({
          where: {
            item_id: id,
          },
        });
      }

      // if (media_type.toLowerCase() === "create_doc") {
      //   let options = { format: "A4" };
      //   let file = { content: req.body.description };
      //   // let filename = Date.now() + `.pdf`;
      //   const path =
      //     constant.GCP_BUCKET_FOLDER +
      //     constant.GCP_USER_FOLDER +
      //     req.user_id +
      //     "/" +
      //     constant.GCP_ITEM_FOLDER +
      //     req.body.id +
      //     "/" +
      //     // filename;
      //     fileName;
      //   // try {
      //   //   await html_to_pdf
      //   //     .generatePdf(file, options)
      //   //     .then(async (pdfBuffer) => {
      //   //       await gcpBufferUpload(
      //   //         pdfBuffer,
      //   //         constant.GCP_BUCKET_FOLDER +
      //   //         constant.GCP_USER_FOLDER +
      //   //         req.user_id +
      //   //         "/" +
      //   //         constant.GCP_ITEM_FOLDER +
      //   //         req.body.id +
      //   //         "/" +
      //   //         filename
      //   //       )
      //   //     });
      //   // } catch (error) {
      //   //   console.log("TCL => HTML => UPLOAD", error);
      //   // }
      //   let pdf_size = await image_size.sizeOfGCPFile(path);
      //   const pdfSize = Math.round(Number((pdf_size.metadata.size) / 1000) * 100) / 100;
      //   const extraTotalStorage = user.dataValues.total_storage + user.dataValues.extra_storage;
      //   const totalUploadedStorage = user.dataValues.used_storage + pdfSize;
      //   console.log("🚀 ~ file: items.service.js:1344 ~ add ~ totalUploadedStorage:", totalUploadedStorage)
      //   console.log("🚀 ~ file: items.service.js:1344 ~ add ~ user.dataValues.total_storage:", user.dataValues.total_storage)
      //   if (extraTotalStorage <= totalUploadedStorage) {
      //     await gcpDelete(
      //       path
      //     );
      //     await Items.destroy({
      //       where: {
      //         item_id: item_id,
      //       },
      //     });

      //     return 1;
      //   }

      //   const findMediaDetails = await Images.findOne({
      //     where: {
      //       item_id: id,
      //     }
      //   });

      //   if (findMediaDetails) {
      //     await Images.update(
      //       {
      //         user_id: req.user_id,
      //         resolution:
      //           media_type.toLowerCase() === "image"
      //             // ? dimensions.width + "*" + dimensions.height
      //             ? imageWidth + "*" + imageHeight
      //             : null,
      //         // AI_objects: media_type.toLowerCase() === "video" /*|| media_type.toLowerCase() === "image"*/ && user.dataValues.AI_set === true ? imageLabels : null,   // commit for node-rekognition library
      //         AI_objects:
      //           (media_type.toLowerCase() === "video" ||
      //             media_type.toLowerCase() === "image") &&
      //             ((user.dataValues.AI_set === true) && (user.dataValues.total_ai_api > user.dataValues.used_ai_api))
      //             ? imageLabels
      //             : null, // commit for node-rekognition library
      //         duration:
      //           media_type.toLowerCase() === "video" ||
      //             media_type.toLowerCase() === "audio"
      //             ? hours + ":" + minutes + ":" + seconds
      //             : null,
      //         local_path: local_path ? local_path : null,
      //         media_type: media_type,
      //         media: fileName, // filename,
      //         source: source ? source : "",
      //         extention: mimeType, // "application/pdf",
      //         size: pdfSize,
      //       },
      //       {
      //         where: {
      //           id: findMediaDetails.id
      //         },
      //       }
      //     );
      //   } else {
      //     await Images.create({
      //       item_id: item_id,
      //       user_id: req.user_id,
      //       resolution:
      //         media_type.toLowerCase() === "image"
      //           // ? dimensions.width + "*" + dimensions.height
      //           ? imageWidth + "*" + imageHeight
      //           : null,
      //       // AI_objects: media_type.toLowerCase() === "video" /*|| media_type.toLowerCase() === "image"*/ && user.dataValues.AI_set === true ? imageLabels : null,   // commit for node-rekognition library
      //       AI_objects:
      //         (media_type.toLowerCase() === "video" ||
      //           media_type.toLowerCase() === "image") &&
      //           ((user.dataValues.AI_set === true) && (user.dataValues.total_ai_api > user.dataValues.used_ai_api))
      //           ? imageLabels
      //           : null, // commit for node-rekognition library
      //       duration:
      //         media_type.toLowerCase() === "video" ||
      //           media_type.toLowerCase() === "audio"
      //           ? hours + ":" + minutes + ":" + seconds
      //           : null,
      //       local_path: local_path ? local_path : null,
      //       media_type: media_type,
      //       media: fileName, // filename,
      //       source: source ? source : "",
      //       extention: mimeType, // "application/pdf",
      //       size: pdfSize,
      //     });
      //   }

      //   var image_details = await Images.findOne({
      //     where: {
      //       item_id: id,
      //     }
      //   });

      //   if (image_details.dataValues.size !== null) {
      //     image_details.dataValues.size = image_details.size.toString();
      //     let imgs = image_size.formatSize(pdf_size.metadata.size);
      //     let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;

      //     image_details.dataValues.size =
      //       b.toString() + " " + imgs.split(" ")[1];
      //   }
      // }

      // if (image_details) {
      //   // media = await image_size.sizeOfGCPFile("staging/user/125/items/1251718795458098/1718795462223.jpg")
      //   const mediaSize = await image_size.sizeOfGCPFile(
      //     constant.GCP_BUCKET_FOLDER +
      //     constant.GCP_USER_FOLDER +
      //     req.user_id +
      //     "/" +
      //     constant.GCP_ITEM_FOLDER +
      //     image_details.dataValues.item_id +
      //     "/" +
      //     image_details.dataValues.media
      //   );

      //   if (parseInt(mediaSize.metadata.size) < 1) {
      //     await gcpDelete(
      //       constant.GCP_BUCKET_FOLDER +
      //       constant.GCP_USER_FOLDER +
      //       req.user_id +
      //       "/" +
      //       constant.GCP_ITEM_FOLDER +
      //       image_details.dataValues.item_id +
      //       "/" +
      //       image_details.dataValues.media
      //     );

      //     await Items.update(
      //       {
      //         is_upload: 0
      //       },
      //       {
      //         where: { item_id: image_details.dataValues.item_id },
      //       }
      //     )
      //   };
      // };

      var items = await Items.findOne({
        where: {
          item_id: item_id,
          is_deleted: 0,
        },
        attributes: {
          include: ['createdAt', 'is_upload'],
        },
      });

      var sharing_info = await Sharing_details.findOne({
        where: {
          item_id: item_id,
          is_deleted: 0,
        },
        attributes: {
          exclude: ['item_id', 'updatedAt'],
        },
      });
      // Activity Logs
      if (items) {
        let activityMsg =
          media_type.toLowerCase() === 'video' ||
          media_type.toLowerCase() === 'audio' ||
          media_type.toLowerCase() === 'image' ||
          media_type.toLowerCase() === 'pdf' ||
          media_type.toLowerCase() === 'doc'
            ? // ? `${req.file.originalname} was added`
              `${fileName} was added`
            : '';

        if (image_details && sharing_info) {
          var item_activity_log = await Activity_logs.create({
            user_id: req.user_id,
            title: 'Add Item',
            description: activityMsg || 'Item, Media ,Sharing_info gets added.',
          });
        } else if (image_details) {
          var item_activity_log = await Activity_logs.create({
            user_id: req.user_id,
            title: 'Add Item',
            description: activityMsg || 'Item and Media gets added.',
          });
        } else if (sharing_info) {
          var item_activity_log = await Activity_logs.create({
            user_id: req.user_id,
            title: 'Add Item',
            description: activityMsg || 'Item and SharingInfo gets added.',
          });
        } else {
          var item_activity_log = await Activity_logs.create({
            user_id: req.user_id,
            title: 'Add Item',
            description: activityMsg || 'Item gets added.',
          });
        }
      }

      if (sharing_info /*|| media_type.toLowerCase() === "link"*/) {
        sharing_info.dataValues.createdAt = sharing_info.dataValues.createdAt
          .toISOString()
          .substr(0, 19)
          .replace('T', ' ');
        items.dataValues.item_sharing_details = sharing_info;
      } else {
        items.dataValues.item_sharing_details = {};
      }
      items.dataValues.createdAt = items.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace('T', ' ');
      items.dataValues.uploadedAt = items.dataValues.uploadedAt
        .toISOString()
        .substr(0, 19)
        .replace('T', ' ');
      if (image_details) {
        // image_details.dataValues.media =
        //   constant.GCP_URL +
        //   constant.GCP_BUCKET_NAME +
        //   "/" +
        //   constant.GCP_BUCKET_FOLDER +
        //   constant.GCP_USER_FOLDER +
        //   image_details.dataValues.user_id +
        //   "/" +
        //   constant.GCP_ITEM_FOLDER +
        //   image_details.dataValues.item_id +
        //   "/" +
        //   image_details.dataValues.media;
        // image_details.dataValues.media = await gcpUtils.getPrivateUrl(
        //   constant.GCP_BUCKET_NAME,
        //   constant.GCP_BUCKET_FOLDER +
        //   constant.GCP_USER_FOLDER +
        //   image_details.dataValues.user_id +
        //   "/" +
        //   constant.GCP_ITEM_FOLDER +
        //   image_details.dataValues.item_id +
        //   "/" +
        //   image_details.dataValues.media, /*expires: '2023-12-02' */
        // );
        image_details.dataValues.mediaName = image_details.dataValues.media;
        image_details.dataValues.createdAt = image_details.dataValues.createdAt
          .toISOString()
          .substr(0, 19)
          .replace('T', ' ');
        image_details.dataValues.updatedAt = image_details.dataValues.updatedAt
          .toISOString()
          .substr(0, 19)
          .replace('T', ' ');
        if (
          image_details.dataValues.size !== null &&
          media_type.toLowerCase() !== 'create_doc'
        ) {
          items.dataValues.size = image_details.size.toString();
          // let imgs = image_size.formatSize(req.file.size);
          let imgs = image_size.formatSize(file_size.metadata.size);
          let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;

          image_details.dataValues.size =
            b.toString() + ' ' + imgs.split(' ')[1];
        }
        items.dataValues.image_details = image_details;

        img.push(items.dataValues.image_details);

        items.dataValues.image_details = img;
      } else {
        items.dataValues.size = '0';
      }

      items.dataValues.web_link_preview_set =
        user.dataValues.is_web_link_preview_set;
      items.dataValues.AI_set = user.dataValues.AI_set;
      items.dataValues.items_tag_lists = items_tag_lists;
      items.dataValues.item_note = item_note;
      items.dataValues.item_ocr_labels = OCRLabelRes;
    }

    console.log(
      '🚀 ~ file: items.service.js:1677 ~ add ~ items:',
      items.dataValues
    );
    return items;
  },

  /* bulkAdd */
  async bulkAdd(req, res) {
    let {
      id,
      title,
      description,
      media_type,
      source,
      uploadFileSize,
      uploadedAt,
      local_path,
      fileName,
      imageWidth,
      imageHeight,
      mimeType,
      bulkNumber,
    } = req.body;

    let user = await User.findOne({ where: { user_id: req.user_id } });

    let bigNum = BigInt(id);

    if (req.file) {
      for (let index = 0; index < bulkNumber; index++) {
        console.log('index ::', index);

        id = (bigNum + BigInt(index)).toString();
        console.log('🚀 ~ file: items.service.js:1714 ~ bulkAdd ~ id:', id);
        // }
        uploadedAt = moment(uploadedAt).utc();

        uploadFileSize = Number(uploadFileSize);

        var find_item = await Items.findOne({
          where: {
            item_id: id,
            // title: title,
            user_id: req.user_id,
          },
        });

        const extraTotalStorage =
          user.dataValues.total_storage + user.dataValues.extra_storage;
        const totalUploadedStorage =
          user.dataValues.used_storage + uploadFileSize;
        console.log(
          '🚀 ~ file: items.service.js:928 ~ add ~ totalUploadedStorage:',
          totalUploadedStorage
        );

        if (
          extraTotalStorage <= totalUploadedStorage &&
          (media_type.toLowerCase() === 'image' ||
            media_type.toLowerCase() === 'video' ||
            media_type.toLowerCase() === 'audio' ||
            media_type.toLowerCase() === 'pdf' ||
            media_type.toLowerCase() === 'doc')
        ) {
          console.log(
            '🚀 ~ file: items.service.js:936 ~ add ~ extraTotalStorage:',
            extraTotalStorage
          );
          console.log(
            '🚀 ~ file: items.service.js:936 ~ add ~ user.dataValues.total_storage:',
            user.dataValues.total_storage
          );
          return 1;
        }

        if (find_item) {
          await Items.update(
            {
              user_id: req.user_id,
              title: title,
              description: description,
              item_type: media_type,
              is_deleted: 0,
              uploadedAt: uploadedAt,
            },
            {
              where: { item_id: id },
            }
          );

          var item = await Items.findOne({
            where: {
              item_id: id,
            },
          });
        } else {
          var item = await Items.create({
            item_id: id, // frontend add id
            user_id: req.user_id,
            title: title,
            description: description,
            item_type: media_type,
            uploadedAt: uploadedAt,
          });
        }

        var find_items = await Items.findAll({
          where: {
            item_id: item.dataValues.item_id,
            is_deleted: 0,
          },
        });

        var img = [];
        var a = find_items.map((data) => {
          return data.dataValues.item_id;
        });
        var item_id = String(a);
        if (find_items) {
          let imageLabels,
            videoLabels,
            buffer,
            base64,
            hours,
            minutes,
            seconds,
            duration_in_sec,
            file_size;

          imageLabels = {
            Labels: [
              {
                Name: 'Computer',
                Confidence: 0.9808518290519714,
              },
              {
                Name: 'Brown',
                Confidence: 0.9804678559303284,
              },
              {
                Name: 'Laptop',
                Confidence: 0.9632933139801024,
              },
              {
                Name: 'Personal computer',
                Confidence: 0.9587812423706056,
              },
              {
                Name: 'Peripheral',
                Confidence: 0.9318280816078186,
              },
              {
                Name: 'Input device',
                Confidence: 0.9306802749633788,
              },
              {
                Name: 'Output device',
                Confidence: 0.9192694425582886,
              },
              {
                Name: 'Netbook',
                Confidence: 0.9183173179626464,
              },
              {
                Name: 'Product',
                Confidence: 0.9074485301971436,
              },
              {
                Name: 'Black',
                Confidence: 0.8955340385437012,
              },
            ],
          };

          if (
            media_type.toLowerCase() === 'image' ||
            media_type.toLowerCase() === 'video' ||
            media_type.toLowerCase() === 'audio' ||
            media_type.toLowerCase() === 'pdf' ||
            media_type.toLowerCase() === 'doc' ||
            media_type.toLowerCase() === 'location'
          ) {
            const findMediaDetails = await Images.findOne({
              where: {
                item_id: id,
              },
            });

            if (req.file) {
              media = await gcpUpload(
                req.file,
                constant.GCP_BUCKET_FOLDER +
                  constant.GCP_USER_FOLDER +
                  req.user_id +
                  '/' +
                  constant.GCP_ITEM_FOLDER +
                  id +
                  '/' +
                  req.file.originalname
              );
            }

            var img = [];
            // let file_size;

            if (findMediaDetails) {
              await Images.update(
                {
                  user_id: req.user_id,
                  resolution:
                    media_type.toLowerCase() === 'image'
                      ? // ? dimensions.width + "*" + dimensions.height
                        imageWidth + '*' + imageHeight
                      : null,
                  AI_objects: imageLabels ? imageLabels : null,
                  duration: null,
                  media_type: media_type,
                  media: req.file.originalname, // req.file.originalname,
                  source: source ? source : '',
                  extention: mimeType, // req.file.mimetype,
                  size: '993.19',
                },
                {
                  where: {
                    id: findMediaDetails.id,
                  },
                }
              );
            } else {
              await Images.create({
                item_id: item_id,
                user_id: req.user_id,
                local_path: local_path ? local_path : null,
                resolution:
                  media_type.toLowerCase() === 'image'
                    ? imageWidth + '*' + imageHeight
                    : null,
                AI_objects: imageLabels ? imageLabels : null, // commit for node-rekognition library
                duration: null,
                media_type: media_type,
                media: req.file.originalname, // req.file.originalname,
                source: source ? source : '',
                extention: mimeType, // req.file.mimetype,
                size: '993.19',
              });
            }

            var image_details = await Images.findOne({
              where: {
                item_id: id,
              },
            });
          }

          var items = await Items.findOne({
            where: {
              item_id: item_id,
              is_deleted: 0,
            },
            attributes: {
              include: ['createdAt', 'is_upload'],
            },
          });

          // Activity Logs
          if (items) {
            let activityMsg =
              media_type.toLowerCase() === 'video' ||
              media_type.toLowerCase() === 'audio' ||
              media_type.toLowerCase() === 'image' ||
              media_type.toLowerCase() === 'pdf' ||
              media_type.toLowerCase() === 'doc'
                ? // ? `${req.file.originalname} was added`
                  `${fileName} was added`
                : '';

            if (image_details) {
              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Add Item',
                description:
                  activityMsg || 'Item, Media ,Sharing_info gets added.',
              });
            } else if (image_details) {
              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Add Item',
                description: activityMsg || 'Item and Media gets added.',
              });
            } else {
              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Add Item',
                description: activityMsg || 'Item gets added.',
              });
            }
          }
        }
      }
    } else {
      // Calculate total storage before the loop
      const extraTotalStorage =
        user.dataValues.total_storage + user.dataValues.extra_storage;
      const totalUploadedStorage =
        user.dataValues.used_storage + uploadFileSize;
      if (extraTotalStorage <= totalUploadedStorage) {
        return 1;
      }

      const items = [];
      const currentDateTime = moment(uploadedAt).utc();

      for (let index = 0; index < bulkNumber; index++) {
        const newId = (bigNum + BigInt(index)).toString();
        console.log(
          '🚀 ~ file: items.service.js:1971 ~ bulkAdd ~ newId:',
          newId
        );

        const find_item = await Items.findOne({
          where: { item_id: newId, is_deleted: 0, user_id: req.user_id },
        });

        if (find_item) {
          await Items.update(
            { title, description, item_type: media_type },
            { where: { item_id: newId } }
          );
        } else {
          items.push({
            item_id: newId,
            user_id: req.user_id,
            title,
            description,
            item_type: media_type,
            uploadedAt: currentDateTime,
          });
        }
      }

      // Bulk create if there are new items
      if (items.length > 0) {
        await Items.bulkCreate(items);
      }

      // Update activity logs
      const activityMsg = `${fileName} was added`;
      await Activity_logs.create({
        user_id: req.user_id,
        title: 'Add Item',
        description: activityMsg,
      });
    }

    return 0;
  },

  /** New Optimize Code */
  async newAdd(req, res) {
    const body = req.body;
    const userId = req.user_id;
    var items_tag_lists = [];
    var item_note = {};
    var OCRLabelRes = {};
    const itemObj = {};
    const promises = [];
    const newPromises = [];
    var img = [];
    let ocrLabelString = '';
    // var item_id = String(body.id);
    let aiLabels,
      ocrLabels,
      fileSize,
      videoLabels,
      buffer,
      base64,
      hours,
      minutes,
      seconds,
      duration_in_sec,
      file_size,
      link_preview,
      newUserAiToken;

    body.uploadedAt = moment(body.uploadedAt).utc();
    body.uploadFileSize = Number(body.uploadFileSize);

    const [
      user,
      findItem,
      findMediaDetails,
      findItemSharingDetails,
      findOcrDetails,
      TokenDeductionDetails,
    ] = await Promise.all([
      User.findOne({
        where: {
          user_id: userId,
        },
      }),
      Items.findOne({
        where: {
          item_id: body.id,
          is_deleted: 0,
          user_id: userId,
        },
      }),
      Images.findOne({
        where: {
          item_id: body.id,
        },
      }),
      Sharing_details.findOne({
        where: {
          item_id: body.id,
        },
      }),
      OCRLabels.findOne({
        where: {
          item_id: body.id,
        },
      }),
      TokenDeductions.findOne(),
    ]);

    const extraTotalStorage =
      user.dataValues.total_storage + user.dataValues.extra_storage;
    const totalUploadedStorage =
      user.dataValues.used_storage + body.uploadFileSize;

    if (
      extraTotalStorage <= totalUploadedStorage &&
      (body.media_type.toLowerCase() === 'image' ||
        body.media_type.toLowerCase() === 'video' ||
        body.media_type.toLowerCase() === 'audio' ||
        body.media_type.toLowerCase() === 'pdf' ||
        body.media_type.toLowerCase() === 'doc' ||
        body.media_type.toLowerCase() === 'location')
    ) {
      await gcpDelete(
        constant.GCP_BUCKET_FOLDER +
          constant.GCP_USER_FOLDER +
          userId +
          '/' +
          constant.GCP_ITEM_FOLDER +
          body.id +
          '/' +
          body.fileName
      );

      return 1;
    }

    // itemObj.user_id = userId
    // itemObj.title = body.media_type.toLowerCase() === "link" ? link_preview.title : body.title
    // itemObj.description = body.media_type.toLowerCase() === "link" ? link_preview.description : body.description
    // itemObj.item_type = body.media_type
    // itemObj.uploadedAt = body.uploadedAt

    // if (findItem) {
    //   // promises.push(Items.update(itemObj, { where: { item_id: body.id } }));
    //   await Items.update(itemObj, { where: { item_id: body.id } });
    // } else {
    //   itemObj.item_id = body.id

    //   // promises.push(Items.create(itemObj));
    //   await Items.create(itemObj);
    // }

    if (body.media_type.toLowerCase() === 'link') {
      try {
        link_preview = await getLinkPreview(body.link, {
          headers: {
            'user-agent': 'googlebot',
            'Accept-Language': 'en-US',
          },
          imagesPropertyType: 'og',
          followRedirects: 'follow',
          timeout: 2000,
        });
        console.log(
          '🚀 ~ file: items.service.js:568 ~ add ~ link_preview:',
          link_preview
        );
      } catch (error) {
        console.log('🚀 ~ file: items.service.js:570 ~ add ~ error:', error);
        res.status(status.INTERNALSERVERERRORSTATUS).send({
          data: [],
          message: message.LINKNOTFOUND,
          status: status.ERROR,
        });
      }
    }

    if (
      body.media_type.toLowerCase() === 'text' &&
      body.description &&
      user &&
      user.dataValues
    ) {
      let urlsFromDescription = extractUrls(body.description) || [];
      if (urlsFromDescription?.length) {
        try {
          let hyperText = null;
          if (urlsFromDescription && urlsFromDescription[0]) {
            hyperText = this.prepareUrlForLinkPreview(urlsFromDescription);
          }
          link_preview = await getLinkPreview(hyperText, {
            headers: {
              'user-agent':
                'googlebot Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
              'Accept-Language': 'en-US',
            },
            imagesPropertyType: 'og',
            followRedirects: 'follow',
            timeout: 2000,
          });
          if (link_preview.description) {
            link_preview.description =
              link_preview.title +
              '<br /> ' +
              `<p>${link_preview.description}<p/>`;
          }
          if (link_preview.title) link_preview.title = body.description;
          if (hyperText) body.link = hyperText;
          body.media_type = 'LINK';

          if (!link_preview.description) {
            let linkPreview = await image_size.fetchPreview(hyperText);

            link_preview = {};
            link_preview.title = linkPreview.title;
            link_preview.description = linkPreview.description;
            link_preview.images = [linkPreview.image];
            link_preview.videos = [linkPreview.video];
          }
        } catch (error) {
          console.log('🚀 ~ file: items.service.js:616 ~ add ~ error:', error);
          link_preview = {};
          link_preview.title = body.title;
          link_preview.description = body.description;
        }
      }
    }

    itemObj.user_id = userId;
    itemObj.title =
      body.media_type.toLowerCase() === 'link'
        ? link_preview.title
        : body.title;
    itemObj.description =
      body.media_type.toLowerCase() === 'link'
        ? /*link_preview.description*/ body.description
        : body.description;
    itemObj.item_type = body.media_type;
    itemObj.uploadedAt = body.uploadedAt;

    if (findItem) {
      // promises.push(Items.update(itemObj, { where: { item_id: body.id } }));
      await Items.update(itemObj, { where: { item_id: body.id } });
    } else {
      itemObj.item_id = body.id;
      // promises.push(Items.create(itemObj));
      await Items.create(itemObj);
    }

    if (
      body.media_type.toLowerCase() === 'contact' ||
      body.media_type.toLowerCase() === 'location' ||
      body.media_type.toLowerCase() === 'link'
    ) {
      const itemSharingObj = {
        name: body.media_type.toLowerCase() === 'contact' ? body.name : null,
        phone_number:
          body.media_type.toLowerCase() === 'contact'
            ? body.phone_number
            : null,
        longitude:
          body.media_type.toLowerCase() === 'location' ? body.longitude : null,
        latitude:
          body.media_type.toLowerCase() === 'location' ? body.latitude : null,
        link: body.media_type.toLowerCase() === 'link' ? body.link : null,
        link_preview_image:
          link_preview && body.media_type.toLowerCase() === 'link'
            ? link_preview.images[0]
            : null,
        link_preview_video:
          link_preview && body.media_type.toLowerCase() === 'link'
            ? link_preview.videos[0]
            : null,
      };

      if (findItemSharingDetails) {
        // promises.push(Sharing_details.update(itemSharingObj, { where: { item_id: body.id } }))
        await Sharing_details.update(itemSharingObj, {
          where: { item_id: body.id },
        });
      } else {
        itemSharingObj.item_id = body.id;

        // promises.push(Sharing_details.create(itemSharingObj))
        await Sharing_details.create(itemSharingObj);
      }
      // promises.push(
      //   Sharing_details.upsert({
      //     item_id: body.id,
      //     name: body.media_type.toLowerCase() === "contact" ? body.name : null,
      //     phone_number:
      //       body.media_type.toLowerCase() === "contact" ? body.phone_number : null,
      //     longitude: body.media_type.toLowerCase() === "location" ? body.longitude : null,
      //     latitude: body.media_type.toLowerCase() === "location" ? body.latitude : null,
      //     link: body.media_type.toLowerCase() === "link" ? body.link : null,
      //     link_preview_image:
      //       link_preview &&
      //         body.media_type.toLowerCase() === "link"
      //         ? link_preview.images[0]
      //         : null,
      //     link_preview_video:
      //       link_preview &&
      //         // user.dataValues.is_web_link_preview_set === true &&
      //         body.media_type.toLowerCase() === "link"
      //         ? link_preview.videos[0]
      //         : null,
      //   }, { returning: true, fields: ['item_id'] })
      // )
    }

    if (
      body.media_type.toLowerCase() === 'image' ||
      body.media_type.toLowerCase() === 'video' ||
      body.media_type.toLowerCase() === 'audio' ||
      body.media_type.toLowerCase() === 'pdf' ||
      body.media_type.toLowerCase() === 'doc' ||
      body.media_type.toLowerCase() === 'location'
    ) {
      const path =
        constant.GCP_BUCKET_FOLDER +
        constant.GCP_USER_FOLDER +
        userId +
        '/' +
        constant.GCP_ITEM_FOLDER +
        body.id +
        '/' +
        body.fileName;

      if (
        findMediaDetails &&
        findMediaDetails.media &&
        findMediaDetails.is_upload === true
      ) {
        await gcpDelete(
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            userId +
            '/' +
            constant.GCP_ITEM_FOLDER +
            findMediaDetails.dataValues.item_id +
            '/' +
            findMediaDetails.dataValues.media
        );
      }

      if (body.media_type.toLowerCase() === 'image') {
        newUserAiToken =
          user.dataValues.used_ai_api +
          Number(TokenDeductionDetails.token_per_image);
        // if ((user.dataValues.AI_set === true) && (user.dataValues.total_ai_api > user.dataValues.used_ai_api)) {
        if (
          user.dataValues.AI_set === true &&
          user.dataValues.total_ai_api >= newUserAiToken
        ) {
          // promises.push(User.update({ used_ai_api: user.dataValues.used_ai_api + 1 }, { where: { user_id: userId } }));
          await User.update(
            { used_ai_api: newUserAiToken },
            { where: { user_id: userId } }
          );
          // await User.update({ used_ai_api: user.dataValues.used_ai_api + 1 }, { where: { user_id: userId } });

          // newPromises.push(
          //   gcpUtils.imageLabelDetectionAndGetResults(
          //     constant.GCP_BUCKET_FOLDER +
          //     constant.GCP_USER_FOLDER +
          //     userId +
          //     "/" +
          //     constant.GCP_ITEM_FOLDER +
          //     body.id +
          //     "/" +
          //     body.fileName, //req.file.originalname,
          //     (user.dataValues.AI_confidence_level / 100)
          //   )
          // )

          if (
            !findMediaDetails ||
            findMediaDetails?.dataValues?.AI_objects?.Labels.length === 0
          ) {
            aiLabels = await gcpUtils.imageLabelDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                userId +
                '/' +
                constant.GCP_ITEM_FOLDER +
                body.id +
                '/' +
                body.fileName, //req.file.originalname,
              user.dataValues.AI_confidence_level / 100
            );
          } else {
            aiLabels = findMediaDetails?.dataValues?.AI_objects || aiLabels;
          }
        }
        // else {
        //   console.log("2");

        //   newPromises.push(null)
        // }

        if (user.dataValues.OCR_set === true) {
          // find and store OCR labels
          // newPromises.push(
          //   gcpUtils.imageOCRDetectionAndGetResults(
          //     constant.GCP_BUCKET_FOLDER +
          //     constant.GCP_USER_FOLDER +
          //     userId +
          //     "/" +
          //     constant.GCP_ITEM_FOLDER +
          //     body.id +
          //     "/" +
          //     body.fileName // req.file.originalname
          //   )
          // )

          if (!findOcrDetails) {
            ocrLabels = await gcpUtils.imageOCRDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                userId +
                '/' +
                constant.GCP_ITEM_FOLDER +
                body.id +
                '/' +
                body.fileName // req.file.originalname
            );
          } else {
            ocrLabels = findOcrDetails?.dataValues?.ocr_label;
          }

          // // Storing detected text as string in database for faster search query
          // let ocrLabelString = ""
          // if (OCR) {
          //   OCR.forEach((each) => {
          //     if (each.Type == "LINE") {
          //       ocrLabelString += ` ${each.DetectedText}`
          //     }
          //   })
          // }

          // OCRLabelRes = await OCRLabels.create({
          //   item_id: item_id,
          //   ocr_label: OCR,
          //   ocr_label_string: ocrLabelString
          // });
        }
        // else {
        //   console.log("0");

        //   newPromises.push(null)
        // }
      }

      if (body.media_type.toLowerCase() === 'video') {
        // const videoTotalUseToken = TokenDeductionDetails.token_per_video_second * body.durationInSec;
        const videoTotalUseToken = (
          Number(TokenDeductionDetails.token_per_video_second) *
          Number(body.durationInSec)
        ).toFixed(2);
        newUserAiToken =
          user.dataValues.used_ai_api + Number(videoTotalUseToken);
        // if ((user.dataValues.AI_set === true) && (user.dataValues.total_ai_api > user.dataValues.used_ai_api)) {
        if (
          user.dataValues.AI_set === true &&
          user.dataValues.total_ai_api >= newUserAiToken
        ) {
          // promises.push(User.update({ used_ai_api: user.dataValues.used_ai_api + 1 }, { where: { user_id: userId } }));
          await User.update(
            { used_ai_api: newUserAiToken },
            { where: { user_id: userId } }
          );
          // await User.update({ used_ai_api: user.dataValues.used_ai_api + 1 }, { where: { user_id: userId } });

          // newPromises.push(
          //   gcpUtils.videoLabelDetectionAndGetResults(
          //     constant.GCP_BUCKET_FOLDER +
          //     constant.GCP_USER_FOLDER +
          //     userId +
          //     "/" +
          //     constant.GCP_ITEM_FOLDER +
          //     body.id +
          //     "/" +
          //     body.fileName,
          //     (user.dataValues.AI_confidence_level / 100)
          //   )
          // )

          if (
            !findMediaDetails ||
            findMediaDetails?.dataValues?.AI_objects?.Labels.length === 0
          ) {
            aiLabels = await gcpUtils.videoLabelDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                userId +
                '/' +
                constant.GCP_ITEM_FOLDER +
                body.id +
                '/' +
                body.fileName,
              user.dataValues.AI_confidence_level / 100
            );
          }
        } else {
          aiLabels = findMediaDetails?.dataValues?.AI_objects || aiLabels;
        }
        // else {
        //   console.log("1");

        //   newPromises.push(null)
        // }

        if (user.dataValues.OCR_set === true) {
          // find and store OCR labels
          // newPromises.push(
          // gcpUtils.videoOCRDetectionAndGetResults(
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   userId +
          //   "/" +
          //   constant.GCP_ITEM_FOLDER +
          //   body.id +
          //   "/" +
          //   body.fileName
          // )
          // )

          if (!findOcrDetails) {
            ocrLabels = await gcpUtils.videoOCRDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                userId +
                '/' +
                constant.GCP_ITEM_FOLDER +
                body.id +
                '/' +
                body.fileName
            );
          } else {
            ocrLabels = findOcrDetails?.dataValues?.ocr_label;
          }
        }
        // else {
        //   console.log("3");

        //   newPromises.push(null)
        // }

        hours = Math.floor(body.durationInSec / 3600);
        minutes = Math.floor(body.durationInSec / 60);
        seconds = Math.floor(body.durationInSec % 60);
      }

      if (body.media_type.toLowerCase() === 'audio') {
        hours = Math.floor(body.durationInSec / 3600);
        minutes = Math.floor(body.durationInSec / 60);
        seconds = Math.floor(body.durationInSec % 60);
      }

      // if (body.media_type.toLowerCase() !== "image" && body.media_type.toLowerCase() !== "video") {
      //   console.log("4");

      //   newPromises.push(null)
      //   newPromises.push(null)
      // }

      // newPromises.push(image_size.newSizeOfGCPFile(path))
      fileSize = await image_size.newSizeOfGCPFile(path);
      // if (body.media_type.toLowerCase() === "image" || body.media_type.toLowerCase() === "video") {
      // console.log(" newPromises 2373::::", newPromises);

      // let [aiLabels, ocrLabels, fileSize] = await Promise.all(newPromises)
      // }
      const mediaObj = {
        user_id: userId,
        local_path: body.local_path ? body.local_path : null,
        resolution:
          body.media_type.toLowerCase() === 'image'
            ? body.imageWidth + '*' + body.imageHeight
            : null,
        AI_objects: aiLabels,
        duration:
          body.media_type.toLowerCase() === 'video' ||
          body.media_type.toLowerCase() === 'audio'
            ? hours + ':' + minutes + ':' + seconds
            : null,
        media_type: body.media_type,
        media: body.fileName,
        source: body.source ? body.source : '',
        extention: body.mimeType,
        size: Math.round(Number(fileSize / 1000) * 100) / 100,
      };

      if (findMediaDetails) {
        // promises.push(Images.update(mediaObj, { where: { item_id: body.id } }))
        await Images.update(mediaObj, { where: { item_id: body.id } });
      } else {
        mediaObj.item_id = body.id;

        // promises.push(Images.create(mediaObj))
        await Images.create(mediaObj);
      }
      // promises.push(
      //   Images.upsert({
      //     item_id: body.item_id,
      //     user_id: userId,
      //     local_path: body.local_path ? body.local_path : null,
      //     resolution: body.media_type.toLowerCase() === "image" ? body.imageWidth + "*" + body.imageHeight : null,
      //     AI_objects: aiLabels,
      //     duration: body.media_type.toLowerCase() === "video" ||
      //       body.media_type.toLowerCase() === "audio"
      //       ? hours + ":" + minutes + ":" + seconds
      //       : null,
      //     media_type: body.media_type,
      //     media: body.fileName,
      //     source: body.source ? body.source : "",
      //     extention: body.mimeType, // req.file.mimetype,
      //     // size: Math.round(Number((file_size.metadata.size) / 1000) * 100) / 100,
      //     size: (Math.round(Number((fileSize) / 1000) * 100) / 100),
      //   // }, { returning: true, fields: ['item_id'] })
      //   }, { updateOnDuplicate: ['item_id'] })
      // )

      if (findOcrDetails) {
        await OCRLabels.update(
          {
            ocr_label: findOcrDetails?.dataValues?.ocrLabels || ocrLabels,
            ocr_label_string:
              findOcrDetails?.dataValues?.ocrLabelString || ocrLabelString,
          },
          { where: { item_id: body.id } }
        );
      } else {
        if (ocrLabels?.length) {
          ocrLabels.forEach((each) => {
            if (each.Type == 'LINE') {
              ocrLabelString += ` ${each.DetectedText}`;
            }
          });

          // promises.push(
          await OCRLabels.create({
            item_id: body.id,
            ocr_label: ocrLabels,
            ocr_label_string: ocrLabelString,
          });
          // )
        }
      }
    }

    // await Promise.all(promises);
    // console.log("🚀 ~ file: items.service.js:2383 ~ newAdd ~ promises:", promises)

    const [item, mediaDetails, sharingDetails, ocrDetails] = await Promise.all([
      Items.findOne({
        where: {
          item_id: body.id,
        },
        attributes: {
          include: ['createdAt', 'is_upload'],
        },
      }),
      Images.findOne({
        where: {
          item_id: body.id,
        },
      }),
      Sharing_details.findOne({
        where: {
          item_id: body.id,
        },
        attributes: {
          exclude: ['item_id', 'updatedAt'],
        },
      }),
      OCRLabels.findOne({
        where: {
          item_id: body.id,
        },
      }),
    ]);

    // Activity Logs
    if (item) {
      let activityMsg =
        body.media_type.toLowerCase() === 'video' ||
        body.media_type.toLowerCase() === 'audio' ||
        body.media_type.toLowerCase() === 'image' ||
        body.media_type.toLowerCase() === 'pdf' ||
        body.media_type.toLowerCase() === 'doc'
          ? `${body.fileName} was added`
          : '';

      if (body.media_type.toLowerCase() === 'create_doc') {
        activityMsg = `${body.title}.pdf was added`;
      }
      let description;

      let itemType =
        body.media_type.charAt(0).toUpperCase() +
        body.media_type.substr(1).toLowerCase();

      description = activityMsg || `${itemType} added.`;

      // if (mediaDetails && sharingDetails) {
      //   description = activityMsg || "Item, Media And Sharing_info added."
      // } else if (mediaDetails) {
      //   description = activityMsg || "Item and Media added."
      // } else if (sharingDetails) {
      //   description = activityMsg || "Item and SharingInfo added."
      // } else {
      //   description = activityMsg || "Item added."
      // }

      await Activity_logs.create({
        user_id: userId,
        title: 'Add Item',
        description: description,
      });
    }

    if (sharingDetails) {
      sharingDetails.dataValues.createdAt = sharingDetails.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace('T', ' ');
      item.dataValues.item_sharing_details = sharingDetails.dataValues;
    } else {
      item.dataValues.item_sharing_details = {};
    }

    item.dataValues.createdAt = item.dataValues.createdAt
      .toISOString()
      .substr(0, 19)
      .replace('T', ' ');
    item.dataValues.uploadedAt = item.dataValues.uploadedAt
      .toISOString()
      .substr(0, 19)
      .replace('T', ' ');

    if (mediaDetails) {
      console.log('2457');

      mediaDetails.dataValues.mediaName = mediaDetails.dataValues.media;
      mediaDetails.dataValues.createdAt = mediaDetails.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace('T', ' ');
      mediaDetails.dataValues.updatedAt = mediaDetails.dataValues.updatedAt
        .toISOString()
        .substr(0, 19)
        .replace('T', ' ');

      if (
        mediaDetails.dataValues.size !== null &&
        body.media_type.toLowerCase() !== 'create_doc'
      ) {
        item.dataValues.size = mediaDetails.size.toString();
        let imgs = image_size.formatSize(mediaDetails.size * 1000);
        let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;
        mediaDetails.dataValues.size = b.toString() + ' ' + imgs.split(' ')[1];
      }

      item.dataValues.image_details = mediaDetails.dataValues;
      img.push(item.dataValues.image_details);

      item.dataValues.image_details = img;
    } else {
      item.dataValues.size = '0';
    }

    item.dataValues.web_link_preview_set =
      user.dataValues.is_web_link_preview_set;
    item.dataValues.AI_set = user.dataValues.AI_set;
    item.dataValues.items_tag_lists = items_tag_lists;
    item.dataValues.item_note = item_note;
    item.dataValues.item_ocr_labels = ocrDetails ? ocrDetails : OCRLabelRes;

    return item;
  },

  /* uploadMedia into cloud */
  async uploadMedia(req, res) {
    let { item_id } = req.body;
    let path;

    if (req.file) {
      path =
        constant.GCP_BUCKET_FOLDER +
        constant.GCP_USER_FOLDER +
        req.user_id +
        '/' +
        constant.GCP_ITEM_FOLDER +
        item_id +
        '/' +
        req.file.originalname;

      await gcpUpload(req.file, path);

      const mediaLink = await gcpUtils.getPrivateUrl(
        constant.GCP_BUCKET_NAME,
        constant.GCP_BUCKET_FOLDER +
          constant.GCP_USER_FOLDER +
          req.user_id +
          '/' +
          constant.GCP_ITEM_FOLDER +
          item_id +
          '/' +
          req.file.originalname /*expires: '2023-12-02' */
      );

      return { mediaLink, fileName: req.file.originalname };
    }

    return;
  },

  /* delete */
  async delete(req, res) {
    let { item_id, delete_type, item_types, is_select_all, unchecked_ids } =
      req.body;
    var item_ids;
    // item_types = ['image','link','text','pdf'];
    item_types = JSON.parse(item_types);

    // manage select all feature
    if (is_select_all) {
      let where = [
        {
          user_id: req.user_id,
          is_deleted: 0,
        },
      ];

      if (unchecked_ids) {
        // unselected items
        let unchecked_item = JSON.parse(unchecked_ids);
        where.push({
          item_id: {
            [Op.notIn]: unchecked_item,
          },
        });
      }

      if (item_types && item_types.length /*&& Array.isArray(item_types)*/) {
        console.log('in');

        where.push({
          item_type: {
            [Op.in]: item_types, // Match multiple item types
          },
        });
      }

      let find_all_item = await Items.findAndCountAll({
        where,
        attributes: ['item_id'],
      });

      item_ids = find_all_item.rows.map((item) => item.dataValues.item_id);
    } else {
      item_ids = JSON.parse(item_id);
    }

    var find_item = await Items.findOne({
      where: {
        item_id: item_ids,
        user_id: req.user_id,
        is_deleted: 0,
      },
    });
    var find_img = await Images.findAll({
      where: {
        item_id: item_ids,
        user_id: req.user_id,
        is_deleted: 0,
      },
    });
    var sharing_details = await Sharing_details.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 0,
      },
    });

    if (find_item) {
      let activityLogArr;
      if (find_img) {
        activityLogArr = find_img.map((item) => {
          const itemType =
            item.dataValues.media_type.charAt(0).toUpperCase() +
            item.dataValues.media_type.substr(1).toLowerCase();
          // return {
          //   user_id: req.user_id,
          //   title: "Delete Item",
          //   description: item.dataValues.media
          //     ? `${item.dataValues.media} added to trash.`
          //     : "Item added to trash.",
          // };
          return {
            user_id: req.user_id,
            title: 'Delete Item',
            description: item.dataValues.media
              ? `${item.dataValues.media} moved to trash.`
              : `${itemType} moved to trash.`,
          };
        });
      }
      if (delete_type.toLowerCase() === 'cloud') {
        if (sharing_details) {
          await Sharing_details.update(
            {
              is_deleted: 1,
            },
            {
              where: {
                item_id: item_ids,
              },
            }
          );
        }
        /** Removed code to change storage calculation */
        // if (find_img) {
        //   await Images.update(
        //     {
        //       is_deleted: 1,
        //     },
        //     {
        //       where: {
        //         item_id: item_ids,
        //       },
        //     }
        //   );
        // }
        await Tags.update(
          {
            is_deleted: 1,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );
        await Notes.update(
          {
            is_deleted: 1,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );

        await Items.update(
          {
            is_deleted: 1,
            delete_type: delete_type,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );
      }
      if (delete_type.toLowerCase() === 'mobile') {
        await Items.update(
          {
            delete_type: delete_type,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );
      }
      await Activity_logs.bulkCreate(activityLogArr);
      return 1;
    }
    {
      return 0;
    }
  },

  /* edit */
  async edit(req, res) {
    let { title, description, item_id, media_type, source, local_path } =
      req.body;
    var userInfo = await User.findOne({
      where: {
        user_id: req.user_id,
      },
    });

    var find_item = await Items.findOne({
      where: {
        item_id: item_id,
        is_deleted: 0,
      },
    });
    var find_img = await Images.findOne({
      where: {
        item_id: item_id,
        is_deleted: 0,
      },
    });
    if (find_item) {
      if (find_img) {
        let imageLabels,
          buffer,
          base64,
          duration_in_sec,
          hours,
          minutes,
          seconds;
        await Items.update(
          {
            title: title,
            description: description,
            item_type: media_type,
          },
          {
            where: { item_id: item_id, is_deleted: 0 },
          }
        );
        var items = await Items.findOne({
          where: {
            item_id: item_id,
            is_deleted: 0,
          },
          include: [
            {
              model: Notes,
              as: 'items_note_lists',
              attributes: {
                exclude: ['item_id'],
              },
            },
            {
              model: Tags,
              as: 'items_tag_lists',
              attributes: {
                exclude: ['item_id', 'id'],
              },

              include: [
                {
                  model: Tag_names,
                  as: 'items_tag_names',
                  attributes: {
                    exclude: ['tag_name_id', 'user_id'],
                  },
                },
              ],
            },
          ],

          attributes: {
            include: ['createdAt', 'updatedAt', 'is_upload'],
          },
        });
        if (req.file) {
          if (find_img.dataValues.media) {
            if (items && items.is_upload === true) {
              await gcpDelete(
                constant.GCP_BUCKET_FOLDER +
                  constant.GCP_USER_FOLDER +
                  req.user_id +
                  '/' +
                  constant.GCP_ITEM_FOLDER +
                  find_img.item_id +
                  '/' +
                  find_img.dataValues.media
              );
            }

            await gcpUpload(
              req.file,
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                find_img.item_id +
                '/' +
                req.file.originalname
            );
          }
          if (media_type.toLowerCase() === 'video') {
            let data = await getVideoDurationInSeconds(req.file.path);
            duration_in_sec = data;
            hours = Math.floor(duration_in_sec / 3600);
            minutes = Math.floor(duration_in_sec / 60);
            seconds = Math.floor(duration_in_sec % 60);
          }
          if (media_type.toLowerCase() === 'audio') {
            let data = await getAudioDurationInSeconds(req.file.path);
            duration_in_sec = data;
            hours = Math.floor(duration_in_sec / 3600);
            minutes = Math.floor(duration_in_sec / 60);
            seconds = Math.floor(duration_in_sec % 60);
          }
          if (media_type.toLowerCase() === 'image') {
            imageLabels = await gcpUtils.imageLabelDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                req.body.id +
                '/' +
                req.file.originalname,
              user.dataValues.AI_confidence_level / 100
            );
          } else {
            imageLabels = null;
          }

          const dimensions =
            media_type.toLowerCase() === 'image' ? sizeOf(req.file.path) : null;
          await Images.update(
            {
              media_type: req.file ? media_type : null,

              resolution:
                req.file && media_type.toLowerCase() === 'image'
                  ? dimensions.width + '*' + dimensions.height
                  : null,
              AI_objects:
                req.file && media_type.toLowerCase() === 'image'
                  ? imageLabels
                  : null, // commit for node-rekognition library
              user_id: req.user_id,
              media: req.file && req.file.originalname,
              duration:
                media_type.toLowerCase() === 'video' ||
                media_type.toLowerCase() === 'audio'
                  ? hours + ':' + minutes + ':' + seconds
                  : null,

              extention: req.file ? req.file.mimetype : null,
              source: source ? source : '',
              size: req.file
                ? Math.round(Number(req.file.size / 1000) * 100) / 100
                : null,
            },
            { where: { item_id: item_id, is_deleted: 0 } }
          );

          var image_details = await Images.findOne({
            where: {
              item_id: item_id,
              is_deleted: 0,
            },
          });
          if (items) {
            if (image_details) {
              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Update Item',
                // description: "Item and Media gets edited.",
                description: `${image_details.dataValues.media} edited.`,
              });
            } else {
              const itemType =
                items.dataValues.item_type.charAt(0).toUpperCase() +
                items.dataValues.item_type.substr(1).toLowerCase();

              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Update Item',
                description: `${itemType} edited.`,
              });
            }
          }
          items.dataValues.size = image_details.size.toString();
          let imgs = image_size.formatSize(req.file.size);
          let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;
          var img = [];
          items.dataValues.createdAt = items.dataValues.createdAt
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          items.dataValues.updatedAt = items.dataValues.updatedAt
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          // image_details.dataValues.media =
          //   constant.GCP_URL +
          //   constant.GCP_BUCKET_NAME +
          //   "/" +
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   image_details.dataValues.user_id +
          //   "/" +
          //   constant.GCP_ITEM_FOLDER +
          //   image_details.dataValues.item_id +
          //   "/" +
          //   image_details.dataValues.media;
          // image_details.dataValues.media = await gcpUtils.getPrivateUrl(
          //   constant.GCP_BUCKET_NAME,
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   image_details.dataValues.user_id +
          //   "/" +
          //   constant.GCP_ITEM_FOLDER +
          //   image_details.dataValues.item_id +
          //   "/" +
          //   image_details.dataValues.media, /*expires: '2023-12-02' */
          // );
          image_details.dataValues.mediaName = image_details.dataValues.media;
          image_details.dataValues.createdAt =
            image_details.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
          image_details.dataValues.updatedAt =
            image_details.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
          items.dataValues.image_details = image_details;
          img.push(items.dataValues.image_details);
          items.dataValues.image_details = img;
          image_details.dataValues.size =
            b.toString() + ' ' + imgs.split(' ')[1];
        } else {
          if (items && items.is_upload === true) {
            await gcpDelete(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                find_img.item_id +
                '/' +
                find_img.dataValues.media
            );
          }

          await Images.destroy({
            where: {
              item_id: item_id,
            },
          });

          // if (media_type.toLowerCase() === "create_doc") {
          //   let options = { format: "A4" };
          //   let file = { content: req.body.description };
          //   let filename = Date.now() + `.pdf`;
          //   const path =
          //     constant.GCP_BUCKET_FOLDER +
          //     constant.GCP_USER_FOLDER +
          //     req.user_id +
          //     "/" +
          //     constant.GCP_ITEM_FOLDER +
          //     req.body.item_id +
          //     "/" +
          //     filename;
          //   await html_to_pdf
          //     .generatePdf(file, options)
          //     .then(async (pdfBuffer) => {
          //       await gcpBufferUpload(
          //         pdfBuffer,
          //         constant.GCP_BUCKET_FOLDER +
          //         constant.GCP_USER_FOLDER +
          //         req.user_id +
          //         "/" +
          //         constant.GCP_ITEM_FOLDER +
          //         req.body.item_id +
          //         "/" +
          //         filename
          //       )
          //     });
          //   var user = await User.findOne({
          //     where: {
          //       user_id: req.user_id,
          //     },
          //   });

          //   let pdf_size = await image_size.sizeOfGCPFile(path);
          //   var img = [];
          //   var image_details = await Images.create({
          //     item_id: item_id,
          //     user_id: req.user_id,
          //     resolution:
          //       media_type.toLowerCase() === "image"
          //         ? dimensions.width + "*" + dimensions.height
          //         : null,
          //     // AI_objects: media_type.toLowerCase() === "video" /*|| media_type.toLowerCase() === "image"*/ && user.dataValues.AI_set === true ? imageLabels : null,   // commit for node-rekognition library
          //     AI_objects:
          //       (media_type.toLowerCase() === "video" ||
          //         media_type.toLowerCase() === "image") &&
          //         ((user.dataValues.AI_set === true) && (user.dataValues.total_ai_api > user.dataValues.used_ai_api))
          //         ? imageLabels
          //         : null, // commit for node-rekognition library
          //     duration:
          //       media_type.toLowerCase() === "video" ||
          //         media_type.toLowerCase() === "audio"
          //         ? hours + ":" + minutes + ":" + seconds
          //         : null,
          //     local_path: local_path ? local_path : null,
          //     media_type: media_type,
          //     media: filename,
          //     source: source ? source : "",
          //     extention: "application/pdf",
          //     size: Math.round(Number((pdf_size.metadata.size) / 1000) * 100) / 100,
          //   });

          //   if (image_details) {
          //     image_details.dataValues.size = image_details.size.toString();
          //     let imgs = image_size.formatSize(pdf_size.metadata.size);
          //     let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;

          //     image_details.dataValues.size =
          //       b.toString() + " " + imgs.split(" ")[1];

          //     // image_details.dataValues.media =
          //     //   constant.GCP_URL +
          //     //   constant.GCP_BUCKET_NAME +
          //     //   "/" +
          //     //   constant.GCP_BUCKET_FOLDER +
          //     //   constant.GCP_USER_FOLDER +
          //     //   image_details.dataValues.user_id +
          //     //   "/" +
          //     //   constant.GCP_ITEM_FOLDER +
          //     //   image_details.dataValues.item_id +
          //     //   "/" +
          //     //   image_details.dataValues.media;
          //     // image_details.dataValues.media = await gcpUtils.getPrivateUrl(
          //     //   constant.GCP_BUCKET_NAME,
          //     //   constant.GCP_BUCKET_FOLDER +
          //     //   constant.GCP_USER_FOLDER +
          //     //   image_details.dataValues.user_id +
          //     //   "/" +
          //     //   constant.GCP_ITEM_FOLDER +
          //     //   image_details.dataValues.item_id +
          //     //   "/" +
          //     //   image_details.dataValues.media, /*expires: '2023-12-02' */
          //     // );
          //     image_details.dataValues.mediaName = image_details.dataValues.media
          //     image_details.dataValues.createdAt =
          //       image_details.dataValues.createdAt
          //         .toISOString()
          //         .substr(0, 19)
          //         .replace("T", " ");
          //     image_details.dataValues.updatedAt =
          //       image_details.dataValues.updatedAt
          //         .toISOString()
          //         .substr(0, 19)
          //         .replace("T", " ");
          //     items.dataValues.image_details = image_details;
          //     img.push(items.dataValues.image_details);
          //     items.dataValues.image_details = img;
          //   }
          // }
          const itemType =
            items.dataValues.item_type.charAt(0).toUpperCase() +
            items.dataValues.item_type.substr(1).toLowerCase();

          var item_activity_log = await Activity_logs.create({
            user_id: req.user_id,
            title: 'Update Item',
            description: `${itemType} edited.`,
          });
          items.dataValues.createdAt = items.dataValues.createdAt
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          items.dataValues.updatedAt = items.dataValues.updatedAt
            .toISOString()
            .substr(0, 19)
            .replace('T', ' ');
          // items.dataValues.size = "0";
        }

        // var items_tag_lists = [];
        // var item_note = {};

        let item_note = items.dataValues.items_note_lists;
        let items_tag_lists = items.dataValues.items_tag_lists;

        item_note.map((data) => {
          if (data.createdAt) {
            data.dataValues.createdAt = data.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
            data.dataValues.updatedAt = data.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');

            item_note.note_id = data.dataValues.id;
            item_note.note_description = data.dataValues.note_description;
            item_note.createdAt = data.dataValues.createdAt;
            item_note.updatedAt = data.dataValues.updatedAt;
          }

          items.dataValues.item_note = item_note;
        });

        items_tag_lists.map((data, index) => {
          if (
            data.createdAt &&
            data.dataValues.is_deleted === false &&
            data.dataValues.items_tag_names !== null
          ) {
            data.dataValues.createdAt = data.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
            data.dataValues.updatedAt = data.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');

            if (data.dataValues.items_tag_names !== null) {
              data.dataValues.tag_name =
                data.items_tag_names.dataValues.tag_name;
            }
          }
        });

        items.dataValues.items_tag_lists = items_tag_lists;
        // items.dataValues.item_note = item_note;
        return items;
      } else {
        let link_preview = {},
          responseFromLinkPreview = false;
        const userResponse = await User.findOne({
          where: { user_id: req.user_id },
          raw: true,
        });

        if (media_type.toLowerCase() === 'text') {
          if (description) {
            if (userResponse /** && userResponse.is_web_link_preview_set */) {
              let urlsFromDescription = extractUrls(description) || [];
              if (urlsFromDescription?.length) {
                try {
                  let hyperText = null;

                  if (urlsFromDescription && urlsFromDescription[0])
                    hyperText =
                      this.prepareUrlForLinkPreview(urlsFromDescription);

                  link_preview = await getLinkPreview(hyperText, {
                    headers: {
                      'user-agent': 'googlebot',
                      'Accept-Language': 'en-US',
                    },
                    imagesPropertyType: 'og',
                    followRedirects: 'follow',
                    timeout: 2000,
                  });

                  if (link_preview.description) {
                    link_preview.description =
                      link_preview.title +
                      '<br /> ' +
                      `<p>${link_preview.description}<p/>`;
                  }
                  if (link_preview.title) link_preview.title = description;
                  if (hyperText) link = hyperText;
                  media_type = 'LINK';
                  responseFromLinkPreview = true;
                } catch (error) {
                  console.log(
                    '🚀 ~ file: items.service.js:1491 ~ edit ~ error:',
                    error
                  );
                  link_preview.title = title;
                  link_preview.description = description;
                  // console.log('TCL :: ->', error);
                }
              }
            }
          }
        }

        await Items.update(
          {
            title: link_preview.title ? link_preview.title : title,
            description: link_preview.description
              ? link_preview.description
              : description,
            item_type: media_type,
          },
          { where: { item_id: item_id, is_deleted: 0 } }
        );

        let sharing_info = {};
        if (responseFromLinkPreview) {
          if (userResponse) {
            sharing_info = await Sharing_details.create({
              item_id: item_id,
              name: null,
              phone_number: null,
              longitude: null,
              latitude: null,
              link: media_type.toLowerCase() === 'link' ? link : null,
              link_preview_image:
                link_preview &&
                // userResponse.is_web_link_preview_set &&
                media_type.toLowerCase() === 'link'
                  ? link_preview.images[0]
                  : null,
              link_preview_video:
                link_preview &&
                // userResponse.is_web_link_preview_set &&
                media_type.toLowerCase() === 'link'
                  ? link_preview.videos[0]
                  : null,
            });
          }
        }

        var items = await Items.findOne({
          where: {
            item_id: item_id,
            is_deleted: 0,
          },
          include: [
            {
              model: Notes,
              as: 'items_note_lists',
              attributes: {
                exclude: ['item_id'],
              },
            },
            {
              model: Tags,
              as: 'items_tag_lists',
              attributes: {
                exclude: ['item_id', 'id'],
              },

              include: [
                {
                  model: Tag_names,
                  as: 'items_tag_names',
                  attributes: {
                    exclude: ['tag_name_id', 'user_id'],
                  },
                },
              ],
            },
          ],
          attributes: {
            include: ['createdAt', 'updatedAt', 'is_upload'],
          },
        });
        // var items_tag_lists = [];
        // var item_note = {};

        let item_note = items.dataValues.items_note_lists;
        let items_tag_lists = items.dataValues.items_tag_lists;

        item_note.map((data) => {
          if (data.createdAt) {
            data.dataValues.createdAt = data.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
            data.dataValues.updatedAt = data.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');

            item_note.note_id = data.dataValues.id;
            item_note.note_description = data.dataValues.note_description;
            item_note.createdAt = data.dataValues.createdAt;
            item_note.updatedAt = data.dataValues.updatedAt;
          }

          items.dataValues.item_note = item_note;
        });

        items_tag_lists.map((data, index) => {
          if (
            data.createdAt &&
            data.dataValues.is_deleted === false &&
            data.dataValues.items_tag_names !== null
          ) {
            data.dataValues.createdAt = data.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
            data.dataValues.updatedAt = data.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');

            if (data.dataValues.items_tag_names !== null) {
              data.dataValues.tag_name =
                data.items_tag_names.dataValues.tag_name;
            }
          }
        });

        let imageLabels,
          buffer,
          base64,
          hours,
          minutes,
          seconds,
          duration_in_sec;

        if (req.file) {
          await gcpUpload(
            req.file,
            constant.GCP_BUCKET_FOLDER +
              constant.GCP_USER_FOLDER +
              req.user_id +
              '/' +
              constant.GCP_ITEM_FOLDER +
              items.item_id +
              '/' +
              req.file.originalname
          );

          if (media_type.toLowerCase() === 'video') {
            let data = await getVideoDurationInSeconds(req.file.path);
            duration_in_sec = data;
            hours = Math.floor(duration_in_sec / 3600);
            minutes = Math.floor(duration_in_sec / 60);
            seconds = Math.floor(duration_in_sec % 60);
          }
          if (media_type.toLowerCase() === 'audio') {
            let data = await getAudioDurationInSeconds(req.file.path);
            duration_in_sec = data;
            hours = Math.floor(duration_in_sec / 3600);
            minutes = Math.floor(duration_in_sec / 60);
            seconds = Math.floor(duration_in_sec % 60);
          }
          if (media_type.toLowerCase() === 'image') {
            imageLabels = await gcpUtils.imageLabelDetectionAndGetResults(
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                req.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                items.item_id +
                '/' +
                req.file.originalname,
              userInfo.dataValues.AI_confidence_level / 100
            );
          } else {
            imageLabels = null;
          }

          const dimensions =
            media_type.toLowerCase() === 'image' ? sizeOf(req.file.path) : null;
          var image_details = await Images.create({
            item_id: item_id,
            user_id: req.user_id,
            resolution:
              media_type.toLowerCase() === 'image'
                ? dimensions.width + '*' + dimensions.height
                : null,
            AI_objects:
              media_type.toLowerCase() === 'image' ? imageLabels : null, // commit for node-rekognition library
            local_path: local_path ? local_path : null,
            media_type: media_type,
            media: req.file.originalname,
            duration:
              media_type.toLowerCase() === 'video' ||
              media_type.toLowerCase() === 'audio'
                ? hours + ':' + minutes + ':' + seconds
                : null,
            source: source ? source : '',
            extention: req.file.mimetype,
            size: Math.round(Number(req.file.size / 1000) * 100) / 100,
          });

          var image_details = await Images.findOne({
            where: {
              item_id: item_id,
              is_deleted: 0,
            },
          });
          if (items) {
            if (image_details) {
              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Update Item',
                description: `${image_details.dataValues.media} edited.`,
              });
            } else {
              const itemType =
                items.dataValues.item_type.charAt(0).toUpperCase() +
                items.dataValues.item_type.substr(1).toLowerCase();

              var item_activity_log = await Activity_logs.create({
                user_id: req.user_id,
                title: 'Update Item',
                description: `${itemType} edited.`,
              });
            }
          }
          items.dataValues.size = image_details.size.toString();
          let imgs = image_size.formatSize(req.file.size);
          let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;

          var img = [];

          // image_details.dataValues.media =
          //   constant.GCP_URL +
          //   constant.GCP_BUCKET_NAME +
          //   "/" +
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   image_details.dataValues.user_id +
          //   "/" +
          //   constant.GCP_ITEM_FOLDER +
          //   image_details.dataValues.item_id +
          //   "/" +
          //   image_details.dataValues.media;
          // image_details.dataValues.media = await gcpUtils.getPrivateUrl(
          //   constant.GCP_BUCKET_NAME,
          //   constant.GCP_BUCKET_FOLDER +
          //   constant.GCP_USER_FOLDER +
          //   image_details.dataValues.user_id +
          //   "/" +
          //   constant.GCP_ITEM_FOLDER +
          //   image_details.dataValues.item_id +
          //   "/" +
          //   image_details.dataValues.media, /*expires: '2023-12-02' */
          // );
          image_details.dataValues.mediaName = image_details.dataValues.media;
          image_details.dataValues.createdAt =
            image_details.dataValues.createdAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
          image_details.dataValues.updatedAt =
            image_details.dataValues.updatedAt
              .toISOString()
              .substr(0, 19)
              .replace('T', ' ');
          items.dataValues.image_details = image_details;
          img.push(items.dataValues.image_details);
          items.dataValues.image_details = img;
          image_details.dataValues.size =
            b.toString() + ' ' + imgs.split(' ')[1];
        } else {
          items.dataValues.size = '0';
        }
        items.dataValues.uploadedAt = items.dataValues.uploadedAt
          .toISOString()
          .substr(0, 19)
          .replace('T', ' ');
        items.dataValues.createdAt = items.dataValues.createdAt
          .toISOString()
          .substr(0, 19)
          .replace('T', ' ');
        items.dataValues.updatedAt = items.dataValues.updatedAt
          .toISOString()
          .substr(0, 19)
          .replace('T', ' ');
        items.dataValues.items_tag_lists = items_tag_lists;
        items.dataValues.item_note = item_note;
        items.dataValues.item_sharing_details = sharing_info;
        return items;
      }
    }

    {
      return 0;
    }
  },

  /**
   * delete AI label
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async deleteAILabel(req, res) {
    let { id, name } = req.body;

    let AIDetails = await Images.findOne({
      where: {
        id,
      },
      attributes: ['AI_objects'],
    });

    if (AIDetails) {
      if (AIDetails.AI_objects) {
        let AILabels = AIDetails.AI_objects.Labels;

        AILabels.map((item) => {
          if (item.Name == name) {
            item.isDeleted = 1;
          }
        });

        let newLabel = { Labels: AILabels };
        await Images.update(
          {
            AI_objects: newLabel,
          },
          { where: { id } }
        );
      }

      return await Images.findOne({
        where: {
          id,
        },
        attributes: ['id', 'AI_objects'],
      });
    }
    return 0;
  },

  /**
   * reset AI label
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async resetAILabel(req, res) {
    let { id } = req.body;

    let AIDetails = await Images.findOne({
      where: {
        id,
      },
      attributes: ['AI_objects'],
    });

    if (AIDetails) {
      if (AIDetails.AI_objects) {
        let AILabels = AIDetails.AI_objects.Labels;

        AILabels.map((item) => {
          if ('isDeleted' in item) {
            delete item.isDeleted;
          }
        });

        let newLabel = { Labels: AILabels };
        await Images.update(
          {
            AI_objects: newLabel,
          },
          { where: { id } }
        );
      }
      return await Images.findOne({
        where: {
          id,
        },
        attributes: ['id', 'AI_objects'],
      });
    }
    return 0;
  },

  /**
   * reobserve AI
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async reobserveAILabel(req, res) {
    let { mediaId } = req.body;

    let AIDetails = await Images.findOne({
      where: {
        id: mediaId,
      },
      attributes: ['media', 'media_type', 'item_id'],
    });

    if (
      AIDetails &&
      (AIDetails.dataValues.media_type == 'IMAGE' ||
        AIDetails.dataValues.media_type == 'VIDEO')
    ) {
      let imageLabels;
      let user = await User.findOne({
        where: {
          user_id: req.user_id,
        },
        attributes: ['AI_confidence_level'],
      });

      if (AIDetails.dataValues.media_type == 'IMAGE') {
        // fetch AI labels
        imageLabels = await gcpUtils.imageLabelDetectionAndGetResults(
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            req.user_id +
            '/' +
            constant.GCP_ITEM_FOLDER +
            AIDetails.dataValues.item_id +
            '/' +
            AIDetails.dataValues.media,
          user.dataValues.AI_confidence_level / 100
        );
      } else {
        // fetch AI labels
        imageLabels = await gcpUtils.videoLabelDetectionAndGetResults(
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            req.user_id +
            '/' +
            constant.GCP_ITEM_FOLDER +
            AIDetails.dataValues.item_id +
            '/' +
            AIDetails.dataValues.media,
          user.dataValues.AI_confidence_level / 100
        );
      }

      // update AI labels
      await Images.update(
        {
          AI_objects: imageLabels,
        },
        { where: { id: mediaId } }
      );

      let item = await Items.findOne({
        where: {
          is_deleted: 0,
          item_id: AIDetails.dataValues.item_id,
        },
        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],
        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
        ],
      });

      let media_details = item.dataValues.image_details;
      if (media_details.length === 0) {
        item.dataValues.size = '0';
      }
      // media_details.map((data) => {
      //   data.dataValues.media =
      //     constant.GCP_URL +
      //     constant.GCP_BUCKET_NAME +
      //     "/" +
      //     constant.GCP_BUCKET_FOLDER +
      //     constant.GCP_USER_FOLDER +
      //     data.dataValues.user_id +
      //     "/" +
      //     constant.GCP_ITEM_FOLDER +
      //     data.dataValues.item_id +
      //     "/" +
      //     data.dataValues.media;
      //   let imgs = image_size.formatSize(data.dataValues.size * 1000);
      //   let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;

      //   let size = b.toString() + " " + imgs.split(" ")[1];
      //   item.dataValues.size = data.dataValues.size.toString();
      //   data.dataValues.size = size;
      // });

      if (media_details.length) {
        media_details = media_details[0];
        // media_details.dataValues.media = await gcpUtils.getPrivateUrl(
        //   constant.GCP_BUCKET_NAME,
        //   constant.GCP_BUCKET_FOLDER +
        //   constant.GCP_USER_FOLDER +
        //   media_details.dataValues.user_id +
        //   "/" +
        //   constant.GCP_ITEM_FOLDER +
        //   media_details.dataValues.item_id +
        //   "/" +
        //   media_details.dataValues.media, /*expires: '2023-12-02' */
        // );
        media_details.dataValues.mediaName = media_details.dataValues.media;
        let imgs = image_size.formatSize(media_details.dataValues.size * 1000);
        let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;

        let size = b.toString() + ' ' + imgs.split(' ')[1];
        item.dataValues.size = media_details.dataValues.size.toString();
        media_details.dataValues.size = size;
      }

      return item;
    }
    return 0;
  },

  /**
   * reobserve OCR
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async reobserveOCRLabel(req, res) {
    let { mediaId, OCRId } = req.body;
    let GetOCRLabels;

    let AIDetails = await Images.findOne({
      where: {
        id: mediaId,
      },
      attributes: ['media', 'media_type', 'item_id'],
    });

    if (
      AIDetails &&
      (AIDetails.dataValues.media_type == 'IMAGE' ||
        AIDetails.dataValues.media_type == 'VIDEO')
    ) {
      if (AIDetails.dataValues.media_type == 'IMAGE') {
        // fetch OCR labels
        OCR = await gcpUtils.imageOCRDetectionAndGetResults(
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            req.user_id +
            '/' +
            constant.GCP_ITEM_FOLDER +
            AIDetails.dataValues.item_id +
            '/' +
            AIDetails.dataValues.media
        );
      } else {
        // fetch OCR labels
        OCR = await gcpUtils.videoOCRDetectionAndGetResults(
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            req.user_id +
            '/' +
            constant.GCP_ITEM_FOLDER +
            AIDetails.dataValues.item_id +
            '/' +
            AIDetails.dataValues.media
        );
      }

      // Storing detected text as string in database for faster search query
      let ocrLabelString = '';
      if (OCR) {
        OCR.forEach((each) => {
          if (each.Type == 'LINE') {
            ocrLabelString += ` ${each.DetectedText}`;
          }
        });
      }

      if (OCRId) {
        // update OCR labels
        await OCRLabels.update(
          {
            ocr_label: OCR,
            ocr_label_string: ocrLabelString,
          },
          { where: { id: OCRId } }
        );
      } else {
        await OCRLabels.create({
          item_id: AIDetails.dataValues.item_id,
          ocr_label: OCR,
          ocr_label_string: ocrLabelString,
        });
      }

      let item = await Items.findOne({
        where: {
          is_deleted: 0,
          item_id: AIDetails.dataValues.item_id,
        },
        include: [
          {
            model: Images,
            as: 'image_details',
            attributes: {
              exclude: ['createdAt', 'updatedAt'],
            },
          },
          {
            model: Sharing_details,
            as: 'item_sharing_details',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Notes,
            as: 'items_note_lists',
            attributes: {
              exclude: ['item_id'],
            },
          },
          {
            model: Tags,
            as: 'items_tag_lists',
            attributes: {
              exclude: ['item_id', 'id'],
            },

            include: [
              {
                model: Tag_names,
                as: 'items_tag_names',
                attributes: {
                  exclude: ['tag_name_id', 'user_id'],
                },
              },
            ],
          },
          {
            model: OCRLabels,
            attributes: ['ocr_label', 'id'],
            as: 'item_ocr_labels',
          },
        ],
        attributes: [
          'item_id',
          'user_id',
          'title',
          'description',
          'item_type',
          'is_bookmarked',
          'is_private',
          'delete_type',
          'is_deleted',
          'is_upload',
          'createdAt',
        ],
      });

      let media_details = item.dataValues.image_details;
      if (media_details !== []) {
        item.dataValues.size = '0';
      }
      // media_details.map((data) => {
      //   data.dataValues.media =
      //     constant.GCP_URL +
      //     constant.GCP_BUCKET_NAME +
      //     "/" +
      //     constant.GCP_BUCKET_FOLDER +
      //     constant.GCP_USER_FOLDER +
      //     data.dataValues.user_id +
      //     "/" +
      //     constant.GCP_ITEM_FOLDER +
      //     data.dataValues.item_id +
      //     "/" +
      //     data.dataValues.media;
      //   let imgs = image_size.formatSize(data.dataValues.size * 1000);
      //   let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;

      //   let size = b.toString() + " " + imgs.split(" ")[1];
      //   item.dataValues.size = data.dataValues.size.toString();
      //   data.dataValues.size = size;
      // });

      if (media_details.length) {
        media_details = media_details[0];
        // media_details.dataValues.media = await gcpUtils.getPrivateUrl(
        //   constant.GCP_BUCKET_NAME,
        //   constant.GCP_BUCKET_FOLDER +
        //   constant.GCP_USER_FOLDER +
        //   media_details.dataValues.user_id +
        //   "/" +
        //   constant.GCP_ITEM_FOLDER +
        //   media_details.dataValues.item_id +
        //   "/" +
        //   media_details.dataValues.media, /*expires: '2023-12-02' */
        // );
        media_details.dataValues.mediaName = media_details.dataValues.media;
        let imgs = image_size.formatSize(media_details.dataValues.size * 1000);
        let b = Math.round(Number(imgs.split(' ')[0]) * 100) / 100;

        let size = b.toString() + ' ' + imgs.split(' ')[1];
        item.dataValues.size = media_details.dataValues.size.toString();
        media_details.dataValues.size = size;
      }

      return item;
    }
    return 0;
  },

  /**
   * delete OCR label
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async deleteOCRLabel(req, res) {
    let { id, name } = req.body;

    let OCRDetails = await OCRLabels.findOne({
      where: {
        id,
      },
      attributes: ['ocr_label'],
    });

    if (OCRDetails) {
      if (OCRDetails.ocr_label) {
        let AllOCRLabels = OCRDetails.ocr_label;

        AllOCRLabels.map((item) => {
          if (item.DetectedText == name) {
            item.isDeleted = 1;
          }
        });

        let ocrLabelString = '';
        if (AllOCRLabels) {
          AllOCRLabels.forEach((each) => {
            if (each.Type == 'LINE') {
              if (!each.isDeleted || (each.isDeleted && each.isDeleted == 0)) {
                ocrLabelString += ` ${each.DetectedText}`;
              }
            }
          });
        }

        await OCRLabels.update(
          {
            ocr_label: AllOCRLabels,
            ocr_label_string: ocrLabelString,
          },
          { where: { id } }
        );
      }

      return await OCRLabels.findOne({
        where: {
          id,
        },
        attributes: ['id', 'ocr_label'],
      });
    }
    return 0;
  },
};
