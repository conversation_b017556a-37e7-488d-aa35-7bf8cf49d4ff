/**
 * Unit Tests for Recycle Bin Business Logic
 * Based on QA E2E Test Scenarios for KyuleBag Recycle Bin Flow
 * 
 * Tests cover:
 * - Trash/Recycle bin item listing with pagination and sorting
 * - Item restoration from trash (single and bulk operations)
 * - Permanent deletion from trash (single and bulk operations)
 * - Select all functionality with exclude options
 * - Cloud storage integration for file deletion
 * - Activity logging for audit trails
 * - Related data management (images, tags, notes, sharing)
 * - Error handling and edge cases
 * - Data formatting and sanitization
 */

const { Op } = require('sequelize');

// Mock Sequelize with its literal function
jest.mock('sequelize', () => {
  const actualSequelize = jest.requireActual('sequelize');
  return {
    ...actualSequelize,
    literal: jest.fn((value) => value),
    Op: actualSequelize.Op,
  };
});

// Mock all external dependencies
jest.mock('../../../database/models', () => ({
  tbl_items: {
    findAndCountAll: jest.fn(),
    findOne: jest.fn(),
    findAll: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    item_id: 'item_id', // Add this property for the distinct query
  },
  tbl_item_notes: {
    update: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_item_tags: {
    update: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_tag_names: {
    // Associated model for tags
  },
  tbl_media: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_sharing_details: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
  },
  tbl_activity_logs: {
    create: jest.fn(),
  },
  tbl_item_ocr_labels: {
    // Associated model for OCR labels
  },
}));

jest.mock('../../../config/constant', () => ({
  LIMIT: 10,
  GCP_BUCKET_FOLDER: 'test-bucket-folder/',
  GCP_USER_FOLDER: 'users/',
  GCP_ITEM_FOLDER: 'items/',
  GCP_URL: 'https://storage.googleapis.com/',
  GCP_BUCKET_NAME: 'test-bucket',
}));

jest.mock('../../../helper/general.helper', () => ({
  formatSize: jest.fn(),
}));

jest.mock('../../../middleware/multer_gcp_delete', () => jest.fn());

// Import the service after mocking
const trashService = require('../trash.service');
const Items = require('../../../database/models').tbl_items;
const Notes = require('../../../database/models').tbl_item_notes;
const Tags = require('../../../database/models').tbl_item_tags;
const Images = require('../../../database/models').tbl_media;
const SharingDetails = require('../../../database/models').tbl_sharing_details;
const ActivityLogs = require('../../../database/models').tbl_activity_logs;
const imageSize = require('../../../helper/general.helper');
const gcpDelete = require('../../../middleware/multer_gcp_delete');

describe('Recycle Bin Business Logic - Unit Tests', () => {
  let mockReq, mockRes;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock request object
    mockReq = {
      user_id: 'user123',
      body: {},
    };

    // Mock response object
    mockRes = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };

    // Default mock implementations
    imageSize.formatSize.mockReturnValue('5.0 MB');
    gcpDelete.mockResolvedValue(true);
  });

  describe('📋 Trash Item Listing', () => {
    describe('✅ Successful Listing', () => {
      it('should successfully list trash items with default pagination', async () => {
        // Arrange
        mockReq.body = {};

        const mockTrashItems = {
          count: 2,
          rows: [
            {
              item_id: 'item1',
              user_id: 'user123',
              title: 'Deleted Item 1',
              description: 'First deleted item',
              item_type: 'document',
              is_deleted: 1,
              createdAt: new Date('2023-01-01T10:00:00Z'),
              uploadedAt: new Date('2023-01-01T10:00:00Z'),
              items_tag_lists: [
                {
                  dataValues: {
                    is_deleted: true,
                    items_tag_names: {
                      dataValues: { tag_name: 'important' },
                    },
                    createdAt: new Date('2023-01-01T10:00:00Z'),
                    updatedAt: new Date('2023-01-01T10:00:00Z'),
                  },
                  items_tag_names: {
                    dataValues: { tag_name: 'important' },
                  },
                  createdAt: new Date('2023-01-01T10:00:00Z'),
                },
              ],
              dataValues: {
                item_id: 'item1',
                createdAt: new Date('2023-01-01T10:00:00Z'),
                uploadedAt: new Date('2023-01-01T10:00:00Z'),
                image_details: [
                  {
                    dataValues: {
                      media: 'test-image.jpg',
                      size: 1024,
                      user_id: 'user123',
                      item_id: 'item1',
                    },
                  },
                ],
                item_sharing_details: {
                  dataValues: {
                    createdAt: new Date('2023-01-01T10:00:00Z'),
                    updatedAt: new Date('2023-01-01T10:00:00Z'),
                  },
                },
                items_note_lists: [
                  {
                    dataValues: {
                      id: 'note1',
                      note_description: 'Test note',
                      createdAt: new Date('2023-01-01T10:00:00Z'),
                      updatedAt: new Date('2023-01-01T10:00:00Z'),
                    },
                    createdAt: new Date('2023-01-01T10:00:00Z'),
                  },
                ],
                items_tag_lists: [
                  {
                    dataValues: {
                      is_deleted: true,
                      items_tag_names: {
                        dataValues: { tag_name: 'important' },
                      },
                      createdAt: new Date('2023-01-01T10:00:00Z'),
                      updatedAt: new Date('2023-01-01T10:00:00Z'),
                    },
                    items_tag_names: {
                      dataValues: { tag_name: 'important' },
                    },
                    createdAt: new Date('2023-01-01T10:00:00Z'),
                  },
                ],
              },
            },
          ],
        };

        Items.findAndCountAll.mockResolvedValue(mockTrashItems);

        // Act
        const result = await trashService.list(mockReq, mockRes);

        // Assert
        expect(Items.findAndCountAll).toHaveBeenCalledWith({
          where: {
            user_id: 'user123',
            is_deleted: 1,
          },
          include: expect.arrayContaining([
            expect.objectContaining({
              model: Images,
              as: 'image_details',
            }),
            expect.objectContaining({
              model: SharingDetails,
              as: 'item_sharing_details',
            }),
            expect.objectContaining({
              model: Notes,
              as: 'items_note_lists',
            }),
            expect.objectContaining({
              model: Tags,
              as: 'items_tag_lists',
            }),
          ]),
          attributes: expect.arrayContaining([
            'item_id',
            'user_id',
            'title',
            'description',
            'item_type',
            'is_bookmarked',
            'is_private',
            'delete_type',
            'is_deleted',
            'createdAt',
            'uploadedAt',
          ]),
          order: [['item_id', 'DESC']],
          offset: 0,
          limit: 10,
          distinct: `${Items.item_id}`,
        });
        expect(result).toEqual(mockTrashItems);
        expect(imageSize.formatSize).toHaveBeenCalled();
      });

      it('should list trash items with custom pagination and sorting', async () => {
        // Arrange
        mockReq.body = {
          page: 2,
          sort_by: 'title',
          order: 'ASC',
        };

        const mockTrashItems = {
          count: 5,
          rows: [],
        };

        Items.findAndCountAll.mockResolvedValue(mockTrashItems);

        // Act
        const result = await trashService.list(mockReq, mockRes);

        // Assert
        expect(Items.findAndCountAll).toHaveBeenCalledWith(
          expect.objectContaining({
            order: [['title', 'ASC']],
            offset: 10, // (page 2 - 1) * limit 10
            limit: 10,
          })
        );
      });

      it('should return 0 when no trash items found', async () => {
        // Arrange
        Items.findAndCountAll.mockResolvedValue({ count: 0, rows: [] });

        // Act
        const result = await trashService.list(mockReq, mockRes);

        // Assert
        expect(result).toBe(0);
      });

      it('should format dates and file sizes correctly', async () => {
        // Arrange
        const mockTrashItems = {
          count: 1,
          rows: [
            {
              dataValues: {
                createdAt: new Date('2023-01-01T10:30:45Z'),
                uploadedAt: new Date('2023-01-01T11:30:45Z'),
                image_details: [
                  {
                    dataValues: {
                      media: 'test.jpg',
                      size: 2048,
                      user_id: 'user123',
                      item_id: 'item1',
                    },
                  },
                ],
                item_sharing_details: {
                  dataValues: {
                    createdAt: new Date('2023-01-01T12:30:45Z'),
                    updatedAt: new Date('2023-01-01T13:30:45Z'),
                  },
                },
                items_note_lists: [],
                items_tag_lists: [],
              },
              items_tag_lists: [],
            },
          ],
        };

        Items.findAndCountAll.mockResolvedValue(mockTrashItems);
        imageSize.formatSize.mockReturnValue('2.0 MB');

        // Act
        const result = await trashService.list(mockReq, mockRes);

        // Assert
        expect(imageSize.formatSize).toHaveBeenCalledWith(2048000); // size * 1000
        expect(result.rows[0].dataValues.image_details[0].dataValues.size).toContain('MB');
      });
    });

    describe('❌ Listing Error Scenarios', () => {
      it('should handle database errors during listing', async () => {
        // Arrange
        Items.findAndCountAll.mockRejectedValue(new Error('Database error'));

        // Act & Assert
        await expect(trashService.list(mockReq, mockRes)).rejects.toThrow('Database error');
      });
    });
  });

  describe('🔄 Item Restoration', () => {
    describe('✅ Successful Restoration', () => {
      it('should successfully restore a single item from trash', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItem = {
          item_id: 'item1',
          user_id: 'user123',
          is_deleted: 1,
          dataValues: {
            delete_type: 'cloud',
          },
        };

        const mockImage = {
          item_id: 'item1',
          dataValues: {
            media: 'test-image.jpg',
          },
        };

        const mockSharingDetails = {
          item_id: 'item1',
        };

        Items.findOne.mockResolvedValue(mockItem);
        Images.findOne.mockResolvedValue(mockImage);
        SharingDetails.findOne.mockResolvedValue(mockSharingDetails);
        SharingDetails.update.mockResolvedValue([1]);
        Images.update.mockResolvedValue([1]);
        Tags.update.mockResolvedValue([1]);
        Notes.update.mockResolvedValue([1]);
        Items.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.restore(mockReq, mockRes);

        // Assert
        expect(Items.findOne).toHaveBeenCalledWith({
          where: {
            item_id: ['item1'],
            is_deleted: 1,
            user_id: 'user123',
          },
        });
        expect(Items.update).toHaveBeenCalledWith(
          {
            is_deleted: 0,
            delete_type: 'NORMAL',
          },
          {
            where: {
              item_id: ['item1'],
            },
          }
        );
        expect(ActivityLogs.create).toHaveBeenCalledWith({
          user_id: 'user123',
          title: 'Restore Item',
          description: '1 Items restored from Recycle Bin.',
        });
        expect(result).toBe(1);
      });

      it('should restore multiple items using select all feature', async () => {
        // Arrange
        mockReq.body = {
          is_select_all: true,
          unchecked_ids: '["item2"]',
        };

        const mockFindAllItems = {
          rows: [
            { dataValues: { item_id: 'item1' } },
            { dataValues: { item_id: 'item3' } },
          ],
        };

        const mockItem = {
          dataValues: { delete_type: 'cloud' },
        };

        Items.findAndCountAll.mockResolvedValue(mockFindAllItems);
        Items.findOne.mockResolvedValue(mockItem);
        Images.findOne.mockResolvedValue(null);
        SharingDetails.findOne.mockResolvedValue(null);
        Images.update.mockResolvedValue([1]);
        Tags.update.mockResolvedValue([1]);
        Notes.update.mockResolvedValue([1]);
        Items.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.restore(mockReq, mockRes);

        // Assert
        expect(Items.findAndCountAll).toHaveBeenCalledWith({
          where: [
            {
              user_id: 'user123',
              is_deleted: 1,
            },
            {
              item_id: {
                [Op.notIn]: ['item2'],
              },
            },
          ],
          attributes: ['item_id'],
        });
        expect(ActivityLogs.create).toHaveBeenCalledWith({
          user_id: 'user123',
          title: 'Restore Item',
          description: '2 Items restored from Recycle Bin.',
        });
        expect(result).toBe(1);
      });

      it('should restore all items when select all is true without exclusions', async () => {
        // Arrange
        mockReq.body = {
          is_select_all: true,
        };

        const mockFindAllItems = {
          rows: [
            { dataValues: { item_id: 'item1' } },
            { dataValues: { item_id: 'item2' } },
            { dataValues: { item_id: 'item3' } },
          ],
        };

        const mockItem = {
          dataValues: { delete_type: 'cloud' },
        };

        Items.findAndCountAll.mockResolvedValue(mockFindAllItems);
        Items.findOne.mockResolvedValue(mockItem);
        Images.findOne.mockResolvedValue(null);
        SharingDetails.findOne.mockResolvedValue(null);
        Images.update.mockResolvedValue([1]);
        Tags.update.mockResolvedValue([1]);
        Notes.update.mockResolvedValue([1]);
        Items.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.restore(mockReq, mockRes);

        // Assert
        expect(Items.findAndCountAll).toHaveBeenCalledWith({
          where: [
            {
              user_id: 'user123',
              is_deleted: 1,
            },
          ],
          attributes: ['item_id'],
        });
        expect(result).toBe(1);
      });

      it('should restore item with sharing details and update all related tables', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItem = {
          dataValues: { delete_type: 'cloud' },
        };
        const mockImage = {
          dataValues: { media: 'test.jpg' },
        };
        const mockSharingDetails = { item_id: 'item1' };

        Items.findOne.mockResolvedValue(mockItem);
        Images.findOne.mockResolvedValue(mockImage);
        SharingDetails.findOne.mockResolvedValue(mockSharingDetails);
        SharingDetails.update.mockResolvedValue([1]);
        Images.update.mockResolvedValue([1]);
        Tags.update.mockResolvedValue([1]);
        Notes.update.mockResolvedValue([1]);
        Items.update.mockResolvedValue([1]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.restore(mockReq, mockRes);

        // Assert
        expect(SharingDetails.update).toHaveBeenCalledWith(
          { is_deleted: 0 },
          { where: { item_id: ['item1'] } }
        );
        expect(Images.update).toHaveBeenCalledWith(
          { is_deleted: 0 },
          { where: { item_id: ['item1'] } }
        );
        expect(Tags.update).toHaveBeenCalledWith(
          { is_deleted: 0 },
          { where: { item_id: ['item1'] } }
        );
        expect(Notes.update).toHaveBeenCalledWith(
          { is_deleted: 0 },
          { where: { item_id: ['item1'] } }
        );
        expect(result).toBe(1);
      });
    });

    describe('❌ Restoration Failures', () => {
      it('should return 0 when item not found for restoration', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["nonexistent"]',
          is_select_all: false,
        };

        Items.findOne.mockResolvedValue(null);

        // Act
        const result = await trashService.restore(mockReq, mockRes);

        // Assert
        expect(result).toBe(0);
        expect(ActivityLogs.create).not.toHaveBeenCalled();
      });

      it('should handle database errors during restoration', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        Items.findOne.mockRejectedValue(new Error('Database error'));

        // Act & Assert
        await expect(trashService.restore(mockReq, mockRes)).rejects.toThrow('Database error');
      });

      it('should handle activity log creation failures', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItem = {
          dataValues: { delete_type: 'cloud' },
        };

        Items.findOne.mockResolvedValue(mockItem);
        Images.findOne.mockResolvedValue(null);
        SharingDetails.findOne.mockResolvedValue(null);
        Images.update.mockResolvedValue([1]);
        Tags.update.mockResolvedValue([1]);
        Notes.update.mockResolvedValue([1]);
        Items.update.mockResolvedValue([1]);
        ActivityLogs.create.mockRejectedValue(new Error('Activity log failed'));

        // Act & Assert
        await expect(trashService.restore(mockReq, mockRes)).rejects.toThrow('Activity log failed');
      });
    });
  });

  describe('🗑️ Permanent Deletion', () => {
    describe('✅ Successful Deletion', () => {
      it('should permanently delete a single item with GCP file removal', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItems = [
          {
            item_id: 'item1',
            is_upload: true,
          },
        ];

        const mockImages = [
          {
            item_id: 'item1',
            media: 'test-image.jpg',
          },
        ];

        const mockSharingDetails = [
          {
            item_id: 'item1',
          },
        ];

        Items.findAll.mockResolvedValue(mockItems);
        Images.findAll.mockResolvedValue(mockImages);
        SharingDetails.findAll.mockResolvedValue(mockSharingDetails);
        Images.destroy.mockResolvedValue(1);
        SharingDetails.destroy.mockResolvedValue(1);
        Tags.destroy.mockResolvedValue(1);
        Notes.destroy.mockResolvedValue(1);
        Items.destroy.mockResolvedValue(1);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.delete(mockReq, mockRes);

        // Assert
        expect(gcpDelete).toHaveBeenCalledWith(
          'test-bucket-folder/users/user123/items/item1/test-image.jpg'
        );
        expect(Images.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(SharingDetails.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(Tags.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(Notes.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(Items.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(ActivityLogs.create).toHaveBeenCalledWith({
          user_id: 'user123',
          title: 'Delete Item',
          description: 'Item permanently deleted.',
        });
        expect(result).toBe(1);
      });

      it('should delete multiple items using select all feature', async () => {
        // Arrange
        mockReq.body = {
          is_select_all: true,
          unchecked_ids: '["item3"]',
        };

        const mockFindAllItems = {
          rows: [
            { dataValues: { item_id: 'item1' } },
            { dataValues: { item_id: 'item2' } },
          ],
        };

        const mockItems = [
          { item_id: 'item1', is_upload: false },
          { item_id: 'item2', is_upload: false },
        ];

        Items.findAndCountAll.mockResolvedValue(mockFindAllItems);
        Items.findAll.mockResolvedValue(mockItems);
        Images.findAll.mockResolvedValue([]);
        SharingDetails.findAll.mockResolvedValue([]);
        Tags.destroy.mockResolvedValue(1);
        Notes.destroy.mockResolvedValue(1);
        Items.destroy.mockResolvedValue(1);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.delete(mockReq, mockRes);

        // Assert
        expect(Items.findAndCountAll).toHaveBeenCalledWith({
          where: [
            {
              user_id: 'user123',
              is_deleted: 1,
            },
            {
              item_id: {
                [Op.notIn]: ['item3'],
              },
            },
          ],
          attributes: ['item_id'],
        });
        expect(result).toBe(1);
      });

      it('should delete item without upload flag and skip GCP deletion', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItems = [
          {
            item_id: 'item1',
            is_upload: false, // No file to delete from GCP
          },
        ];

        const mockImages = [
          {
            item_id: 'item1',
            media: 'test-image.jpg',
          },
        ];

        Items.findAll.mockResolvedValue(mockItems);
        Images.findAll.mockResolvedValue(mockImages);
        SharingDetails.findAll.mockResolvedValue([]);
        Images.destroy.mockResolvedValue(1);
        Tags.destroy.mockResolvedValue(1);
        Notes.destroy.mockResolvedValue(1);
        Items.destroy.mockResolvedValue(1);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.delete(mockReq, mockRes);

        // Assert
        expect(gcpDelete).not.toHaveBeenCalled();
        expect(Images.destroy).toHaveBeenCalled();
        expect(result).toBe(1);
      });

      it('should handle item deletion without associated images or sharing details', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItems = [
          {
            item_id: 'item1',
            is_upload: false,
          },
        ];

        Items.findAll.mockResolvedValue(mockItems);
        Images.findAll.mockResolvedValue([]); // No images
        SharingDetails.findAll.mockResolvedValue([]); // No sharing details
        Tags.destroy.mockResolvedValue(1);
        Notes.destroy.mockResolvedValue(1);
        Items.destroy.mockResolvedValue(1);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.delete(mockReq, mockRes);

        // Assert
        expect(Images.destroy).not.toHaveBeenCalled();
        expect(SharingDetails.destroy).not.toHaveBeenCalled();
        expect(Tags.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(Notes.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(Items.destroy).toHaveBeenCalledWith({
          where: { item_id: 'item1' },
        });
        expect(result).toBe(1);
      });

      it('should return 0 when no items provided for deletion', async () => {
        // Arrange
        mockReq.body = {
          item_id: '[]',
          is_select_all: false,
        };

        Items.findAll.mockResolvedValue([]);
        Images.findAll.mockResolvedValue([]);
        SharingDetails.findAll.mockResolvedValue([]);
        ActivityLogs.create.mockResolvedValue({});

        // Act
        const result = await trashService.delete(mockReq, mockRes);

        // Assert
        expect(result).toBe(0);
      });
    });

    describe('❌ Deletion Failures', () => {
      it('should handle database errors during deletion', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        Items.findAll.mockRejectedValue(new Error('Database error'));

        // Act & Assert
        await expect(trashService.delete(mockReq, mockRes)).rejects.toThrow('Database error');
      });

      it('should handle GCP deletion failures', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItems = [
          {
            item_id: 'item1',
            is_upload: true,
          },
        ];

        const mockImages = [
          {
            item_id: 'item1',
            media: 'test-image.jpg',
          },
        ];

        Items.findAll.mockResolvedValue(mockItems);
        Images.findAll.mockResolvedValue(mockImages);
        SharingDetails.findAll.mockResolvedValue([]);
        gcpDelete.mockRejectedValue(new Error('GCP deletion failed'));

        // Act & Assert
        await expect(trashService.delete(mockReq, mockRes)).rejects.toThrow('GCP deletion failed');
      });

      it('should handle activity log creation failures during deletion', async () => {
        // Arrange
        mockReq.body = {
          item_id: '["item1"]',
          is_select_all: false,
        };

        const mockItems = [
          {
            item_id: 'item1',
            is_upload: false,
          },
        ];

        Items.findAll.mockResolvedValue(mockItems);
        Images.findAll.mockResolvedValue([]);
        SharingDetails.findAll.mockResolvedValue([]);
        Tags.destroy.mockResolvedValue(1);
        Notes.destroy.mockResolvedValue(1);
        Items.destroy.mockResolvedValue(1);
        ActivityLogs.create.mockRejectedValue(new Error('Activity log failed'));

        // Act & Assert
        await expect(trashService.delete(mockReq, mockRes)).rejects.toThrow('Activity log failed');
      });
    });
  });

  describe('🚨 Edge Cases and Error Handling', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle invalid JSON in item_id parameter', async () => {
      // Arrange
      mockReq.body = {
        item_id: 'invalid-json',
        is_select_all: false,
      };

      // Act & Assert
      await expect(trashService.restore(mockReq, mockRes)).rejects.toThrow();
    });

    it('should handle invalid JSON in unchecked_ids parameter', async () => {
      // Arrange
      mockReq.body = {
        is_select_all: true,
        unchecked_ids: 'invalid-json',
      };

      // Act & Assert
      await expect(trashService.restore(mockReq, mockRes)).rejects.toThrow();
    });

    it('should handle empty unchecked_ids gracefully', async () => {
      // Arrange
      mockReq.body = {
        is_select_all: true,
        unchecked_ids: '',
      };

      const mockFindAllItems = {
        rows: [{ dataValues: { item_id: 'item1' } }],
      };

      const mockItem = {
        dataValues: { delete_type: 'cloud' },
      };

      Items.findAndCountAll.mockResolvedValue(mockFindAllItems);
      Items.findOne.mockResolvedValue(mockItem);
      Images.findOne.mockResolvedValue(null);
      SharingDetails.findOne.mockResolvedValue(null);
      Images.update.mockResolvedValue([1]);
      Tags.update.mockResolvedValue([1]);
      Notes.update.mockResolvedValue([1]);
      Items.update.mockResolvedValue([1]);
      ActivityLogs.create.mockResolvedValue({});

      // Act
      const result = await trashService.restore(mockReq, mockRes);

      // Assert
      expect(result).toBe(1);
    });

    it('should handle missing request body parameters', async () => {
      // Arrange
      mockReq.body = {}; // Empty body

      const mockItems = {
        count: 1,
        rows: [
          {
            dataValues: {
              createdAt: new Date('2023-01-01T10:00:00Z'),
              uploadedAt: new Date('2023-01-01T10:00:00Z'),
              image_details: [],
              item_sharing_details: null,
              items_note_lists: [],
              items_tag_lists: [],
            },
            items_tag_lists: [],
          },
        ],
      };

      Items.findAndCountAll.mockResolvedValue(mockItems);

      // Act
      const result = await trashService.list(mockReq, mockRes);

      // Assert - Should handle gracefully with defaults
      expect(Items.findAndCountAll).toHaveBeenCalled();
      expect(result).toEqual(mockItems);
    });

    it('should handle concurrent deletion operations', async () => {
      // Arrange
      mockReq.body = {
        item_id: '["item1", "item2"]',
        is_select_all: false,
      };

      const mockItems = [
        { item_id: 'item1', is_upload: true },
        { item_id: 'item2', is_upload: true },
      ];

      const mockImages = [
        { item_id: 'item1', media: 'image1.jpg' },
        { item_id: 'item2', media: 'image2.jpg' },
      ];

      Items.findAll.mockResolvedValue(mockItems);
      Images.findAll.mockResolvedValue(mockImages);
      SharingDetails.findAll.mockResolvedValue([]);
      gcpDelete.mockResolvedValue(true);
      Images.destroy.mockResolvedValue(1);
      Tags.destroy.mockResolvedValue(1);
      Notes.destroy.mockResolvedValue(1);
      Items.destroy.mockResolvedValue(1);
      ActivityLogs.create.mockResolvedValue({});

      // Act
      const result = await trashService.delete(mockReq, mockRes);

      // Assert
      expect(gcpDelete).toHaveBeenCalledTimes(2);
      expect(Images.destroy).toHaveBeenCalledTimes(2);
      expect(result).toBe(1);
    });
  });

  describe('🔄 Data Validation and Sanitization', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle undefined pagination parameters with defaults', async () => {
      // Arrange
      mockReq.body = {
        page: undefined,
        sort_by: undefined,
        order: undefined,
      };

      Items.findAndCountAll.mockResolvedValue({ count: 0, rows: [] });

      // Act
      const result = await trashService.list(mockReq, mockRes);

      // Assert
      expect(Items.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          order: [['item_id', 'DESC']], // Default sorting
          offset: 0, // Default page 1
          limit: 10, // Default limit
        })
      );
    });

    it('should handle empty string pagination parameters', async () => {
      // Arrange
      mockReq.body = {
        page: '',
        sort_by: '',
        order: '',
      };

      Items.findAndCountAll.mockResolvedValue({ count: 0, rows: [] });

      // Act
      const result = await trashService.list(mockReq, mockRes);

      // Assert
      expect(Items.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          order: [['item_id', 'DESC']], // Default when empty
          offset: -10, // Empty string page causes calculation issue
        })
      );
    });

    it('should handle large item arrays efficiently', async () => {
      // Arrange
      const largeItemArray = Array.from({ length: 100 }, (_, i) => `item${i}`);
      mockReq.body = {
        item_id: JSON.stringify(largeItemArray),
        is_select_all: false,
      };

      const mockItems = largeItemArray.map(id => ({
        item_id: id,
        is_upload: false,
      }));

      Items.findAll.mockResolvedValue(mockItems);
      Images.findAll.mockResolvedValue([]);
      SharingDetails.findAll.mockResolvedValue([]);
      Tags.destroy.mockResolvedValue(1);
      Notes.destroy.mockResolvedValue(1);
      Items.destroy.mockResolvedValue(1);
      ActivityLogs.create.mockResolvedValue({});

      // Act
      const result = await trashService.delete(mockReq, mockRes);

      // Assert
      expect(result).toBe(1);
      expect(Tags.destroy).toHaveBeenCalledTimes(100);
      expect(Notes.destroy).toHaveBeenCalledTimes(100);
      expect(Items.destroy).toHaveBeenCalledTimes(100);
    });

    it('should sanitize file paths for GCP deletion', async () => {
      // Arrange
      mockReq.body = {
        item_id: '["item1"]',
        is_select_all: false,
      };

      const mockItems = [
        {
          item_id: 'item1',
          is_upload: true,
        },
      ];

      const mockImages = [
        {
          item_id: 'item1',
          media: '../../../malicious-path.jpg', // Potential path traversal
        },
      ];

      Items.findAll.mockResolvedValue(mockItems);
      Images.findAll.mockResolvedValue(mockImages);
      SharingDetails.findAll.mockResolvedValue([]);
      Images.destroy.mockResolvedValue(1);
      Tags.destroy.mockResolvedValue(1);
      Notes.destroy.mockResolvedValue(1);
      Items.destroy.mockResolvedValue(1);
      ActivityLogs.create.mockResolvedValue({});

      // Act
      const result = await trashService.delete(mockReq, mockRes);

      // Assert
      expect(gcpDelete).toHaveBeenCalledWith(
        'test-bucket-folder/users/user123/items/item1/../../../malicious-path.jpg'
      );
      expect(result).toBe(1);
    });
  });
}); 