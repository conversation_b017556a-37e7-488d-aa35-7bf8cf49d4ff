'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    return Promise.all([
      queryInterface.changeColumn("tbl_users", "AI_set", {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      }),
      queryInterface.changeColumn("tbl_users", "OCR_set", {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      }),
      queryInterface.changeColumn("tbl_users", "AI_confidence_level", {
        type: Sequelize.INTEGER,
        defaultValue: 75,
      }),
      queryInterface.changeColumn("tbl_users", "hide_AI_label", {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      }),
      queryInterface.changeColumn("tbl_users", "hide_OCR_label", {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      }),
    ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    return Promise.all([
      queryInterface.changeColumn("tbl_users", "AI_set", {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      }),
      queryInterface.changeColumn("tbl_users", "OCR_set", {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      }),
      queryInterface.changeColumn("tbl_users", "AI_confidence_level", {
        type: Sequelize.INTEGER,
        defaultValue: 70,
      }),
      queryInterface.changeColumn("tbl_users", "hide_AI_label", {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      }),
      queryInterface.changeColumn("tbl_users", "hide_OCR_label", {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      }),
    ])
  }
};
