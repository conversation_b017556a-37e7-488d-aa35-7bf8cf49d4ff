'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_subscriptions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    // static associate(models) {
    //   // define association here
    //   tbl_subscriptions.hasMany(models.tbl_user_subscriptions, {
    //     targetKey: "subscription_id",
    //     foreignKey: "subscription_id",
    //     // as: "subscription_plan_details",
    //   });
    // }
  }
  tbl_subscriptions.init({
    ai_subscription_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    number_of_api: DataTypes.FLOAT,
    ai_subscription_product_id: DataTypes.TEXT,
    price: DataTypes.STRING,
    duration: {
      type: DataTypes.ENUM("monthly", "yearly"),
    },
    // status: {
    //   type: DataTypes.ENUM("inactive", "active"),
    //   defaultValue: "active",
    // },
    is_deleted: {
      allowNull: false, 
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_ai_subscriptions',
  });
  return tbl_subscriptions;
};