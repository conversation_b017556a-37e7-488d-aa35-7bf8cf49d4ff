const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const admin_service = require("../../services/cms/admin.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
    // list of admin
  async listAdmin(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let admin_list = await admin_service.listAdmin(req, res);

      if (admin_list) {
        res.status(status.SUCCESSSTATUS).send({
          data: admin_list,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("Kyulebag Admin: listAdmin -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // list of sub admin
  async listSubAdmin(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let sub_admin_list = await admin_service.listSubAdmin(req, res);

      if (sub_admin_list) {
        res.status(status.SUCCESSSTATUS).send({
          data: sub_admin_list,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("Kyulebag Admin: listSubAdmin -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // admin retrive
  async retriveAdmin(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_admin_res = await admin_service.retriveAdmin(req, res);
      if (!list_admin_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_res,
          message: message.ADMINNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: list_admin_res,
          message: message.ADMINLIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("retrieveAdmin -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // add admin
  async addAdmin(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let add_admin_res = await admin_service.addAdmin(req, res);
      if (!add_admin_res) {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          data: [],
          // data: add_admin_res,
          message: message.ADMINEXIST,
          status: status.ERROR,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          // data: [],
          data: add_admin_res,
          message: message.ADMINADDED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("addAdmin -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // admin edit
  async editAdmin(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let admin_res = await admin_service.editAdmin(req, res);
      if (admin_res === 0) {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          data: [],
          message: message.ADMINEXIST,
          status: status.ERROR,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          // data: [],
          data: admin_res,
          message: message.ADMINUPDATED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("editAdmin -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // delete admin
  async deleteAdmin(req, res) {
    try {
      let delete_admin = await admin_service.deleteAdmin(req, res);
      if (delete_admin) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ADMINDELETED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
}