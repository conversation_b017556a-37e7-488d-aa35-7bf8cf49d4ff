'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_static_contents extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  };
  tbl_static_contents.init({
    static_contents_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    title: DataTypes.TEXT,
    content: DataTypes.TEXT,
    type: DataTypes.ENUM('web', 'app'),
    is_active: DataTypes.ENUM('0', '1')
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_static_contents',
  });
  return tbl_static_contents;
};