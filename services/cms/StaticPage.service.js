require("dotenv").config();
const StaticContent = require("../../database/models").tbl_static_contents;
const constant = require("../../config/constant");
const Sequelize = require("sequelize");
const Op = Sequelize.Op;

module.exports = {
  async addStaticPage(req) {
    
    let add_static_content = await StaticContent.create(
      {
        title: req.body.title,
        content: req.body.content,
        type: req.body.type,
        // is_active: req.body.is_active
      }
    );
    return add_static_content;
  },
  async editStaticPage(req) {
    let static_page_edit_obj = {
      title: req.body.title && req.body.title,
      content: req.body.content && req.body.content,
      type: req.body.type && req.body.type,
    };
    // const isStaticPage = await StaticContent.findOne({where : {static_contents_id : req.params.id}})
    // if(isStaticPage !== null) {
      let edit_static_content = await StaticContent.update(
        static_page_edit_obj,
        { where: { static_contents_id: req.params.id } }
      );
      var edit_content = await StaticContent.findOne({
        where:{
          static_contents_id: req.params.id
        }
      })
      return edit_content;
    // }
    // else {
    //   return 0
    // }
  },
  async listStaticPage(req, res) {
    let { sort_by, search, order} =req.body;
    // const bodyParams = req.body;
    // let order = bodyParams.order ? bodyParams.order : "ASC";
    // let page = bodyParams.page ? bodyParams.page : 1;
    // let sortBy = bodyParams.sortBy ? bodyParams.sortBy : "createdAt";
    // let search = bodyParams.search ? bodyParams.search : "";
    const limit = constant.LIMIT;
    const offset = (req.body.page - 1) * limit;
    var find_static_content = await StaticContent.findAndCountAll({
      where: { title: { [Op.like]: `%${search}%` }, is_active: true },
      offset: offset,
      limit: limit,
      order: [[Sequelize.literal(`${sort_by}`),`${order}`]],      
    });
    return find_static_content;
  },
  async changeStatusStaticPage(req, res) {
    const isStaticPage = await StaticContent.findOne({where : {static_contents_id : req.body.id}})
    if(isStaticPage !== null) {
      let add_static_content = await StaticContent.update(
        { is_active: req.body.is_active },
        { where: { static_contents_id: req.body.id } }
      );
      return add_static_content;
    }
    else {
      return 0;
    }
    
  },

  async retriveStaticPage(req, res) {
    let find_static_content = await StaticContent.findOne({
      where: { static_contents_id: req.params.id },
    });
    return find_static_content;
  },
}