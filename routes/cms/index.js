/** NPM-modules */
var router = require("express").Router();

/* require for Authentication */
const commonRouter = require("../cms/common");
const adminRouter = require("../cms/admin");
const roleRouter = require("../cms/adminRole");
const userRouter = require("../../routes/cms/user");
const dashboardRouter = require("./dashboard");
const staticPageRouter = require("./StaticPage");
const emailNotification = require("./notification");
const announcementRouter = require("./announcement");
const commentRouter = require("./comments")
const subscriptionRouter = require("./subscription");
const transactionRouter = require("./paymentTransaction");
const aiSubscriptionRouter = require("./AISubscription");

//  /* route of cms API*/
router.use("/admin", commonRouter);
router.use("/admin", adminRouter);
router.use("/admin-role", roleRouter);
router.use("/user", userRouter);
router.use("/dashboard", dashboardRouter);
router.use("/static-content", staticPageRouter);
router.use("/email-notification", emailNotification);
router.use("/announcements", announcementRouter);
router.use("/comments", commentRouter);
router.use("/subscription", subscriptionRouter);
router.use("/payment-transaction", transactionRouter);
router.use("/ai-subscription", aiSubscriptionRouter);

module.exports = router;
