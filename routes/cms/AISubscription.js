var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

/* require for Controller */
const ai_subscription_controller = require("../../controllers/cms/AIsubscription.controller");

/*require for validators */
const validators = require("../../assets/validators");

router.post(
  "/add",
  authentication,
  ai_subscription_controller.addAIsubscription
);
router.post(
  "/list",
  authentication,
  ai_subscription_controller.listAIsubscription
);
router.put(
  "/edit/:id",
  authentication,
  /**validators.cms.staticContent.updateStaticPage,*/ ai_subscription_controller.editAIsubscription
);
router.put("/delete/:id", authentication, ai_subscription_controller.deleteAIsubscription);
router.post(
  "/retrive/:id",
  authentication,
  ai_subscription_controller.retriveAIsubscription
);

module.exports = router;
