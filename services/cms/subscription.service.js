require("dotenv").config();
const StoragePlan = require("../../database/models").tbl_subscriptions;
const NewStoragePlan = require("../../database/models").tbl_new_subscriptions;
const AIStoragePlan = require("../../database/models").tbl_new_ai_subscriptions;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const message = require("../../config/cms.message").cmsMessage;
const constant = require("../../config/constant");
const { createSubscriptionProduct, createInAppProduct, activateSubscriptionProduct, updateInAppProduct, activateAndDeactivateInAppProduct, inactivateSubscriptionProduct, updateSubscriptionProduct } = require("../../helper/gcpUtils");

module.exports = {
  async addStoragePlan(req, res) {
    try {
      // let add_storage_plan = await StoragePlan.create(
      //     {
      //         storage: req.body.storage,
      //         price: req.body.price,
      //         duration: req.body.duration,
      //         // is_active: req.body.is_active
      //     }
      //   );
      //   return add_storage_plan;
      let { type, title, productId, basePlanId, storage, status, price, currencyCode, regionCode, duration, number_of_api, description } = req.body;
      const packageName = constant.PLAY_CONSOLE_PACKAGE_NAME;
      const parts = price.toString().split('.');
      const units = parseInt(parts[0]);
      const nanos = parseInt(parts[1]) * 1e7;
      const priceMicros = price * 1000000;

      if (type === constant.SUBSCRIPTION_TYPE.SUBSCRIPTION) {
        const findProductId = await NewStoragePlan.findOne({
          where: { subscriptions_product_id: productId },
        });

        if (findProductId.is_deleted === "1") {
          return { type: 3, data: [] };
        }

        if (findProductId) {
          return { type: 0, data: [] };
        }

        const basePlan = {
          basePlanId: basePlanId,
          state: 'ACTIVE',
          regionalConfigs: {
            regionCode: regionCode,
            newSubscriberAvailability: true,
            price: {
              currencyCode: currencyCode,
              units,
              nanos,
            },
          },
          autoRenewingBasePlanType: {
            billingPeriodDuration: duration === 'yearly' ? 'P1Y' : 'P1M',
            gracePeriodDuration: 'P7D',
            resubscribeState: 'RESUBSCRIBE_STATE_ACTIVE',
            legacyCompatible: true,
          },
        };
        const subscriptionConfig = {
          packageName: packageName,
          productId: productId,
          basePlans: basePlan,
          listings: [
            {
              languageCode: 'en-US',
              title: title,
              benefits: [storage],
            },
          ],
        };

        await createSubscriptionProduct(subscriptionConfig);
        await activateSubscriptionProduct(packageName, productId, basePlanId);


        let add_storage_plan = await NewStoragePlan.create(
          {
            subscriptions_product_id: productId,
            title: title,
            subscriptions_benefits: storage,
            base_plan_id: basePlanId,
            duration: duration,
            price: price,
            price_amount_micros: priceMicros,
            price_currency_code: currencyCode,
            status: 'active',
            is_deleted: '0',
            is_free: '0',
          }
        );
        return { type: 1, data: add_storage_plan };

      } else if (type === constant.SUBSCRIPTION_TYPE.INAPP) {
        const findProductId = await AIStoragePlan.findOne({
          where: { ai_subscription_product_id: productId },
        });

        if (findProductId.is_deleted === "1") {
          return { type: 3, data: [] };
        }

        if (findProductId) {
          return { type: 0, data: [] };
        }

        const productConfig = {
          packageName: packageName,
          sku: productId,
          status,
          purchaseType: 'managedUser',
          defaultPrice: {
            priceMicros: priceMicros,
            currency: currencyCode,
          },
          prices: {
            [regionCode]: {
              priceMicros: priceMicros,
              currency: currencyCode,
            },
          },
          listings: {
            'en-US': {
              title: title,
              description: description,
            },
          },
          defaultLanguage: 'en-US',
        };

        await createInAppProduct(productConfig);

        let ai_storage_plan = await AIStoragePlan.create(
          {
            ai_subscription_product_id: productId,
            title: title,
            description: description,
            number_of_api: number_of_api,
            price: price,
            price_amount_micros: priceMicros,
            price_currency_code: currencyCode,
            status: status,
            is_deleted: '0',
            is_free: '0',
          }
        );
        return { type: 2, data: ai_storage_plan };
      }
    } catch (error) {
      throw error;
    }
  },

  async listStoragePlan(req, res) {
    try {
      let { sort_by, search, order, is_deleted } = req.body;
      // is_verified = is_verified === "null" ? "" : is_verified;
      search = search === undefined ? "" : search;
      let where = {
          is_deleted: "0",
          [Op.or]: [
            {
              subscriptions_product_id: { [Op.like]: `%${search}%` },
            },
            {
              title: { [Op.like]: `%${search}%` },
            },
            {
              subscriptions_benefits: { [Op.like]: `%${search}%` },
            },
            {
              price: { [Op.like]: `%${search}%` },
            },
            {
              duration: { [Op.like]: `%${search}%` },
            }
          ],
        }
      
      if (is_deleted === "1") {
        where["is_deleted"] = "1";
        where["is_free"] = "0";
      }
      // if (is_verified) {
      //   searchObj.where.is_verified = parseInt(is_verified);
      // }

      /*pagination*/
      const offset = (req.body.page - 1) * constant.LIMIT;
      const limit = constant.LIMIT;

      /* storage plan find query */
      let storage_plan_list = await NewStoragePlan.findAndCountAll({
        where: where,
        attributes: {
          include: ["createdAt", "updatedAt"],
        },
        order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
        limit: limit,
        offset: offset,
      });
      return storage_plan_list;
    } catch (error) {
      throw error;
    }
  },

  async editStoragePlan(req) {
    try {
      // let storage_plan_edit_obj = {
      //     storage: req.body.storage && req.body.storage,
      //     price: req.body.price && req.body.price,
      //     duration: req.body.duration && req.body.duration,
      //     status: req.body.status && req.body.status,
      // };
      // const storagePlan_id = await StoragePlan.findOne({where : {subscription_id : req.params.id}})
      // if(storagePlan_id !== null) {
      //   let edit_storage = await StoragePlan.update(
      //     storage_plan_edit_obj,
      //     { where: { subscription_id: req.params.id } }
      //   );
      //   var edit_storagePlan = await StoragePlan.findOne({
      //     where:{
      //         subscription_id: req.params.id
      //     }
      //   })
      //   return edit_storagePlan;
      // }
      // else {
      //   return 0
      // }

      let { type, title, productId, basePlanId, storage, status, price, currencyCode, regionCode, duration, number_of_api, description, isFree } = req.body;
      const packageName = constant.PLAY_CONSOLE_PACKAGE_NAME;
      const parts = price.toString().split('.');
      const units = parseInt(parts[0]);
      const nanos = parseInt(parts[1]) * 1e7;
      const priceMicros = price * 1000000;
      if (type === constant.SUBSCRIPTION_TYPE.SUBSCRIPTION) {
        const findPlan = await NewStoragePlan.findOne({
          where: { subscription_id: req.params.id },
        });

        if (!findPlan) {
          return { data: [], message: message.STORAGEPLANNOTFOUND }
        }

        const basePlan = {
          basePlanId: basePlanId,
          state: status.toUpperCase(),
          regionalConfigs: {
            regionCode: regionCode,
            newSubscriberAvailability: true,
            price: {
              currencyCode: currencyCode,
              units,
              nanos,
            },
          },
          autoRenewingBasePlanType: {
            billingPeriodDuration: duration === 'yearly' ? 'P1Y' : 'P1M',
            gracePeriodDuration: 'P7D',
            resubscribeState: 'RESUBSCRIBE_STATE_ACTIVE',
            legacyCompatible: true,
          },
        };
        const subscriptionConfig = {
          packageName: packageName,
          productId: productId,
          basePlans: basePlan,
          listings: [
            {
              languageCode: 'en-US',
              title: title,
              benefits: [storage],
            },
          ],
        };

        await updateSubscriptionProduct(subscriptionConfig);
        await activateSubscriptionProduct(packageName, productId, basePlanId);

        const obj = {
          subscriptions_product_id: productId,
          title: title,
          subscriptions_benefits: storage,
          base_plan_id: basePlanId,
          duration: duration,
          price: price,
          price_amount_micros: priceMicros,
          price_currency_code: currencyCode,
          status: status,
          is_deleted: '0',
          is_free: '0',
        }

        let add_storage_plan = await NewStoragePlan.update(obj,
          { where: { subscription_id: req.params.id } }
        );
        return { data: add_storage_plan, message: message.STORAGEPLANUPDATED };

      } else if (type === constant.SUBSCRIPTION_TYPE.INAPP) {

        const findPlan = await AIStoragePlan.findOne({
          where: { ai_subscription_id: req.params.id },
        });

        if (!findPlan) {
          return { data: [], message: message.STORAGEPLANNOTFOUND }
        }

        if (isFree !== '1') {
          const productConfig = {
            packageName: packageName,
            sku: productId,
            status,
            purchaseType: 'managedUser',
            defaultPrice: {
              priceMicros: priceMicros,
              currency: currencyCode,
            },
            prices: {
              [regionCode]: {
                priceMicros: priceMicros,
                currency: currencyCode,
              },
            },
            listings: {
              'en-US': {
                title: title,
                description: description,
              },
            },
            defaultLanguage: 'en-US',
          };

          await updateInAppProduct(productConfig);
        }
        const obj = {
          ai_subscription_product_id: productId,
          title: title,
          description: description,
          number_of_api: number_of_api,
          price: price,
          price_amount_micros: priceMicros,
          price_currency_code: currencyCode,
          status: status,
        }

        await AIStoragePlan.update(obj, {
          where: { ai_subscription_id: req.params.id },
        });
        const editedPlanData = await AIStoragePlan.findOne({
          where: {
            ai_subscription_id: req.params.id,
          },
        });
        return { data: editedPlanData, message: message.AISTORAGEPLANUPDATED };
      }
    } catch (error) {
      throw error;
    }
  },

  async deleteStoragePlan(req, res) {
    try {
      let find_storagePlan = await StoragePlan.update(
        {
          is_deleted: "1",
        },
        {
          where: {
            subscription_id: req.params.id,
          },
        }
      );
      return find_storagePlan;
    } catch (error) {
      throw error;
    }
  },

  async retriveStoragePlan(req, res) {
    try {

      let find_storagePlan = await NewStoragePlan.findOne({
        where: { subscription_id: req.params.id },
      });
      find_storagePlan.dataValues.price = parseFloat(find_storagePlan.price)
      return find_storagePlan;
    } catch (error) {
      throw error;
    }
  },

  async changeStatusPlan(req, res) {
    try {
      const { type, productId, basePlanId, status } = req.body;
      const packageName = constant.PLAY_CONSOLE_PACKAGE_NAME;

      if (type === constant.SUBSCRIPTION_TYPE.SUBSCRIPTION) {
        const findPlan = await NewStoragePlan.findOne({
          where: { subscription_id: req.params.id },
        });

        if (!findPlan) {
          return { data: [], message: message.STORAGEPLANNOTFOUND }
        }

        if (status === "active") {
          await activateSubscriptionProduct(packageName, productId, basePlanId);

        } else if (status === "inactive") {
          await inactivateSubscriptionProduct(packageName, productId, basePlanId);
        }

        const updateData = await NewStoragePlan.update(
          {
            status: status,
          },
          {
            where: {
              subscription_id: req.params.id,
            },
          }
        );
        const newMessage = `Plan ${status} successfully.`

        return { data: updateData, message: newMessage };

      } else if (type === constant.SUBSCRIPTION_TYPE.INAPP) {
        const findPlan = await AIStoragePlan.findOne({
          where: { ai_subscription_id: req.params.id },
        });

        if (!findPlan) {
          return { data: [], message: message.STORAGEPLANNOTFOUND }
        }

        await activateAndDeactivateInAppProduct(packageName, productId, status);

        const updateData = await AIStoragePlan.update(
          {
            status: status,
          },
          {
            where: {
              ai_subscription_id: req.params.id,
            },
          }
        );
        const newMessage = `Plan ${status} successfully.`

        return { data: updateData, message: newMessage };
      }
    } catch (error) {
      throw error;
    }
  },
};
