'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_token_deductions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_token_deductions.init(
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      token_per_image: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
      token_per_video_second: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
    },
    {
      sequelize,
      createdAt: false,
      updatedAt: false,
      modelName: "tbl_token_deductions",
    }
  );
  return tbl_token_deductions;
};