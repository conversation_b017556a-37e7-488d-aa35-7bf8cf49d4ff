/**
 * HTTP Status Codes and Response Status Configuration
 *
 * This module defines standardized HTTP status codes and application-level response status
 * constants used throughout the KyuleBag API. It ensures consistent status code usage
 * across all controllers and services for proper client-server communication.
 *
 * Business Logic Connection:
 * - Standardizes API response codes for mobile app and web client consumption
 * - Enables consistent error handling and success response patterns
 * - Supports proper HTTP semantics for RESTful API design
 * - Facilitates debugging and monitoring by using standard status codes
 *
 * Status Code Categories:
 * - 2xx Success: SUCCESSSTATUS (200)
 * - 4xx Client Errors: NOTFOUND (404), UNAUTHORIZEDUSER (401), errorStatus (400)
 * - 5xx Server Errors: INTERNALSERVERERRORSTATUS (500)
 *
 * Application Status Flags:
 * - SUCCESS (1): Operation completed successfully
 * - ERROR (0): Operation failed or encountered an error
 *
 * Usage Pattern:
 * Controllers use these constants to maintain consistent response formats:
 * - res.status(STATUS_CODES.SUCCESSSTATUS).send({status: STATUS_CODES.SUCCESS})
 * - res.status(STATUS_CODES.UNAUTHORIZEDUSER).send({status: STATUS_CODES.ERROR})
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

/**
 * Status codes configuration object
 * Contains both HTTP status codes and application-level status flags
 *
 * HTTP Status Codes (following RFC 7231 standards):
 * - SUCCESSSTATUS: Standard success response for successful operations
 * - INTERNALSERVERERRORSTATUS: Server error for unexpected failures
 * - NOTFOUND: Resource not found error
 * - INCORRECTPASSWORD: Authentication failure (password mismatch)
 * - UNAUTHORIZEDUSER: Authorization failure (invalid/missing credentials)
 * - errorStatus: Generic client error for malformed requests
 *
 * Application Status Flags:
 * - SUCCESS: Boolean-like flag indicating successful operation
 * - ERROR: Boolean-like flag indicating failed operation
 */
const status = {
  // HTTP 2xx Success Status Codes
  SUCCESSSTATUS: 200, // OK - Request successful, response contains requested data

  // HTTP 5xx Server Error Status Codes
  INTERNALSERVERERRORSTATUS: 500, // Internal Server Error - Unexpected server condition

  // HTTP 4xx Client Error Status Codes
  NOTFOUND: 404, // Not Found - Requested resource doesn't exist
  INCORRECTPASSWORD: 401, // Unauthorized - Password authentication failed
  UNAUTHORIZEDUSER: 401, // Unauthorized - User authentication/authorization failed
  errorStatus: 400, // Bad Request - Client sent malformed or invalid request

  // Application-Level Status Flags
  // Used in response JSON to indicate operation success/failure
  SUCCESS: 1, // Operation completed successfully
  ERROR: 0, // Operation failed or encountered an error
};

/**
 * Export status configuration for use throughout the application
 * This object is imported by controllers, services, and middleware
 * to ensure consistent status code usage across all API endpoints
 */
module.exports = {
  status,
};
