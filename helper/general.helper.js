/**
 * General Helper Utilities - Common Application Functions
 * 
 * This module provides a collection of utility functions used throughout the KyuleBag
 * application for various common operations including encryption, file processing,
 * random generation, cloud storage operations, and data manipulation.
 * 
 * Business Logic Connection:
 * - Supports secure data encryption for sensitive user information
 * - Enables file size calculations and storage quota management
 * - Provides random string/number generation for verification codes and tokens
 * - Integrates with cloud storage services for file operations
 * - Supports PDF generation for document creation features
 * - Handles date/time operations for content organization
 * - Provides utility functions for data formatting and validation
 * 
 * Key Features:
 * - AES-256-CBC encryption for data security
 * - File size formatting and calculation utilities
 * - OTP and random string generation for security
 * - Google Cloud Storage integration functions
 * - PDF generation using Puppeteer
 * - File processing and metadata extraction
 * - Data validation and sanitization helpers
 * 
 * Security Implementation:
 * - AES-256-CBC encryption with random keys and IVs
 * - Secure random number generation for OTPs
 * - Cryptographically secure string generation
 * - Input validation and sanitization
 * - File type and size validation support
 * 
 * External Integrations:
 * - Google Cloud Storage SDK
 * - Puppeteer for PDF generation
 * - Moment.js for date operations
 * - Node.js crypto module for encryption
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

const crypto = require('crypto');
const fs = require('fs');
const get_path = require('path');
const jwt = require('jsonwebtoken');
const http2 = require('http2');
const moment = require('moment');
const puppeteer = require('puppeteer');
const { Storage } = require('@google-cloud/storage');
const constant = require('../config/constant');
const gcpUtils = require('./gcpUtils');

/**
 * Encryption Configuration
 * AES-256-CBC encryption setup with random keys and initialization vectors
 */
const algorithm = 'aes-256-cbc';
const key = crypto.randomBytes(32);
const iv = crypto.randomBytes(16);

/**
 * File Size Units
 * Array of size units for file size formatting
 */
var sizes = ['bytes', 'KB', 'MB', 'GB', 'TB'];

/**
 * Google Cloud Storage Configuration
 * Initialize GCS client with service account credentials
 */
const storage = new Storage({
  projectId: constant.GCP_PROJECT_ID,
  credentials: require('../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json'),
});

const bucketName = constant.GCP_BUCKET_NAME;

/**
 * Utility Functions Export Object
 * Collection of helper functions for various application needs
 */
module.exports = {
  /**
   * Generate Random OTP (One-Time Password)
   *
   * Creates a random 6-digit numeric string for verification purposes.
   * Used for email verification, phone verification, and password reset flows.
   *
   * @returns {string} 6-digit random numeric string
   *
   * Usage Examples:
   * - Email verification codes
   * - SMS verification codes
   * - Two-factor authentication tokens
   * - Password reset verification
   *
   * Security Considerations:
   * - Uses Math.random() which is pseudorandom (sufficient for OTPs)
   * - 6-digit format provides 1,000,000 possible combinations
   * - Should be used with expiration times and attempt limits
   *
   * @example
   * const otp = generateOtp(); // Returns: "123456"
   */
  generateOtp() {
    return Math.random().toString().substring(2, 8);
  },

  /**
   * Generate 4-Digit OTP
   *
   * Creates a 4-digit numeric OTP for simpler verification scenarios.
   * Provides 10,000 possible combinations with improved user experience.
   *
   * @returns {number} 4-digit random number (1000-9999)
   *
   * Usage Examples:
   * - Quick verification codes
   * - PIN-style authentication
   * - Simple confirmation codes
   *
   * @example
   * const otp = generateOTP(); // Returns: 1234
   */
  generateOTP() {
    return Math.floor(1000 + Math.random() * 9000);
  },

  /**
   * Generate Random String with Special Characters
   *
   * Creates a cryptographically secure random string with mixed character types
   * including uppercase, lowercase, numbers, and special characters.
   *
   * @param {number} length - Desired length of the generated string
   * @returns {string} Random string with mixed character types
   *
   * Character Set Includes:
   * - Uppercase letters (A-Z)
   * - Lowercase letters (a-z)
   * - Numbers (0-9)
   * - Special characters (!#%^_*()-)
   *
   * Usage Examples:
   * - Secure password generation
   * - API key generation
   * - Session token creation
   * - Random identifiers for sensitive operations
   *
   * Security Features:
   * - Large character set increases entropy
   * - Suitable for cryptographic applications
   * - Configurable length for different security requirements
   *
   * @example
   * const randomStr = generateRandomString(16);
   * // Returns: "A!2def#G8ij(klm9"
   */
  generateRandomString(length) {
    var result = '';
    var characters =
      'AB!CD#EFGHI%JKLMNO^PQRSTU_VWXYZa*bcdefghij(klmnopqrs?tuvwxyz0123-456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  },

  /**
   * AES-256-CBC Encryption Function
   *
   * Encrypts plaintext data using AES-256-CBC algorithm with random IV.
   * Provides strong encryption for sensitive data storage and transmission.
   *
   * @param {string} text - Plaintext data to encrypt
   * @returns {Object} Encryption result with IV and encrypted data
   *
   * Return Structure:
   * ```javascript
   * {
   *   iv: "hex-encoded-initialization-vector",
   *   encryptedData: "hex-encoded-encrypted-data"
   * }
   * ```
   *
   * Security Features:
   * - AES-256-CBC provides strong encryption
   * - Random IV prevents pattern recognition
   * - Hex encoding for safe storage/transmission
   * - Industry-standard encryption algorithm
   *
   * Usage Examples:
   * - Encrypting sensitive user data
   * - Securing API keys and credentials
   * - Protecting private notes and documents
   * - Database field encryption
   *
   * @example
   * const encrypted = encrypt("sensitive data");
   * // Returns: { iv: "abc123...", encryptedData: "def456..." }
   */
  encrypt(text) {
    let cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key), iv);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return { iv: iv.toString('hex'), encryptedData: encrypted.toString('hex') };
  },

  /**
   * AES-256-CBC Decryption Function
   *
   * Decrypts data encrypted with the corresponding encrypt function.
   * Restores original plaintext from encrypted data and IV.
   *
   * @param {Object} text - Encryption object containing IV and encrypted data
   * @param {string} text.iv - Hex-encoded initialization vector
   * @param {string} text.encryptedData - Hex-encoded encrypted data
   * @returns {string} Decrypted plaintext data
   *
   * Usage Examples:
   * - Decrypting stored sensitive data
   * - Processing encrypted API responses
   * - Retrieving encrypted user information
   * - Secure data transmission
   *
   * @example
   * const decrypted = decrypt({ iv: "abc123...", encryptedData: "def456..." });
   * // Returns: "sensitive data"
   */
  decrypt(text) {
    let iv = Buffer.from(text.iv, 'hex');
    let encryptedText = Buffer.from(text.encryptedData, 'hex');
    let decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key), iv);
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  },

  /**
   * File Size Formatting Function
   *
   * Converts bytes to human-readable format with appropriate units.
   *
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted size string with units
   */
  formatSize(bytes) {
    if (bytes == 0) return '0 Byte';
    var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
  },
  async sizeOf(path) {
    return s3
      .headObject({ Key: path, Bucket: process.env.S3_BUCKET_NAME })
      .promise()
      .then((res) => res.ContentLength);
  },

  async arrayMediaURL(array) {
    for (let index = 0; index < array.length; index++) {
      const data = array[index];
      if (data.image_details.length) {
        const image_details = data.image_details[0].dataValues;

        image_details.media = image_details.media
          ? await gcpUtils.getPrivateUrl(
              constant.GCP_BUCKET_NAME,
              constant.GCP_BUCKET_FOLDER +
                constant.GCP_USER_FOLDER +
                array[index].dataValues.user_id +
                '/' +
                constant.GCP_ITEM_FOLDER +
                array[index].dataValues.item_id +
                '/' +
                image_details.media
            )
          : '';
      }
    }
    return array;
  },

  async staticFileImages() {
    const fileNames = [
      'doc.png',
      'log.png',
      'odp.png',
      'ods.png',
      'pdf.png',
      'powerpoint.png',
      'txt.png',
      'wav.png',
      'xlr.png',
      'xls.png',
      'apk.png',
      'zip.png',
      'csv.png',
      'json.png',
    ];

    const urls = await Promise.all(
      fileNames.map((fileName) =>
        gcpUtils.getPrivateUrl(
          constant.GCP_BUCKET_NAME,
          `${constant.GCP_BUCKET_FOLDER}${constant.GCP_STATIC_FOLDER}${fileName}`,
          {
            action: 'read',
            expires: moment().add(1, 'year').format(),
          }
        )
      )
    );

    const staticImages = fileNames.reduce((acc, fileName, index) => {
      const key = fileName.replace('.png', 'Image');
      acc[key] = urls[index];
      return acc;
    }, {});

    return staticImages;
  },

  async userProfileMediaURL(array, isUser) {
    for (let index = 0; index < array.length; index++) {
      const data = array[index];

      if (isUser && data.photo) {
        data.photo = await gcpUtils.getPrivateUrl(
          constant.GCP_BUCKET_NAME,
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            array[index].dataValues.user_id +
            '/' +
            data.photo
        );
      } else if (!isUser && data.photo) {
        data.photo = await gcpUtils.getPrivateUrl(
          constant.GCP_BUCKET_NAME,
          constant.GCP_BUCKET_FOLDER +
            constant.GCP_ADMIN_FOLDER +
            array[index].dataValues.admin_id +
            '/' +
            data.photo
        );
      }
    }
    return array;
  },

  async sizeOfGCPFile(path) {
    return new Promise((resolve, reject) => {
      const myBucket = storage.bucket(bucketName);
      const file = myBucket.file(path);
      file.get(function (err, file, apiResponse) {
        resolve(file);
      });
    });
  },

  async newSizeOfGCPFile(path) {
    return new Promise((resolve, reject) => {
      const myBucket = storage.bucket(bucketName);
      const file = myBucket.file(path);
      file.get(function (err, file, apiResponse) {
        if (err) {
          return reject(err);
        }
        resolve(file.metadata.size);
      });
    });
  },

  detectUrlFromText(text) {
    if (!text) return [];

    text = text.replace(/[()'"<>{}]/g, ' ');
    const urlRegex = new RegExp(constant.DETECT_URL_REGEX);
    return text.match(urlRegex);
  },

  async fetchPreview(url) {
    try {
      const browser = await puppeteer.launch({
        headless: true,
        executablePath: process.env.PUPPETEER_EXECUTION_PATH,
        args: (process.env.PUPPETEER_ARGS || '').split(' '),
      });
      const page = await browser.newPage();
      await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      const metadata = await page.evaluate(() => {
        const getMetaContent = (name) => {
          const element = document.querySelector(
            `meta[name="${name}"], meta[property="${name}"]`
          );
          return element ? element.content : '';
        };

        return {
          title: document.title || getMetaContent('og:title') || '',
          description:
            getMetaContent('description') ||
            getMetaContent('og:description') ||
            '',
          image: getMetaContent('og:image') || '',
          url: window.location.href,
          siteName: getMetaContent('og:site_name') || '',
        };
      });

      return metadata;
    } catch (error) {
      console.error('Error fetching preview:', error);
      return null;
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  },
};
