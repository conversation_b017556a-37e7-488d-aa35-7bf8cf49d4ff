/*
 * Summary:     dashboard.js file handles all routes, request and response for DASHBOARD - (CMS related actions).
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

/* require for Controller */
const dashboardController = require("../../controllers/cms/dashboard.controller");

/* route of Dashboard management */
router.post("/list", authentication, dashboardController.dashboard);
/*List dashboard API*/

module.exports = router;
