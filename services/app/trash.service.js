/*
 * Summary:     user.services file for handling all USER related actions.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules,models and constants for configuration */
const Items = require("../../database/models").tbl_items;
const Notes = require("../../database/models").tbl_item_notes;
const Tags = require("../../database/models").tbl_item_tags;
const Tag_names = require("../../database/models").tbl_tag_names;
const Images = require("../../database/models").tbl_media;
const Sharing_details = require("../../database/models").tbl_sharing_details;
const Activity_logs = require("../../database//models").tbl_activity_logs;
const OCRLabels = require("../../database/models").tbl_item_ocr_labels;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");
const image_size = require("../../helper/general.helper");
const gcpDelete = require("../../middleware/multer_gcp_delete");

module.exports = {
  /* list */
  async list(req, res) {
    let { page, sort_by, order } = req.body;
    page = page === undefined ? 1 : page;
    const offset = (page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;
    sort_by = sort_by === undefined || sort_by === "" ? "item_id" : sort_by;
    order = order === undefined || order === "" ? "DESC" : order;
    var find_items = await Items.findAndCountAll({
      where: {
        user_id: req.user_id,
        is_deleted: 1,
      },

      include: [
        {
          model: Images,
          as: "image_details",
          attributes: {
            exclude: ["createdAt", "updatedAt"],
          },
        },
        {
          model: Sharing_details,
          as: "item_sharing_details",
          attributes: {
            exclude: ["item_id"],
          },
        },
        {
          model: Notes,
          as: "items_note_lists",
          attributes: {
            exclude: ["item_id"],
          },
        },
        {
          model: Tags,
          as: "items_tag_lists",
          attributes: {
            exclude: ["item_id", "id"],
          },

          include: [
            {
              model: Tag_names,
              as: "items_tag_names",
              attributes: {
                exclude: ["tag_name_id", "user_id"],
              },
            },
          ],
        },
        {
          model: OCRLabels,
          attributes: ["ocr_label", "id"],
          as: "item_ocr_labels",
        },
      ],

      attributes: [
        "item_id",
        "user_id",
        "title",
        "description",
        "item_type",
        "is_bookmarked",
        "is_private",
        "delete_type",
        "is_deleted",
        "createdAt",
        "uploadedAt",
      ],
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      offset: offset,
      limit: limit,
      distinct: `${Items.item_id}`,
    });
    if (find_items.count === 0) {
      return 0;
    } else {
      await Promise.all(
        find_items.rows.map(item_result => {
          item_result.dataValues.createdAt = item_result.dataValues.createdAt
            .toISOString()
            .substr(0, 19)
            .replace("T", " ");
          item_result.dataValues.uploadedAt = item_result.dataValues.uploadedAt
            .toISOString()
            .substr(0, 19)
            .replace("T", " ");
          let note_list = item_result.dataValues.items_note_lists;
          let tag_list = item_result.dataValues.items_tag_lists;

          let item_sharing_details =
            item_result.dataValues.item_sharing_details;
          if (item_sharing_details) {
            item_sharing_details.dataValues.createdAt =
              item_sharing_details.dataValues.createdAt
                .toISOString()
                .substr(0, 19)
                .replace("T", " ");
            item_sharing_details.dataValues.updatedAt =
              item_sharing_details.dataValues.updatedAt
                .toISOString()
                .substr(0, 19)
                .replace("T", " ");
          }
          item_note = {};

          media_details = item_result.dataValues.image_details;
          media_details.map(data => {
            // data.dataValues.media =
            //   constant.GCP_URL +
            //   constant.GCP_BUCKET_NAME +
            //   "/" +
            //   constant.GCP_BUCKET_FOLDER +
            //   constant.GCP_USER_FOLDER +
            //   data.dataValues.user_id +
            //   "/" +
            //   constant.GCP_ITEM_FOLDER +
            //   data.dataValues.item_id +
            //   "/" +
            //   data.dataValues.media;
            data.dataValues.mediaName = data.dataValues.media;
            let imgs = image_size.formatSize(data.dataValues.size * 1000);
            let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;

            data.dataValues.size = b.toString() + " " + imgs.split(" ")[1];
          });
          note_list.map(data => {
            if (data.createdAt) {
              data.dataValues.createdAt = data.dataValues.createdAt
                .toISOString()
                .substr(0, 19)
                .replace("T", " ");
              data.dataValues.updatedAt = data.dataValues.updatedAt
                .toISOString()
                .substr(0, 19)
                .replace("T", " ");

              item_note.note_id = data.dataValues.id;
              item_note.note_description = data.dataValues.note_description;
              item_note.createdAt = data.dataValues.createdAt;
              item_note.updatedAt = data.dataValues.updatedAt;
            }

            item_result.dataValues.item_note = item_note;
          });

          tag_list.map((data, index) => {
            if (
              data.createdAt &&
              data.dataValues.is_deleted === true &&
              data.dataValues.items_tag_names !== null
            ) {
              data.dataValues.createdAt = data.dataValues.createdAt
                .toISOString()
                .substr(0, 19)
                .replace("T", " ");
              data.dataValues.updatedAt = data.dataValues.updatedAt
                .toISOString()
                .substr(0, 19)
                .replace("T", " ");

              if (data.dataValues.items_tag_names !== null) {
                data.dataValues.tag_name =
                  data.items_tag_names.dataValues.tag_name;
              }
            }
          });
        })
      );

      find_items.rows.map((data, index) => {
        data.items_tag_lists.map(data1 => {
          delete data1.dataValues.items_tag_names;
        });
        delete data.dataValues.items_note_lists;
      });

      // find_items.rows = await image_size.arrayMediaURL(find_items.rows)

      return find_items;
    }
  },


  /* restore */
  async restore(req, res) {
    let { item_id, is_select_all, unchecked_ids } = req.body;
    var item_ids;

    // manage select all feature
    if (is_select_all) {
      let where = [
        {
          user_id: req.user_id,
          is_deleted: 1,
        },
      ];

      if (unchecked_ids) {
        // unselected items
        let unchecked_item = JSON.parse(unchecked_ids);
        where.push({
          item_id: {
            [Op.notIn]: unchecked_item,
          },
        });
      }

      let find_all_item = await Items.findAndCountAll({
        where,
        attributes: ["item_id"],
      });

      item_ids = find_all_item.rows.map((item) => item.dataValues.item_id);
    } else {
      item_ids = JSON.parse(item_id);
    }

    var find_item = await Items.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 1,
        user_id: req.user_id
      },
    });
    var find_img = await Images.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 1,
      },
    });
    var sharing_details = await Sharing_details.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 1,
      },
    });


    if (find_item) {
      if (find_item.dataValues.delete_type.toLowerCase() === "cloud") {
        if (find_img) {
          if (find_img.dataValues.media) {

          }
        }
        if (sharing_details) {
          await Sharing_details.update(
            {
              is_deleted: 0,
            },
            {
              where: {
                item_id: item_ids,
              },
            }
          );
        }

        await Images.update(
          {
            is_deleted: 0,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );

        await Tags.update(
          {
            is_deleted: 0,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );

        await Notes.update(
          {
            is_deleted: 0,
          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );


        await Items.update(
          {
            is_deleted: 0,
            delete_type: "NORMAL" //After restoring item from trash it's delete type must be normal

          },
          {
            where: {
              item_id: item_ids,
            },
          }
        );

      }
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Restore Item",
        // description: `Item restored from trash.`
        description: item_ids?.length ? `${item_ids?.length} Items restored from Recycle Bin.` : `Item restored from Recycle Bin.`
      });
      return 1;
    }
    {
      return 0;
    }
  },

  /* delete */
  async oldDelete(req, res) {
    let { item_id, is_select_all, unchecked_ids } = req.body;
    var item_ids;

    // manage select all feature
    if (is_select_all) {
      let where = [
        {
          user_id: req.user_id,
          is_deleted: 1,
        },
      ];

      if (unchecked_ids) {
        // unselected items
        let unchecked_item = JSON.parse(unchecked_ids);
        where.push({
          item_id: {
            [Op.notIn]: unchecked_item,
          },
        });
      }

      let find_all_item = await Items.findAndCountAll({
        where,
        attributes: ["item_id"],
      });

      item_ids = find_all_item.rows.map((item) => item.dataValues.item_id);
    } else {
      item_ids = JSON.parse(item_id);
    }

    var find_item = await Items.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 1,
      },
    });
    var find_img = await Images.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 1,
      },
    });
    var sharing_details = await Sharing_details.findOne({
      where: {
        item_id: item_ids,
        is_deleted: 1,
      },
    });
    if (find_item.is_upload === true) {
      if (find_img) {
        if (find_img.dataValues.media) {
          await gcpDelete(
            constant.GCP_BUCKET_FOLDER +
            constant.GCP_USER_FOLDER +
            req.user_id +
            "/" +
            constant.GCP_ITEM_FOLDER +
            find_img.item_id +
            "/" +
            find_img.dataValues.media
          );
        }

        await Images.destroy({
          where: {
            item_id: item_ids,
          },
        });
      }
      if (sharing_details) {
        await Sharing_details.destroy({
          where: {
            item_id: item_ids,
          },
        });

      }

      await Tags.destroy({
        where: {
          item_id: item_ids,
        },
      });

      await Notes.destroy({
        where: {
          item_id: item_ids,
        },
      });

      await Items.destroy({
        where: {
          item_id: item_ids,
        },
      });

      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Delete Item",
        description: `Item permanently deleted.`
      });
      return 1;
    }
    {
      return 0;
    }
  },

  /* delete */
  // async delete(req, res) {
  //   let { item_id, is_select_all, unchecked_ids } = req.body;
  //   console.log("🚀 ~ delete ~ is_select_all:", is_select_all)
  //   var item_ids;

  //   // manage select all feature
  //   if (is_select_all) {
  //     console.log("in");

  //     let where = [
  //       {
  //         user_id: req.user_id,
  //         is_deleted: 1,
  //       },
  //     ];

  //     if (unchecked_ids) {
  //       console.log("🚀 ~ delete ~ unchecked_ids:", unchecked_ids)
  //       // unselected items
  //       let unchecked_item = JSON.parse(unchecked_ids);
  //       where.push({
  //         item_id: {
  //           [Op.notIn]: unchecked_item,
  //         },
  //       });
  //     }

  //     let find_all_item = await Items.findAndCountAll({
  //       where,
  //       attributes: ["item_id"],
  //     });

  //     item_ids = find_all_item.rows.map((item) => item.dataValues.item_id);
  //     console.log("🚀 ~ delete ~ item_ids:", item_ids)
  //   } else {
  //     item_ids = JSON.parse(item_id);
  //   }
  //   console.log("🚀 ~ delete ~ item_ids:", item_ids.length)

  //   for (let index = 0; index < item_ids.length; index++) {
  //     const itemId = item_ids[index];
  //     console.log("🚀 ~ delete ~ itemId:", itemId)

  //     var find_item = await Items.findOne({
  //       where: {
  //         item_id: itemId,
  //         is_deleted: 1,
  //       },
  //     });

  //     var find_img = await Images.findOne({
  //       where: {
  //         item_id: itemId,
  //         is_deleted: 1,
  //       },
  //     });

  //     var sharing_details = await Sharing_details.findOne({
  //       where: {
  //         item_id: itemId,
  //         is_deleted: 1,
  //       },
  //     });

  //     if (find_item.is_upload === true) {
  //       if (find_img) {
  //         if (find_img.media) {
  //           console.log("🚀 ~ delete ~ find_img.media:", find_img.media)

  //           await gcpDelete(
  //             constant.GCP_BUCKET_FOLDER +
  //             constant.GCP_USER_FOLDER +
  //             req.user_id +
  //             "/" +
  //             constant.GCP_ITEM_FOLDER +
  //             itemId +
  //             "/" +
  //             find_img.media
  //           );
  //         }

  //         await Images.destroy({
  //           where: {
  //             item_id: itemId,
  //           },
  //         });
  //       }
  //     }
  //       if (sharing_details) {
  //         await Sharing_details.destroy({
  //           where: {
  //             item_id: itemId,
  //           },
  //         });

  //       }

  //       await Tags.destroy({
  //         where: {
  //           item_id: itemId,
  //         },
  //       });

  //       await Notes.destroy({
  //         where: {
  //           item_id: itemId,
  //         },
  //       });

  //       await Items.destroy({
  //         where: {
  //           item_id: itemId,
  //         },
  //       });

  //     }    
  //     var item_activity_log = await Activity_logs.create({
  //       user_id: req.user_id,
  //       title: "Delete Item",
  //       description: `Item permanently deleted.`
  //     });


  //     if (item_ids.length) {
  //       return 1;
  //     } else {
  //       return 0;
  //     }
  // },

  /* delete */
  async delete(req, res) {
    let { item_id, is_select_all, unchecked_ids } = req.body;
    let item_ids;

    // manage select all feature
    if (is_select_all) {
      console.log("in");

      let where = [
        {
          user_id: req.user_id,
          is_deleted: 1,
        },
      ];

      if (unchecked_ids) {
        // unselected items
        let unchecked_item = JSON.parse(unchecked_ids);
        where.push({
          item_id: {
            [Op.notIn]: unchecked_item,
          },
        });
      }

      let find_all_item = await Items.findAndCountAll({
        where,
        attributes: ["item_id"],
      });

      item_ids = find_all_item.rows.map((item) => item.dataValues.item_id);
    } else {
      item_ids = JSON.parse(item_id);
    }
    console.log("🚀 ~ delete ~ item_ids:", item_ids.length)

    // Find all items, images, and sharing details in parallel
    let [items, images, sharingDetails] = await Promise.all([
      Items.findAll({
        where: {
          item_id: item_ids,
          is_deleted: 1,
        }
      }),
      Images.findAll({
        where: {
          item_id: item_ids,
          // is_deleted: 1,
        }
      }),
      Sharing_details.findAll({
        where: {
          item_id: item_ids,
          is_deleted: 1,
        }
      })
    ]);

    // Prepare deletions and other async operations in parallel
    let deletePromises = [];

    items.forEach((item) => {
      let image = images.find(img => img.item_id === item.item_id);
      let sharingDetail = sharingDetails.find(sd => sd.item_id === item.item_id);

      if (item.is_upload === true && image && image.media) {
        deletePromises.push(gcpDelete(
          constant.GCP_BUCKET_FOLDER +
          constant.GCP_USER_FOLDER +
          req.user_id +
          "/" +
          constant.GCP_ITEM_FOLDER +
          item.item_id +
          "/" +
          image.media
        ));
      }

      if (image) {
        deletePromises.push(Images.destroy({
          where: { item_id: item.item_id }
        }));
      }

      if (sharingDetail) {
        deletePromises.push(Sharing_details.destroy({
          where: { item_id: item.item_id }
        }));
      }

      deletePromises.push(Tags.destroy({
        where: { item_id: item.item_id }
      }));

      deletePromises.push(Notes.destroy({
        where: { item_id: item.item_id }
      }));

      deletePromises.push(Items.destroy({
        where: { item_id: item.item_id }
      }));
    });

    // Execute all deletion operations in parallel
    await Promise.all(deletePromises);

    // Log the activity
    await Activity_logs.create({
      user_id: req.user_id,
      title: "Delete Item",
      description: `Item permanently deleted.`
    });

    return item_ids.length ? 1 : 0;
  }
};
