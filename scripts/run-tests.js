#!/usr/bin/env node

/**
 * Test Runner Script for KyuleBag Unit Tests
 * 
 * Usage:
 *   node scripts/run-tests.js [test-suite]
 * 
 * Available test suites:
 *   signup  - Run signup flow tests
 *   signin  - Run signin flow tests  
 *   home    - Run home/dashboard flow tests
 *   drawer  - Run drawer menu flow tests
 *   all     - Run all unit tests
 *   coverage - Run all tests with coverage report
 */

const { execSync } = require('child_process');

function runCommand(command) {
  try {
    console.log(`🚀 Running: ${command}`);
    execSync(command, { stdio: 'inherit' });
  } catch (error) {
    console.error(`❌ Command failed: ${command}`);
    process.exit(1);
  }
}

function showHelp() {
  console.log(`
📋 KyuleBag Unit Test Runner

Usage: node scripts/run-tests.js <command>

Available Commands:
  signup      - Run signup flow tests (42 tests)
  signin      - Run signin flow tests (30 tests)  
  home        - Run home/dashboard flow tests (30 tests)
  drawer      - Run drawer menu flow tests (41 tests)
  recyclebin  - Run recycle bin flow tests (29 tests)
  trash       - Alias for recyclebin
  unit        - Run all unit tests
  all         - Run all unit tests
  coverage    - Run tests with coverage report
  help        - Show this help message

Examples:
  node scripts/run-tests.js signup
  node scripts/run-tests.js signin
  node scripts/run-tests.js home
  node scripts/run-tests.js drawer
  node scripts/run-tests.js recyclebin
  node scripts/run-tests.js coverage
`);
}

const command = process.argv[2];

switch (command) {
  case 'signup':
    runCommand('npx jest services/app/__tests__/common.service.signup.test.js --verbose');
    break;
  
  case 'signin':
    runCommand('npx jest services/app/__tests__/common.service.signin.test.js --verbose');
    break;
    
  case 'home':
    runCommand('npx jest services/app/__tests__/common.service.home.test.js --verbose');
    break;

  case 'drawer':
    runCommand('npx jest services/app/__tests__/common.service.drawer.test.js --verbose');
    break;
  
  case 'recyclebin':
  case 'trash':
    runCommand('npx jest services/app/__tests__/trash.service.recyclebin.test.js --verbose');
    break;

  case 'unit':
  case 'all':
    console.log('🚀 Running all unit tests...\n');
    runCommand('npx jest services/app/__tests__/ --verbose');
    break;
    
  case 'coverage':
    runCommand('npm run test:coverage');
    break;
    
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
    
  default:
    if (!command) {
      showHelp();
    } else {
      console.error(`❌ Unknown command: ${command}`);
      showHelp();
      process.exit(1);
    }
} 