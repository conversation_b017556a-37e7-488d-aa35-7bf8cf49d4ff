'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    return Promise.all([
      queryInterface.addColumn("tbl_subscriptions", "offer_id_token", {
        type: Sequelize.TEXT,
      }),
      queryInterface.addColumn("tbl_subscriptions", "base_plan_id", {
        type: Sequelize.TEXT,
      }),
      queryInterface.addColumn("tbl_subscriptions", "price_amount_micros", {
        type: Sequelize.INTEGER,
      }),
      queryInterface.addColumn("tbl_subscriptions", "price_currency_code", {
        type: Sequelize.STRING,
      }),
    ]);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
