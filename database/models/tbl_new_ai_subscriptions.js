'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_new_ai_subscriptions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_new_ai_subscriptions.init({
    ai_subscription_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    ai_subscription_product_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    number_of_api: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    price_amount_micros: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    price_currency_code: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [3, 3],
      },
    },
    status: {
      type: DataTypes.ENUM('inactive', 'active'),
      defaultValue: 'inactive',
    },
    is_deleted: {
      allowNull: false,
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
    is_free: {
      allowNull: false,
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
  }, {
    sequelize,
    createdAt: false,
    updatedAt: false,
    modelName: 'tbl_new_ai_subscriptions',
  });
  return tbl_new_ai_subscriptions;
};