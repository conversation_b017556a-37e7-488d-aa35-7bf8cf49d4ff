'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_announcements extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_announcements.init({
    announcement_id:{
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    admin_id: {
      type: DataTypes.INTEGER
    },
    user_id:{
      type: DataTypes.INTEGER
    },
    isDeleted:{
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    is_announcement:{
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    content: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'tbl_announcements',
  });
  return tbl_announcements;
};