const User = require("../database/models").tbl_users;
// const serviceAccount = require("../assets/kyulebag-95d98-firebase-adminsdk-i9yk1-31bc3c098f.json");
const serviceAccount = require("../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json");
var FCM = require("fcm-node");
const { google } = require('googleapis');
const axios = require('axios');
var serverKey =
  "AAAA58iaJlA:APA91bHvtgrAccXn-jTV4I3k9pCpcFuCEQ_ljDryRGxkC4s8B6nRZmfXBhNPLxQbOJYUwZC3s9bQzIB6I3Y0K4lVPE_o-_lATiV46ENTPg8X4vtCPJA2j5gaK_Cr12x07X0S0KzkNuJY";
var fcm = new FCM(serverKey);

async function getAccessToken() {
  try {

    return new Promise(function (resolve, reject) {
      const SCOPES = ['https://www.googleapis.com/auth/firebase.messaging'];

      const jwtClient = new google.auth.JWT(
        serviceAccount.client_email,
        null,
        serviceAccount.private_key,
        SCOPES,
        null
      );
      jwtClient.authorize(function (err, tokens) {
        if (err) {
          reject(err);
          return;
        }
        resolve(tokens.access_token);
      });
    });
  } catch (error) {
    console.log("🚀 ~ getAccessToken ~ error:", error)
  }
}

const androidNotification = (notification, tokens) => {
  var message = {
    //this may vary according to the message type (single recipient, multicast, topic, et cetera)
    to: tokens,
    data: {
      //you can send only notification or only data(or include both)
      title: "Kyulbag Channel",
      message: notification.content,
      body: {
        content: notification.content,
        announcement_id: notification.announcement_id,
        admin_id: notification.admin_id,
        createdAt: notification.createdAt,
      },
      notification_type: "ANNOUNCEMENT",
    },
  };
  fcm.send(message, function (err, response) {
    if (err) {
      console.log('Something has gone wrong:', err);
    } else {
      // showToast("Successfully sent with response");
      console.log("Successfully sent with response:", response);
    }
  });
};

//this function is for count page no
var paginationCountFunction = (totalRecords, limit = null) => {
  return new Promise((resolve, reject) => {
    let divider = limit || DefaultPaginationLimit;
    var totalPageNo = Math.floor(totalRecords / divider);
    var totalPageNoModulo = Math.floor(totalRecords % divider);
    if (totalPageNoModulo != 0) {
      totalPageNo = totalPageNo + 1;
    }

    var returnObject = {};
    returnObject.totalPageNo = totalPageNo;
    return resolve(returnObject);
  });
};

const sendNotification = (where, notification, limit = 400) => {
  return new Promise((resolve, reject) => {
    User.findAndCountAll({
      where: {
        ...where,
        push_notification: "1",
        is_deleted: "0",
      },
    })
      .then(async (count) => {
        // console.log(count.count);
        let pageNumbers = await paginationCountFunction(count.count, limit);
        for (let i = 0; i < pageNumbers.totalPageNo; i++) {
          await User.findAll({
            where: {
              ...where,
              push_notification: "1",
              is_deleted: "0",
            },
            limit,
            offset: i * limit,
            attributes: ["device_token"],
          }).then(async (users) => {
            let tokens = [];
            users.forEach((user) => {
              if (user.device_token) {
                tokens.push(user.device_token);
              }
              androidNotification(notification, user.device_token);
            });
          });
        }
        resolve("success");
      })
      .catch((err) => reject(err));
  });
};

async function fcmNewNotification(notification, tokens) {
  try {
    console.log("Tokens :", tokens);
    console.log("notification", notification)

    const accessToken = await getAccessToken();
    const URL = `https://fcm.googleapis.com/v1/projects/${serviceAccount.project_id}/messages:send`;


    const notificationObjs = tokens.map(token => ({
      message: {
        // notification: {
        //   title: "Kyulebag Channel",
        //   content: notification.content,
        // },
        data: {
          title: "Kyulebag Channel",
          content: notification.content,
          type: "ANNOUNCEMENT",
          announcement_id: `${notification.announcement_id}`,
          admin_id: notification.admin_id,
          createdAt: notification.createdAt,
        },
        token: token,
      },
    }));

    const responses = await Promise.all(notificationObjs.map(async notificationObj => {
      try {
        const response = await axios.post(URL, notificationObj, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        });

        console.log('Successfully Send Notification :', response.data)
        return response.data;
      } catch (error) {
        console.log('Error in send notification :', error.response ? error.response.data : error.message);
      }
    }));
  } catch (error) {
    console.log('Error in send notification :', error.response ? error.response.data : error.message);
  }
}

const sendFcmNewNotification = (where, notification, limit = 400) => {
  return new Promise((resolve, reject) => {
    User.count({
      where: {
        ...where,
        is_deleted: '0',
        push_notification: '1',
      }
    })
      .then(async count => {
        console.log(count)
        let pageNumbers = await paginationCountFunction(count, limit)
        for (let i = 0; i < pageNumbers.totalPageNo; i++) {
          await User.findAll({
            where: {
              ...where,
              is_deleted: '0',
              push_notification: '1',
            },
            limit,
            offset: i * limit,
            attributes: ['device_token']
          }).then(async users => {
            let tokens = []
            users.forEach(user => {
              if (user.device_token) {
                tokens.push(user.device_token)
              }
            })

            // Ensure unique tokens
            const uniqueTokens = [...new Set(tokens)];

            await fcmNewNotification(notification, uniqueTokens);
          })
        }
        resolve('success')
      })
      .catch(err => reject(err))
  })
}

module.exports = {
  paginationCountFunction,
  sendNotification,
  sendFcmNewNotification
};