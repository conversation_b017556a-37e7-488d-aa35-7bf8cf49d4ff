const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const comment_service = require("../../services/cms/comment.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async listComments(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_comment_res = await comment_service.listComments(
        req,
        res
      );
      if (list_comment_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_comment_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.COMMENTLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listComments -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async allListComments(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_comment_res = await comment_service.allListComments(
        req,
        res
      );
      if (list_comment_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_comment_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.COMMENTLISTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listComments -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async getCommentSettings(req, res){
    try {
      let comment_settings_res = await comment_service.getCommentSettings(
        req,
        res
      );
      if (comment_settings_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: comment_settings_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("CommentSettings -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async editCommentSettings(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await comment_service.editCommentSettings(req, res);

      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: [],
        message: message.SENDANNOUNCEMENT,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("editCommentSettings -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

};
