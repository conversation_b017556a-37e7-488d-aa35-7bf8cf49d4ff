/*
 * Summary:     common.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const staticPages_controller = require("../../controllers/app/staticPages.controller");

router.post("/terms-of-use", staticPages_controller.terms_of_use);
router.post("/about-us", staticPages_controller.about_us);
router.post("/help", staticPages_controller.help);
router.post("/privacy-policy", staticPages_controller.privacy_policy);

module.exports = router;