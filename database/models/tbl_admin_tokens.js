'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_admin_tokens extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  };
  tbl_admin_tokens.init({
    admin_token_id:{
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    admin_id: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    access_token: DataTypes.STRING
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_admin_tokens',
  });
  return tbl_admin_tokens;
};