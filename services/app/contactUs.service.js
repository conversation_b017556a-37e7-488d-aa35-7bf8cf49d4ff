const contactUs = require('../../database/models').tbl_contact_us;
const Mail = require('../../helper/sendmail');

module.exports = {
	async add(req, res) {
		let response = await contactUs.create(req.body);

		const mailBody = `<div> <p> First name: ${response.name} <p>
		<p> Email: ${response.email} <p>
		<p> Phone number: ${response.phone_number} <p>
		<p> Message: ${response.message} <p> </div>`;

		await Mail.sendmail(res, '<EMAIL>', 'Kyulebag - Inquiry', mailBody);

		return response;
	},
};
