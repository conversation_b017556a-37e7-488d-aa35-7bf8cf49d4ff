/**
 * KyuleBag Backend Scenario-Based Test Case Generator
 * 
 * This script generates comprehensive backend test case documentation in Excel format
 * that mirrors the frontend test case structure while covering complete request-response
 * lifecycles for all API endpoints and user workflows.
 * 
 * Features:
 * - Module-wise test case organization
 * - Unit, Integration, and End-to-End scenario categorization
 * - Complete request-response lifecycle coverage
 * - Edge case and error handling scenarios
 * - Code optimization recommendations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const XLSX = require('xlsx');
const fs = require('fs');

// Test Case Categories
const TEST_CATEGORIES = {
  UNIT: 'Unit Test',
  INTEGRATION: 'Integration Test', 
  SCENARIO: 'Scenario-Based Test',
  EDGE_CASE: 'Edge Case Test',
  SECURITY: 'Security Test',
  PERFORMANCE: 'Performance Test'
};

// Priority Levels
const PRIORITY = {
  CRITICAL: 'Critical',
  HIGH: 'High',
  MEDIUM: 'Medium',
  LOW: 'Low'
};

// Test Status
const STATUS = {
  NOT_STARTED: 'Not Started',
  IN_PROGRESS: 'In Progress',
  COMPLETED: 'Completed',
  BLOCKED: 'Blocked'
};

// API Modules
const MODULES = {
  AUTHENTICATION: 'Authentication & Authorization',
  USER_MANAGEMENT: 'User Management',
  ITEM_MANAGEMENT: 'Item Management',
  TAG_MANAGEMENT: 'Tag Management',
  NOTE_MANAGEMENT: 'Note Management',
  TRASH_MANAGEMENT: 'Trash Management',
  SUBSCRIPTION: 'Subscription Management',
  ADMIN_PANEL: 'Admin Panel',
  FILE_UPLOAD: 'File Upload & Media',
  NOTIFICATIONS: 'Notifications',
  ACTIVITY_LOGS: 'Activity Logs',
  WEBHOOKS: 'Webhooks & Integrations'
};

// Excel Column Headers (Aligned with Frontend Test Structure)
const HEADERS = [
  'Test Case ID',
  'Test Scenario',
  'Module/Feature',
  'Test Category',
  'Priority',
  'Test Description',
  'Pre-conditions',
  'Test Steps',
  'Input Data',
  'Expected Response',
  'Post-conditions',
  'Dependencies',
  'Tags',
  'Request-Response Lifecycle Stage',
  'HTTP Method',
  'API Endpoint',
  'Authentication Required',
  'Validation Rules',
  'Business Logic',
  'Database Operations',
  'External Services',
  'Error Handling',
  'Status',
  'Notes'
];

/**
 * Generate Authentication & Authorization Test Cases
 */
function generateAuthenticationTests() {
  const tests = [];
  let testId = 1;

  // User Registration Tests
  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Registration - KyuleBag Native Account',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify complete user registration flow for KyuleBag native account with email and password',
    'Pre-conditions': 'User does not exist in system, valid email format, strong password',
    'Test Steps': '1. Send POST request to /app-api/user/signup\n2. Validate input data\n3. Check user existence\n4. Hash password\n5. Create user record\n6. Generate JWT token\n7. Send verification email\n8. Create default tags\n9. Allocate AI credits',
    'Input Data': '{\n  "name": "John Doe",\n  "email": "<EMAIL>",\n  "password": "SecurePass123!",\n  "login_type": "KyuleBag",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Registration successful",\n  "data": {\n    "user_id": "user123",\n    "token": "jwt_token",\n    "email_verified": "0"\n  }\n}',
    'Post-conditions': 'User created in database, verification email sent, default tags created, JWT token generated',
    'Dependencies': 'Email service, Database, JWT service',
    'Tags': 'registration, authentication, email-verification',
    'Request-Response Lifecycle Stage': 'Complete Registration Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format, password strength, required fields',
    'Business Logic': 'User creation, password hashing, token generation, default setup',
    'Database Operations': 'INSERT user, INSERT token, INSERT default tags',
    'External Services': 'Email service, GCP storage',
    'Error Handling': 'Duplicate email, invalid input, service failures',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical path for user onboarding'
  });

  // User Login Tests
  tests.push({
    'Test Case ID': `AUTH-${testId++}`,
    'Test Scenario': 'User Login - Email and Password Authentication',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify user login with valid email and password credentials',
    'Pre-conditions': 'User exists in system, email verified, account active',
    'Test Steps': '1. Send POST request to /app-api/user/signin\n2. Validate input format\n3. Find user by email\n4. Verify password hash\n5. Check account status\n6. Generate JWT token\n7. Update device info\n8. Return user profile',
    'Input Data': '{\n  "email": "<EMAIL>",\n  "password": "SecurePass123!",\n  "device_token": "device123"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Login successful",\n  "data": {\n    "user_id": "user123",\n    "token": "jwt_token",\n    "profile": {...}\n  }\n}',
    'Post-conditions': 'User authenticated, JWT token issued, device info updated',
    'Dependencies': 'Database, JWT service, Password hashing',
    'Tags': 'login, authentication, password-verification',
    'Request-Response Lifecycle Stage': 'Authentication Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signin',
    'Authentication Required': 'No',
    'Validation Rules': 'Email format, password required',
    'Business Logic': 'Credential verification, session creation, profile loading',
    'Database Operations': 'SELECT user, UPDATE token, SELECT profile data',
    'External Services': 'None',
    'Error Handling': 'Invalid credentials, account locked, unverified email',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Core authentication functionality'
  });

  return tests;
}

/**
 * Generate Item Management Test Cases
 */
function generateItemManagementTests() {
  const tests = [];
  let testId = 1;

  // Item Creation Tests
  tests.push({
    'Test Case ID': `ITEM-${testId++}`,
    'Test Scenario': 'Create New Item with Media Upload',
    'Module/Feature': MODULES.ITEM_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.SCENARIO,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify complete item creation flow with media file upload and AI processing',
    'Pre-conditions': 'User authenticated, valid media file, sufficient storage quota',
    'Test Steps': '1. Send POST request to /app-api/items/add\n2. Validate authentication\n3. Process file upload\n4. Validate file type/size\n5. Upload to GCP storage\n6. Create item record\n7. Process AI/OCR labels\n8. Create activity log\n9. Return item details',
    'Input Data': '{\n  "title": "Important Document",\n  "description": "Tax document 2023",\n  "media_type": "image",\n  "file": "document.jpg",\n  "tags": ["tax", "2023"]\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Item created successfully",\n  "data": {\n    "item_id": "item123",\n    "media_url": "gcp_url",\n    "ai_labels": [...]\n  }\n}',
    'Post-conditions': 'Item created, file uploaded to GCP, AI labels processed, activity logged',
    'Dependencies': 'GCP Storage, AI/OCR services, Database',
    'Tags': 'item-creation, file-upload, ai-processing',
    'Request-Response Lifecycle Stage': 'Complete Item Creation Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/add',
    'Authentication Required': 'Yes (JWT)',
    'Validation Rules': 'File type, file size, required fields, storage quota',
    'Business Logic': 'File processing, AI analysis, storage management',
    'Database Operations': 'INSERT item, INSERT media, INSERT labels, INSERT activity',
    'External Services': 'GCP Storage, Google Vision AI, Google Video Intelligence',
    'Error Handling': 'Storage quota exceeded, invalid file type, AI service failures',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Core content creation functionality'
  });

  return tests;
}

/**
 * Generate Tag Management Test Cases
 */
function generateTagManagementTests() {
  const tests = [];
  let testId = 1;

  tests.push({
    'Test Case ID': `TAG-${testId++}`,
    'Test Scenario': 'Create New Tag for User',
    'Module/Feature': MODULES.TAG_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.UNIT,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify tag creation functionality with validation and duplicate checking',
    'Pre-conditions': 'User authenticated, tag name not exists for user',
    'Test Steps': '1. Send POST to /app-api/tags/add\n2. Validate authentication\n3. Check duplicate tag\n4. Create tag record\n5. Return success response',
    'Input Data': '{\n  "tag_name": "Important Documents",\n  "color": "#FF5733"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Tag created successfully",\n  "data": {"tag_id": "tag123"}\n}',
    'Post-conditions': 'Tag created in database for user',
    'Dependencies': 'Database',
    'Tags': 'tag-creation, user-organization',
    'Request-Response Lifecycle Stage': 'Tag Creation Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/tags/add',
    'Authentication Required': 'Yes (JWT)',
    'Validation Rules': 'Tag name required, color format, length limits',
    'Business Logic': 'Duplicate checking, user-specific tags',
    'Database Operations': 'INSERT tag, SELECT for duplicates',
    'External Services': 'None',
    'Error Handling': 'Duplicate tag name, invalid input',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'User organization feature'
  });

  return tests;
}

/**
 * Generate Admin Panel Test Cases
 */
function generateAdminPanelTests() {
  const tests = [];
  let testId = 1;

  tests.push({
    'Test Case ID': `ADMIN-${testId++}`,
    'Test Scenario': 'Admin Login Authentication',
    'Module/Feature': MODULES.ADMIN_PANEL,
    'Test Category': TEST_CATEGORIES.SECURITY,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify admin authentication with role-based access control',
    'Pre-conditions': 'Admin account exists, correct credentials provided',
    'Test Steps': '1. Send POST to /cms-api/admin/login\n2. Validate credentials\n3. Check admin role\n4. Generate admin token\n5. Return admin profile',
    'Input Data': '{\n  "email": "<EMAIL>",\n  "password": "AdminPass123!"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Admin login successful",\n  "data": {"admin_id": "admin123", "token": "admin_jwt", "role": "super_admin"}\n}',
    'Post-conditions': 'Admin authenticated with appropriate permissions',
    'Dependencies': 'Admin database, JWT service',
    'Tags': 'admin-auth, security, role-based-access',
    'Request-Response Lifecycle Stage': 'Admin Authentication Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/cms-api/admin/login',
    'Authentication Required': 'No (login endpoint)',
    'Validation Rules': 'Email format, password required, admin role verification',
    'Business Logic': 'Admin credential verification, role assignment',
    'Database Operations': 'SELECT admin, UPDATE login timestamp',
    'External Services': 'None',
    'Error Handling': 'Invalid credentials, insufficient permissions',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical security component'
  });

  return tests;
}

/**
 * Generate Subscription Management Test Cases
 */
function generateSubscriptionTests() {
  const tests = [];
  let testId = 1;

  tests.push({
    'Test Case ID': `SUB-${testId++}`,
    'Test Scenario': 'User Storage Subscription Upgrade',
    'Module/Feature': MODULES.SUBSCRIPTION,
    'Test Category': TEST_CATEGORIES.INTEGRATION,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify storage subscription upgrade with payment processing and quota allocation',
    'Pre-conditions': 'User authenticated, valid payment method, subscription plan available',
    'Test Steps': '1. Send POST to /app-api/user/storage-subscription-plan\n2. Validate user and plan\n3. Process payment\n4. Update user storage quota\n5. Create subscription record\n6. Send confirmation',
    'Input Data': '{\n  "plan_id": "storage_premium",\n  "payment_method": "stripe_token_123"\n}',
    'Expected Response': '{\n  "status": 1,\n  "message": "Subscription upgraded successfully",\n  "data": {"new_storage_limit": "100GB", "subscription_id": "sub123"}\n}',
    'Post-conditions': 'User storage quota increased, payment processed, subscription active',
    'Dependencies': 'Payment gateway, Database, Email service',
    'Tags': 'subscription, payment, storage-upgrade',
    'Request-Response Lifecycle Stage': 'Subscription Upgrade Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/storage-subscription-plan',
    'Authentication Required': 'Yes (JWT)',
    'Validation Rules': 'Valid plan ID, payment method validation',
    'Business Logic': 'Payment processing, quota calculation, subscription management',
    'Database Operations': 'UPDATE user storage, INSERT subscription, INSERT payment record',
    'External Services': 'Stripe/Payment gateway, Email service',
    'Error Handling': 'Payment failures, invalid plans, quota errors',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Revenue-critical functionality'
  });

  return tests;
}

/**
 * Generate Edge Case and Error Handling Test Cases
 */
function generateEdgeCaseTests() {
  const tests = [];
  let testId = 1;

  // Authentication Edge Cases
  tests.push({
    'Test Case ID': `EDGE-${testId++}`,
    'Test Scenario': 'Registration with Duplicate Email',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.EDGE_CASE,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system handles duplicate email registration gracefully',
    'Pre-conditions': 'User with email already exists in system',
    'Test Steps': '1. Send POST to /app-api/user/signup with existing email\n2. Check user existence\n3. Return appropriate error',
    'Input Data': '{\n  "name": "Jane Doe",\n  "email": "<EMAIL>",\n  "password": "NewPass123!"\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Email already registered",\n  "data": {}\n}',
    'Post-conditions': 'No new user created, appropriate error returned',
    'Dependencies': 'Database',
    'Tags': 'edge-case, duplicate-email, error-handling',
    'Request-Response Lifecycle Stage': 'Input Validation Stage',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/user/signup',
    'Authentication Required': 'No',
    'Validation Rules': 'Email uniqueness check',
    'Business Logic': 'Duplicate prevention logic',
    'Database Operations': 'SELECT for existence check',
    'External Services': 'None',
    'Error Handling': 'Duplicate email error response',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical for data integrity'
  });

  // File Upload Edge Cases
  tests.push({
    'Test Case ID': `EDGE-${testId++}`,
    'Test Scenario': 'File Upload Exceeding Storage Quota',
    'Module/Feature': MODULES.FILE_UPLOAD,
    'Test Category': TEST_CATEGORIES.EDGE_CASE,
    'Priority': PRIORITY.HIGH,
    'Test Description': 'Verify system prevents file upload when user storage quota exceeded',
    'Pre-conditions': 'User at 99% storage capacity, file size would exceed quota',
    'Test Steps': '1. Send POST to /app-api/items/add with large file\n2. Check current storage usage\n3. Calculate new total\n4. Reject if exceeds quota',
    'Input Data': '{\n  "title": "Large Document",\n  "file": "large_file.pdf" (10MB)\n}',
    'Expected Response': '{\n  "status": 0,\n  "message": "Storage quota exceeded",\n  "data": {"current_usage": "95%", "available": "500MB"}\n}',
    'Post-conditions': 'File not uploaded, storage quota enforced',
    'Dependencies': 'Storage calculation service, Database',
    'Tags': 'edge-case, storage-quota, file-upload',
    'Request-Response Lifecycle Stage': 'Pre-upload Validation',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/add',
    'Authentication Required': 'Yes (JWT)',
    'Validation Rules': 'Storage quota validation, file size limits',
    'Business Logic': 'Storage quota enforcement',
    'Database Operations': 'SELECT user storage usage',
    'External Services': 'None',
    'Error Handling': 'Storage quota exceeded error',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Prevents storage abuse'
  });

  return tests;
}

/**
 * Generate Performance Test Cases
 */
function generatePerformanceTests() {
  const tests = [];
  let testId = 1;

  tests.push({
    'Test Case ID': `PERF-${testId++}`,
    'Test Scenario': 'Bulk Item Upload Performance',
    'Module/Feature': MODULES.ITEM_MANAGEMENT,
    'Test Category': TEST_CATEGORIES.PERFORMANCE,
    'Priority': PRIORITY.MEDIUM,
    'Test Description': 'Verify system performance when uploading multiple items simultaneously',
    'Pre-conditions': 'User authenticated, multiple files ready for upload',
    'Test Steps': '1. Send POST to /app-api/items/bulkAdd with 50 items\n2. Monitor response time\n3. Check memory usage\n4. Verify all items processed',
    'Input Data': 'Array of 50 items with media files',
    'Expected Response': 'All items processed within 30 seconds, memory usage stable',
    'Post-conditions': 'All items uploaded successfully, system performance maintained',
    'Dependencies': 'GCP Storage, Database, AI services',
    'Tags': 'performance, bulk-upload, scalability',
    'Request-Response Lifecycle Stage': 'Bulk Processing Flow',
    'HTTP Method': 'POST',
    'API Endpoint': '/app-api/items/bulkAdd',
    'Authentication Required': 'Yes (JWT)',
    'Validation Rules': 'Bulk upload limits, file validations',
    'Business Logic': 'Batch processing, queue management',
    'Database Operations': 'Bulk INSERT operations',
    'External Services': 'GCP Storage, AI processing services',
    'Error Handling': 'Partial failure handling, rollback mechanisms',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical for user experience'
  });

  return tests;
}

/**
 * Generate Security Test Cases
 */
function generateSecurityTests() {
  const tests = [];
  let testId = 1;

  tests.push({
    'Test Case ID': `SEC-${testId++}`,
    'Test Scenario': 'JWT Token Manipulation Attack',
    'Module/Feature': MODULES.AUTHENTICATION,
    'Test Category': TEST_CATEGORIES.SECURITY,
    'Priority': PRIORITY.CRITICAL,
    'Test Description': 'Verify system rejects manipulated or expired JWT tokens',
    'Pre-conditions': 'Valid JWT token available for manipulation',
    'Test Steps': '1. Modify JWT token payload\n2. Send request with manipulated token\n3. Verify token validation fails\n4. Return unauthorized error',
    'Input Data': 'Modified JWT token in Authorization header',
    'Expected Response': '{\n  "status": 0,\n  "message": "Invalid or expired token",\n  "data": {}\n}',
    'Post-conditions': 'Request rejected, no unauthorized access granted',
    'Dependencies': 'JWT verification service',
    'Tags': 'security, jwt-validation, token-manipulation',
    'Request-Response Lifecycle Stage': 'Authentication Middleware',
    'HTTP Method': 'Any authenticated endpoint',
    'API Endpoint': 'Any protected endpoint',
    'Authentication Required': 'Yes (Invalid JWT)',
    'Validation Rules': 'JWT signature validation, expiry check',
    'Business Logic': 'Token security validation',
    'Database Operations': 'None (rejected at middleware)',
    'External Services': 'None',
    'Error Handling': 'Unauthorized access prevention',
    'Status': STATUS.NOT_STARTED,
    'Notes': 'Critical security test'
  });

  return tests;
}

/**
 * Generate comprehensive test suite
 */
function generateAllTestCases() {
  const allTests = [
    ...generateAuthenticationTests(),
    ...generateItemManagementTests(),
    ...generateTagManagementTests(),
    ...generateAdminPanelTests(),
    ...generateSubscriptionTests(),
    ...generateEdgeCaseTests(),
    ...generatePerformanceTests(),
    ...generateSecurityTests()
  ];

  return allTests;
}

/**
 * Create Excel workbook with test cases
 */
function createExcelWorkbook() {
  const testCases = generateAllTestCases();
  
  // Group tests by module
  const moduleGroups = {};
  testCases.forEach(test => {
    const module = test['Module/Feature'];
    if (!moduleGroups[module]) {
      moduleGroups[module] = [];
    }
    moduleGroups[module].push(test);
  });

  const worksheets = [];

  // Create comprehensive summary sheet
  const summaryData = [
    ['KyuleBag Backend Test Case Summary', '', '', '', '', '', ''],
    ['Generated on:', new Date().toISOString().split('T')[0], '', '', '', '', ''],
    ['Total Test Cases:', testCases.length, '', '', '', '', ''],
    ['', '', '', '', '', '', ''],
    ['Module', 'Total Tests', 'Unit Tests', 'Integration Tests', 'Scenario Tests', 'Edge Cases', 'Security Tests'],
    ...Object.keys(moduleGroups).map(module => {
      const tests = moduleGroups[module];
      return [
        module,
        tests.length,
        tests.filter(t => t['Test Category'] === TEST_CATEGORIES.UNIT).length,
        tests.filter(t => t['Test Category'] === TEST_CATEGORIES.INTEGRATION).length,
        tests.filter(t => t['Test Category'] === TEST_CATEGORIES.SCENARIO).length,
        tests.filter(t => t['Test Category'] === TEST_CATEGORIES.EDGE_CASE).length,
        tests.filter(t => t['Test Category'] === TEST_CATEGORIES.SECURITY).length
      ];
    }),
    ['', '', '', '', '', '', ''],
    ['Priority Distribution', '', '', '', '', '', ''],
    ['Critical', testCases.filter(t => t['Priority'] === PRIORITY.CRITICAL).length, '', '', '', '', ''],
    ['High', testCases.filter(t => t['Priority'] === PRIORITY.HIGH).length, '', '', '', '', ''],
    ['Medium', testCases.filter(t => t['Priority'] === PRIORITY.MEDIUM).length, '', '', '', '', ''],
    ['Low', testCases.filter(t => t['Priority'] === PRIORITY.LOW).length, '', '', '', '', ''],
    ['', '', '', '', '', '', ''],
    ['API Coverage', '', '', '', '', '', ''],
    ['App API Endpoints', testCases.filter(t => t['API Endpoint'] && t['API Endpoint'].includes('/app-api/')).length, '', '', '', '', ''],
    ['CMS API Endpoints', testCases.filter(t => t['API Endpoint'] && t['API Endpoint'].includes('/cms-api/')).length, '', '', '', '', '']
  ];

  worksheets.push({
    name: 'Test Summary',
    data: summaryData
  });

  // Create API Endpoint Mapping Sheet
  const apiEndpoints = [
    ['API Endpoint Mapping', '', '', '', ''],
    ['Endpoint', 'HTTP Method', 'Authentication', 'Module', 'Test Coverage'],
    ['', '', '', '', ''],
    // App API Endpoints
    ['/app-api/user/signup', 'POST', 'No', 'Authentication', 'Yes'],
    ['/app-api/user/signin', 'POST', 'No', 'Authentication', 'Yes'],
    ['/app-api/user/verify-otp', 'POST', 'Yes', 'Authentication', 'Planned'],
    ['/app-api/user/forgot-password', 'POST', 'No', 'Authentication', 'Planned'],
    ['/app-api/user/reset-password', 'POST', 'No', 'Authentication', 'Planned'],
    ['/app-api/user/edit-profile', 'PUT', 'Yes', 'User Management', 'Planned'],
    ['/app-api/user/change-password', 'PUT', 'Yes', 'User Management', 'Planned'],
    ['/app-api/items/add', 'POST', 'Yes', 'Item Management', 'Yes'],
    ['/app-api/items/list', 'POST', 'Yes', 'Item Management', 'Planned'],
    ['/app-api/items/edit', 'PUT', 'Yes', 'Item Management', 'Planned'],
    ['/app-api/items/delete', 'POST', 'Yes', 'Item Management', 'Planned'],
    ['/app-api/items/bulkAdd', 'POST', 'Yes', 'Item Management', 'Yes'],
    ['/app-api/tags/add', 'POST', 'Yes', 'Tag Management', 'Yes'],
    ['/app-api/tags/list', 'POST', 'Yes', 'Tag Management', 'Planned'],
    ['/app-api/notes/add', 'POST', 'Yes', 'Note Management', 'Planned'],
    ['/app-api/notes/edit', 'PUT', 'Yes', 'Note Management', 'Planned'],
    ['/app-api/trash/list', 'POST', 'Yes', 'Trash Management', 'Planned'],
    ['', '', '', '', ''],
    // CMS API Endpoints
    ['/cms-api/admin/login', 'POST', 'No', 'Admin Panel', 'Yes'],
    ['/cms-api/user/list', 'POST', 'Yes', 'Admin Panel', 'Planned'],
    ['/cms-api/dashboard/stats', 'GET', 'Yes', 'Admin Panel', 'Planned'],
    ['/cms-api/subscription/list', 'POST', 'Yes', 'Subscription', 'Planned'],
    ['/cms-api/ai-subscription/add', 'POST', 'Yes', 'AI Subscription', 'Planned']
  ];

  worksheets.push({
    name: 'API Endpoints',
    data: apiEndpoints
  });

  // Create Test Execution Guide
  const executionGuide = [
    ['Backend Test Execution Guide', '', ''],
    ['', '', ''],
    ['Prerequisites', '', ''],
    ['1. Node.js environment setup', '', ''],
    ['2. Jest testing framework installed', '', ''],
    ['3. Test database configured', '', ''],
    ['4. Environment variables set', '', ''],
    ['5. Mock services configured', '', ''],
    ['', '', ''],
    ['Test Execution Commands', '', ''],
    ['Run all tests:', 'npm test', ''],
    ['Run specific module:', 'npm test -- --testNamePattern="Authentication"', ''],
    ['Run with coverage:', 'npm run test:coverage', ''],
    ['Run integration tests:', 'npm run test:integration', ''],
    ['', '', ''],
    ['Test Data Management', '', ''],
    ['- Use test fixtures for consistent data', '', ''],
    ['- Clean database before each test suite', '', ''],
    ['- Mock external services (GCP, Email)', '', ''],
    ['- Use separate test database', '', ''],
    ['', '', ''],
    ['Validation Checklist', '', ''],
    ['✓ All endpoints tested', '', ''],
    ['✓ Authentication scenarios covered', '', ''],
    ['✓ Error handling validated', '', ''],
    ['✓ Edge cases included', '', ''],
    ['✓ Performance benchmarks met', '', ''],
    ['✓ Security vulnerabilities checked', '', '']
  ];

  worksheets.push({
    name: 'Execution Guide',
    data: executionGuide
  });

  // Create individual module sheets
  Object.keys(moduleGroups).forEach(module => {
    const moduleTests = moduleGroups[module];
    const sheetData = [
      HEADERS,
      ...moduleTests.map(test => HEADERS.map(header => test[header] || ''))
    ];

    worksheets.push({
      name: module.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 31), // Excel sheet name limits
      data: sheetData
    });
  });

  return worksheets;
}

// Generate and save Excel file
try {
  const worksheets = createExcelWorkbook();

  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Add each worksheet to the workbook
  worksheets.forEach(sheet => {
    const worksheet = XLSX.utils.aoa_to_sheet(sheet.data);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
  });

  // Write the workbook to file
  XLSX.writeFile(workbook, 'Backend_Scenario_Test_Cases.xlsx');

  console.log('✅ Backend test case documentation generated successfully!');
  console.log('📄 File: Backend_Scenario_Test_Cases.xlsx');
  console.log('📊 Total test cases:', generateAllTestCases().length);
  console.log('📋 Sheets created:');
  worksheets.forEach(sheet => {
    console.log(`   - ${sheet.name} (${sheet.data.length} rows)`);
  });

} catch (error) {
  console.error('❌ Error generating test cases:', error);
  console.error('Stack trace:', error.stack);
}

module.exports = {
  generateAuthenticationTests,
  generateItemManagementTests,
  createExcelWorkbook,
  TEST_CATEGORIES,
  PRIORITY,
  MODULES,
  HEADERS
};
