/*
 * Summary:     staticPages.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const staticPages_service = require("../../services/app/staticPages.service");

module.exports = {

  /* terms_of_use */
  async terms_of_use(req, res) {
    try {
      let data = await staticPages_service.terms_of_use(req, res);
      if (data) {
        res.status(status.SUCCESSSTATUS).send({
          data: data,
          message: message.CONTENTRETRIVE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* about_us */
  async about_us(req, res) {
    try {
      let data = await staticPages_service.about_us(req, res);
      if (data) {
        res.status(status.SUCCESSSTATUS).send({
          data: data,
          message: message.CONTENTRETRIVE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* help */
  async help(req, res) {
    try {
      let data = await staticPages_service.help(req, res);
      if (data) {
        res.status(status.SUCCESSSTATUS).send({
          data: data,
          message: message.CONTENTRETRIVE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* privacy-policy */
  async privacy_policy(req, res) {
    try {
      let data = await staticPages_service.privacy_policy(req, res);
      if (data) {
        res.status(status.SUCCESSSTATUS).send({
          data: data,
          message: message.CONTENTRETRIVE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

}  