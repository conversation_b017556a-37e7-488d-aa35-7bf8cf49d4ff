'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    return Promise.all([
      queryInterface.addColumn("tbl_items", "uploadedAt", {
        type: Sequelize.DATE,
      }),
      queryInterface.sequelize.query(`
      UPDATE tbl_items
      SET uploadedAt = createdAt
    `),
      queryInterface.changeColumn("tbl_items", "uploadedAt", {
        allowNull: false,
        type: Sequelize.DATE,
        })
    ]);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
