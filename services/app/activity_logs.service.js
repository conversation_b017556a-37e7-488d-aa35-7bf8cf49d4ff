const Activity_logs = require("../../database/models").tbl_activity_logs;
const constant = require("../../config/constant");
const Sequelize = require("sequelize");

module.exports = {
  /* list */
  async list(req, res) {
    let { page, sort_by, order } = req.body;
    page = page === undefined ? 1 : page;
    const offset = (page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;
    sort_by = sort_by ? sort_by === "created_at" ? "createdAt" : sort_by : "created_at";
    order = order === undefined || order === "" ? "DESC" : order;

    var activity_logs = await Activity_logs.findAndCountAll({
      where: {
        user_id: req.user_id,
      },
      attributes: { include: ["title", "createdAt", "updatedAt"] },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      offset: offset,
      limit: limit,
    });
    if (activity_logs.count === 0) {
      return 0;
    } else {
      activity_logs.rows.map((data) => {
        data.dataValues.createdAt = data.dataValues.createdAt
          .toISOString()
          .substr(0, 19)
          .replace("T", " ");
        data.dataValues.updatedAt = data.dataValues.updatedAt
          .toISOString()
          .substr(0, 19)
          .replace("T", " ");
      });
      return activity_logs;
    }
  },
};
