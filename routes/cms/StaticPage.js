var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

/* require for Controller */
const staticPage_controller = require("../../controllers/cms/staticPage.controller");

/*require for validators */
const validators = require('../../assets/validators');

router.post("/add", authentication, validators.cms.staticContent.addStaticPage, staticPage_controller.addStaticPage);
router.put("/edit/:id", authentication, validators.cms.staticContent.updateStaticPage, staticPage_controller.editStaticPage);
router.post("/list", authentication, validators.cms.staticContent.listStaticPage, staticPage_controller.listStaticPage);
router.post("/retrive/:id", authentication, staticPage_controller.retriveStaticPage);

module.exports = router;