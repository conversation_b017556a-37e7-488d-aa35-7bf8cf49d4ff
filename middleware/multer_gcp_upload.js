/**
 * Google Cloud Storage Upload Middleware - File Upload Handler
 *
 * This middleware module handles file uploads to Google Cloud Storage (GCS) for the
 * KyuleBag platform. It provides secure, scalable file storage for user content
 * including images, videos, documents, and other media files.
 *
 * Business Logic Connection:
 * - Enables core content storage functionality for the KyuleBag platform
 * - Supports multi-format file uploads (images, videos, documents, audio)
 * - Integrates with content management system for media organization
 * - Provides scalable cloud storage for growing content libraries
 * - Supports both mobile app and web client file upload workflows
 * - Enables content sharing and collaboration through cloud accessibility
 *
 * Key Features:
 * - Direct upload to Google Cloud Storage buckets
 * - Configurable destination file paths for organization
 * - Asynchronous upload processing with Promise-based API
 * - Comprehensive error handling with detailed error messages
 * - Integration with GCP service account authentication
 * - Support for large file uploads and streaming
 *
 * Cloud Storage Integration:
 * - Uses Google Cloud Storage SDK (@google-cloud/storage)
 * - Authenticates using service account credentials
 * - Supports organized folder structure within buckets
 * - Provides secure, encrypted storage with GCP infrastructure
 * - Enables global content delivery through GCP CDN integration
 *
 * Upload Process:
 * 1. Accept file object with path and metadata
 * 2. Initialize GCS bucket and file references
 * 3. Create write stream to destination path in bucket
 * 4. Stream file data to cloud storage
 * 5. Handle upload completion or error events
 * 6. Return Promise resolving on success or rejecting on error
 *
 * Error Handling:
 * - Network connectivity issues during upload
 * - Authentication/authorization failures with GCP
 * - Storage quota exceeded scenarios
 * - File corruption or invalid file formats
 * - Timeout handling for large file uploads
 *
 * Security Features:
 * - Service account authentication for secure access
 * - Controlled bucket access with proper IAM policies
 * - File path validation to prevent directory traversal
 * - Secure credential management through environment variables
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

/**
 * Dependencies and External Modules
 * Import required modules for Google Cloud Storage integration
 */

// Google Cloud Storage SDK for file upload operations
const { Storage } = require('@google-cloud/storage');

// Application constants including GCP configuration
const CONSTANT = require('../config/constant');

/**
 * Google Cloud Storage Configuration
 *
 * Initialize the Google Cloud Storage client with project credentials
 * and configuration. Uses service account key file for authentication.
 */
const storage = new Storage({
  projectId: CONSTANT.GCP_PROJECT_ID, // GCP project identifier
  credentials: require('../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json'), // Service account credentials
});

// Target storage bucket name from configuration
const bucketName = CONSTANT.GCP_BUCKET_NAME;

/**
 * GCP File Upload Function
 *
 * Uploads a file to Google Cloud Storage bucket with specified destination path.
 * Provides asynchronous file upload with comprehensive error handling and
 * progress tracking capabilities.
 *
 * @param {Object} files - File object containing file data and metadata
 * @param {string} files.path - Local file path or file buffer for upload
 * @param {string} files.originalname - Original filename from client
 * @param {string} files.mimetype - MIME type of the file
 * @param {number} files.size - File size in bytes
 *
 * @param {string} destinationFilePath - Target path within GCS bucket
 *                                      Example: "user/123/items/image.jpg"
 *
 * @returns {Promise<void>} Promise that resolves when upload completes successfully
 *                          or rejects with error details on failure
 *
 * @throws {Error} Upload errors including network, authentication, or storage issues
 *
 * Usage Example:
 * ```javascript
 * const uploadResult = await gcpUpload(fileObject, 'user/123/profile.jpg');
 * console.log('File uploaded successfully');
 * ```
 *
 * Error Scenarios Handled:
 * - Network connectivity issues during upload
 * - Invalid file paths or bucket configurations
 * - Authentication failures with GCP services
 * - Storage quota exceeded conditions
 * - File corruption during transfer
 * - Timeout conditions for large files
 */
function gcpUpload(files, destinationFilePath) {
  return new Promise((resolve, reject) => {
    /**
     * Google Cloud Storage References
     *
     * Create references to the target bucket and destination file
     * for the upload operation. These references provide the API
     * interface for interacting with GCS resources.
     */
    const bucket = storage.bucket(bucketName); // Target storage bucket
    const file = bucket.file(destinationFilePath); // Destination file reference

    /**
     * Alternative Upload Method (Currently Used)
     *
     * Direct file upload from local path to GCS bucket.
     * This method handles the entire upload process including
     * file reading, streaming, and error handling.
     */
    storage.bucket(bucketName).upload(files.path, {
      destination: destinationFilePath, // Target path in bucket
      // Additional upload options can be specified here:
      // - metadata: Custom file metadata
      // - predefinedAcl: Access control settings
      // - resumable: Enable resumable uploads for large files
      // - validation: Enable data integrity checks
    });

    // Note: Alternative direct upload method (commented out)
    // storage.bucket(bucketName).upload(files.path);

    /**
     * Stream-based Upload Process
     *
     * Create a write stream to the destination file in GCS bucket.
     * This approach provides more control over the upload process
     * and better error handling capabilities.
     */
    file
      .createWriteStream()
      /**
       * Upload Success Handler
       *
       * Called when the file upload completes successfully.
       * Resolves the Promise to indicate successful upload.
       */
      .on('finish', () => {
        console.log(`✅ File uploaded successfully to: ${destinationFilePath}`);
        console.log(`📂 Bucket: ${bucketName}`);
        resolve(); // Resolve Promise on successful upload
      })

      /**
       * Upload Error Handler
       *
       * Called when an error occurs during the upload process.
       * Provides detailed error information for debugging and
       * user feedback purposes.
       */
      .on('error', (error) => {
        // Log detailed error information for debugging
        console.log('❌ GCP Upload Error:', error);
        console.log(`📁 Failed upload path: ${destinationFilePath}`);
        console.log(`🪣 Target bucket: ${bucketName}`);

        // Reject Promise with comprehensive error details
        reject({
          message: 'Could not upload file, something went wrong',
          err: error,
          bucket: bucketName,
          destinationPath: destinationFilePath,
          timestamp: new Date().toISOString(),
        });
      })
      .end(); // End the write stream to complete the upload
  });
}

/**
 * Export Upload Function
 *
 * Make the GCP upload function available for use throughout the application
 * in controllers, services, and other middleware that handle file operations.
 */
module.exports = gcpUpload;

/**
 * Additional Features for Future Enhancement:
 *
 * 1. Upload Progress Tracking: Implement progress callbacks for large files
 * 2. Resumable Uploads: Enable resumable uploads for improved reliability
 * 3. File Validation: Add file type and size validation before upload
 * 4. Thumbnail Generation: Automatically generate thumbnails for images
 * 5. Metadata Management: Store custom metadata with uploaded files
 * 6. Batch Upload Support: Enable multiple file uploads in single operation
 * 7. Upload Queue Management: Implement queue system for high-volume uploads
 * 8. CDN Integration: Configure CloudFront or similar CDN for content delivery
 * 9. Compression: Implement automatic compression for applicable file types
 * 10. Virus Scanning: Integrate virus scanning before final storage
 *
 * Current Implementation Strengths:
 * - Promise-based API for easy async/await usage
 * - Comprehensive error handling with detailed error information
 * - Integration with GCP service account authentication
 * - Configurable destination paths for organized storage
 * - Stream-based uploads for memory efficiency
 * - Proper error logging for debugging and monitoring
 */
