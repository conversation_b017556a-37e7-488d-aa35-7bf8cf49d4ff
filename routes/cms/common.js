/** NPM-modules */
const express = require("express");
const router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;
const multer_middleware = require('../../middleware/multer');

/* require for Validators */
const validators = require('../../assets/validators');

/* require for  Controller*/
const common_controller = require('../../controllers/cms/common.controller');



//  /* routes for admin panel */
router.post("/login", validators.cms.admin.login, common_controller.logIn); /* login API */
router.delete("/logout",common_controller.logOut); /* logout API */
router.post("/forgot-password", validators.cms.admin.forgot_password, common_controller.forgotPassword); /* Forgot password API */
router.post("/reset-password", validators.cms.admin.reset_password, common_controller.resetPassword); /* Reset password API */
router.put("/edit-profile", authentication, multer_middleware.singleProfilePic, validators.cms.admin.edit_profile, common_controller.updateProfile ) /* edit-profaile API */
router.put("/change-password", authentication, validators.cms.admin.change_password, common_controller.changePassword) /* Change password API */

// routes for admin list
// router.post("/list",admin_controller.listAdmin); /** admin list API */
// router.post("/sub-admins",admin_controller.listSubAdmin); /** sub admins list API */
// router.post("/add", /**authentication,*/ /*validators.cms.admin.addAdmin,*/ admin_controller.addAdmin); /** add admin API */
// router.post("/retrive/:id", admin_controller.retriveAdmin); 
// router.put("/edit/:id", admin_controller.editAdmin ); /** edit admin */
// router.put("/delete/:id", admin_controller.deleteAdmin); /** delete admin */

// router.post("/add-role", admin_controller.addNewRole); /** add role */
// router.put("/edit-role/:id", admin_controller.editRole); /** edit role */

module.exports = router;
