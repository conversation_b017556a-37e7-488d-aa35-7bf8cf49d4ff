'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    return Promise.all([
      queryInterface.changeColumn("tbl_users", "is_web_link_preview_set", {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      }),
      queryInterface.removeIndex('tbl_users', 'email'),
      queryInterface.removeIndex('tbl_users', 'phone'),
      queryInterface.changeColumn("tbl_users", "email", {
        unique: false,
        type: Sequelize.STRING,
      }),
      queryInterface.changeColumn("tbl_users", "phone", {
        unique: false,
        type: Sequelize.STRING,
      }),
    ])
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
