require("dotenv").config();
const Announcement = require("../../database/models").tbl_announcements;
const CommentSettings = require("../../database/models").tbl_comment_settings;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");

module.exports = {
  async listAnnouncement(req, res) {
    let { sort_by, search, order } = req.body;
    sort_by = sort_by
      ? sort_by === "created_at"
        ? "createdAt"
        : sort_by
      : "created_at";
    search = search === undefined ? "" : search;
    let searchObj = {
      where: {
        [Op.and]: [
          {
            [Op.or]: [
              {
                is_announcement: true,
              },
              {
                user_id: req.user_id,
              },
            ],
          },
          {
            isDeleted: false,
          },
        ],
      },
    };

    let limit = req.query.limit ? Number(req.query.limit) : constant.LIMIT;
    const offset = (req.body.page - 1) * limit;

    let announcement_list = await Announcement.findAndCountAll({
      where: searchObj.where,
      attributes: {
        exclude: ["isDeleted"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });

    // Adding comment setting
    let commentSetting = await CommentSettings.findOne({
      where: {
        commentSettingId: 1,
      },
      attributes: ["isActive", "characterLimit", "commentLimit"],
      raw: true,
    });

    announcement_list.commentSetting = commentSetting;

    return announcement_list;
  },

  async validateWithCommentSetting(req, flag) {
    const commentSetting = await CommentSettings.findOne({
      where: {
        commentSettingId: 1,
      },
      attributes: ["isActive", "characterLimit", "commentLimit"],
      raw: true,
    });

    const userCommentCount = await Announcement.count({
      where: {
        user_id: req.user_id,
      },
    });

    if (commentSetting.isActive === 1) {
      if (
        req.body.content &&
        req.body.content.length > commentSetting.characterLimit
      ) {
        return { isValid: 1 };
      }
      return { isValid: 4 };
    } else {
      return { isValid: 3 };
    }
  },

  async addComment(req) {
    const { content } = req.body;
    const comment = {
      user_id: req.user_id,
      content: content,
    };

    const createdComment = await Announcement.create(comment);
    const createdCommentDataToSend = {
      announcement_id: createdComment.announcement_id,
      content: createdComment.content,
      is_announcement: createdComment.is_announcement,
      createdAt: createdComment.createdAt,
      updatedAt: createdComment.updatedAt,
    };

    return createdCommentDataToSend;
  },

  async editComment(req) {
    const { content, announcement_id } = req.body;
    const where = {
      [Op.and]: [
        {
          announcement_id: announcement_id,
        },
        {
          user_id: req.user_id,
        },
      ],
    };

    try {
      await Announcement.update(
        {
          content: content,
        },
        {
          where: where,
        }
      );

      const comment = await Announcement.findOne(
        { where: where },
        { raw: true }
      );

      return {
        announcement_id: comment.announcement_id,
        content: comment.content,
        is_announcement: comment.is_announcement,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
      };
    } catch (error) {
      throw error;
    }
  },
};
