/*
 * Summary:     Index.js file for handling all routes, request and response for admin panel - (app related actions).
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
var express = require("express");
var router = express.Router();

/* require for Authentication */
const commonRouter = require("../app/common");
const staticRouter = require("../app/staticPages");
const tagsRouter = require("../app/tags");
const itemsRouter = require("../app/items");
const notesRouter = require("../app/notes");
const trashRouter = require("../app/trash");
const activityLogsRouter = require("../app/activity_logs");
const announcementRouter = require("../app/announcement");
const contactUsRouter = require('../app/contactUs')
const webhookRouter = require('../app/webhook')

// /* routes of app API*/
router.use("/user", commonRouter);
router.use("/static", staticRouter);
router.use("/tags", tagsRouter);
router.use("/items", itemsRouter);
router.use("/notes", notesRouter);
router.use("/trash", trashRouter);
router.use("/activity-logs", activityLogsRouter);
router.use("/announcements", announcementRouter);
router.use("/contact-us", contactUsRouter)
router.use("/webhook", webhookRouter)

module.exports = router;
