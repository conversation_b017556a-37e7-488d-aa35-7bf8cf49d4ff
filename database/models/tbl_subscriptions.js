'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_subscriptions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      tbl_subscriptions.hasMany(models.tbl_user_subscriptions, {
        targetKey: "subscription_id",
        foreignKey: "subscription_id",
        // as: "subscription_plan_details",
      });
    }
  }
  tbl_subscriptions.init({
    subscription_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    subscriptions_product_id: {
      type: DataTypes.TEXT
    },
    storage: DataTypes.STRING, 
    price: DataTypes.STRING,
    duration: {
      type: DataTypes.ENUM("monthly", "yearly"),
    },
    status: {
      type: DataTypes.ENUM("inactive", "active"),
      defaultValue: "active",
    },
    is_deleted: {
      allowNull: false, 
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
    offer_id_token: {
      type: DataTypes.TEXT
    },
    base_plan_id: {
      type: DataTypes.TEXT
    },
    price_amount_micros: {
      type: DataTypes.INTEGER
    },
    price_currency_code: {
      type: DataTypes.STRING
    },
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_subscriptions',
  });
  return tbl_subscriptions;
};