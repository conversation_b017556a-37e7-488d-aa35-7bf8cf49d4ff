"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    return Promise.all([
      queryInterface.changeColumn("tbl_announcements", "admin_id", {
        type: Sequelize.INTEGER,
      }),
      queryInterface.addColumn("tbl_announcements", "user_id", {
        type: Sequelize.INTEGER,
      }),
      queryInterface.addColumn("tbl_announcements", "is_announcement", {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      }),
      queryInterface.addColumn("tbl_announcements", "isDeleted", {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      }),
    ]);
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    return Promise.all([
      queryInterface.changeColumn("tbl_announcements", "admin_id", {
        type: Sequelize.INTEGER,
        allowNull: false,
      }),
      queryInterface.removeColumn("tbl_announcements", "user_id"),
      queryInterface.removeColumn("tbl_announcements", "is_announcement"),
      queryInterface.removeColumn("tbl_announcements", "isDeleted"),
    ]);
  },
};
