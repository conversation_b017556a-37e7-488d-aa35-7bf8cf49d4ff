/** NPM-modules */
const express = require("express");
const router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;
const multer_middleware = require("../../middleware/multer");

/* require for Validators */
const validators = require("../../assets/validators");

/* require for  Controller*/
const admin_controller = require("../../controllers/cms/admin.controller");

// routes for admin list
router.post("/list", authentication, admin_controller.listAdmin); /** admin list API */
router.post("/sub-admins", authentication, admin_controller.listSubAdmin); /** sub admins list API */
router.post("/add", authentication, /*validators.cms.admin.addAdmin,*/ admin_controller.addAdmin); /** add admin API */
router.post("/retrive/:id", authentication, admin_controller.retriveAdmin); 
router.put("/edit/:id", authentication, admin_controller.editAdmin ); /** edit admin */
router.put("/delete/:id", authentication, admin_controller.deleteAdmin); /** delete admin */

module.exports = router;