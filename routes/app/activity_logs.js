/*
 * Summary:     tags.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const user_auth = require("../../middleware/auth").userAuthentication;
const activity_logs_controller = require("../../controllers/app/activity_logs.controller");

router.post("/list", user_auth, activity_logs_controller.list);

module.exports = router;
