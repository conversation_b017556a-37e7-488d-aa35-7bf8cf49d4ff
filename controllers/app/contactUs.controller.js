const status = require('../../config/status').status;
const message = require('../../config/web.message').appMessage;
const { validationResult } = require('express-validator');
const contactUsService = require('../../services/app/contactUs.service');
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
	async add(req, res) {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			let message = '';
			displayOnlyFirstError(errors.array()).map((el, i, arr) =>
				arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
			);
			return res.status(status.SUCCESSSTATUS).send({ data: {}, message: message, status: 0 });
		}
		try {
			const response = await contactUsService.add(req, res);

			res.status(201).send({
				message: message.SUCCESS,
				data: response,
			});
		} catch (error) {
			console.log('TCL:error', error);
			res.status(status.INTERNALSERVERERRORSTATUS).send({
				data: [],
				message: message.INTERNALSERVERERROR,
				status: status.ERROR,
			});
		}
	},
};
