'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tbl_new_subscriptions', {
      subscription_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      subscriptions_product_id: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      subscriptions_benefits: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      base_plan_id: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      duration: {
        type: Sequelize.ENUM("monthly", "yearly"),
        allowNull: true
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      price_amount_micros: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      price_currency_code: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          len: [3, 3],
        },
      },
      status: {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "inactive",
      },
      is_deleted: {
        allowNull: false,
        type: Sequelize.ENUM('0', '1'),
        defaultValue: '0',
      },
      is_free: {
        allowNull: false,
        type: Sequelize.ENUM('0', '1'),
        defaultValue: '0',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tbl_new_subscriptions');
  }
};