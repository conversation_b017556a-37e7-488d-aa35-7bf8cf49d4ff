# Kyulebag

Kyulebag App is as a self note keeping app including text, link, images, video, doc and audio. Text 
from images can be extracted using optical character recognition and video recordings can be 
transcribed.

## Getting Started
If, you do not have VSCode or Node installed follow these steps and after the requirements are met follow the above mentioned steps.

- Download and setup [VSCode](https://code.visualstudio.com/download).

- Download and Install [NodeJs](https://nodejs.org/en/download/) version (18.17.1).


Download and extract the project in your project directory or,

Clone the project in your preferred folder from git CLI using

```bash
    cd your_project_folder
```
```bash
    git clone https://gitlab.openxcell.dev/a-team/kyulebag/api.git 
```
```checkout
    git checkout --Name Required
```

Install dependencies

```bash
   npm install
```

### Prerequisites

Install the node_modules.

```
Hit the below command in Terminal:
-> npm install
```

### Installing

After getting the project in your local pull a branch that you'll be working on from git.
```
eg : git pull origin development
```

When cloned/downloaded the project, you will need to create a `.env` and change the following environment variables to run this project.

For this simply, copy and paste and rename `.env.development` to `.env` in your project folder.

> Note - Do not replace `.env.development`

Now, in your `.env` locate and change the
```
eg : Change DB_DATABASE=kyulebag to DB_DATABASE=local 
AND change DB_HOST=openxcell-development.c5uwiw99as4r.eu-west-1.rds.amazonaws.com to DB_HOST=localhost
```

Now run the project by entering the run command for node projects.
```
npm start
```
The project will be running fine if everything is perfect.

## Environment Variables
Some of the important and most used variables in the environment file are mentioned below.
| VARIABLE NAME | DEFAULT VALUE | DESCRIPTION 
| --- | --- | --- |              
| DB_PORT | 3306 | The port on which database  is runnning
| DB_DATABASE | 'kyulebag' | Database used to run the system
| DB_HOST | 'openxcell-development.c5uwiw99as4r.eu-west-1.rds.amazonaws.com' | Database Host
| DB_USERNAME | 'kyulebag' | Username of the database used
| DB_PASSWORD | '*****' | Password of the database used
| DB_CONNECTION | 'mysql' | The database dialect used
| --- | --- | ---|
## Running the tests

After doing the changes in a local branch, commit the changes after checking and running it on local to avoid errors on staging. After ensuring everything is fine stage the changes.

You can stage the changes by :
  -- git add <directory_name> (Stage all changes in directory for the next commit.)

After staging all the changes, commit the changes for push. The command to commit the changes: 
  -- git commit -am "commit message" (A power user shortcut command that combines the -a and -m options. This combination immediately creates a commit of all the staged changes and takes an inline commit message.)

After commiting the changes, we are ready to push the changes. 
   -- git push origin development
      (Since we already made sure the local main was up-to-date, this should result in a fast-forward merge, and git push should not complain about any of the non-fast-forward issues discussed above.)

## Built With
* [NodeJS](https://nodejs.org/en/docs/) - The web framework used
* (https://nodejs.org/en/docs/meta/topics/dependencies/) - Dependency Management

## Features

- Global Response Handler.
- Global Variables.
- By default Multi Language Support.
- Most of the status codes are available.
- Global Error Handler.
- Sequelize configured.

## Tech Stack

**Framework** - NodeJs, ReactJs

**Core Languages** - HTML5, CSS, Javascript

**DATABASE** - MySQL