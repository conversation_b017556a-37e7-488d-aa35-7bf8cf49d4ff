'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_admin_roles extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_admin_roles.init({
    admin_role_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    role_id: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    admin_id: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    view: {
      allowNull: false, 
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    edit: {
      allowNull: false, 
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    add: {
      allowNull: false, 
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    delete: {
      allowNull: false, 
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_admin_roles',
  });
  return tbl_admin_roles;
};