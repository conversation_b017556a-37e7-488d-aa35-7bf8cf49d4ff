/*
 * Summary:     user.services file for handling all USER related actions.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules,models and constants for configuration */
const Tags = require("../../database/models").tbl_tag_names;
const Activity_logs = require("../../database/models").tbl_activity_logs;

module.exports = {
  /* list */
  async list(req, res) {
    var find_tags = await Tags.findAndCountAll({
      where: { user_id: req.user_id },
      attributes: { include: ["createdAt", "updatedAt"] },
    });
    if (find_tags.count === 0) {
      return 0;
    } else {
      find_tags.rows.map(data => {
        data.dataValues.createdAt = data.dataValues.createdAt
          .toISOString()
          .substr(0, 19)
          .replace("T", " ");
        data.dataValues.updatedAt = data.dataValues.updatedAt
          .toISOString()
          .substr(0, 19)
          .replace("T", " ");
      });
      return find_tags;
    }
  },

  /* add */
  async add(req, res) {
    let tag_name = req.body.tag_name;
    var find_tag = await Tags.findOne({
      where: {
        tag_name: tag_name,
        user_id: req.user_id,
      },
    });

    if (find_tag) {
      return 0;
    }
    {
      await Tags.create({
        tag_name: tag_name,
        user_id: req.user_id,
        // include: ["createdAt", "updatedAt"],
      });
      let tags = await Tags.findOne({
        where: {
          tag_name: tag_name,
          user_id: req.user_id,
        },
        attributes: { include: ["createdAt", "updatedAt"] },
      });
      tags.dataValues.createdAt = tags.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");
      tags.dataValues.updatedAt = tags.dataValues.updatedAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Add New Tag",
        // description: `Tag-name ${tags.tag_name} added to Manage Tags.`
        description: `${tags.tag_name} tag created.`
      });
      return tags;
    }
  },

  /* edit */
  async edit(req, res) {
    let { tag_name_id, tag_name } = req.body;

    var find_tag_exist = await Tags.findOne({
      where: {
        tag_name: tag_name,
        user_id: req.user_id,
      },
    });

    if (find_tag_exist) {
      return 1;
    }

    var find_tag = await Tags.findOne({
      where: {
        tag_name_id: tag_name_id,
        user_id: req.user_id,

      },
    });

    if (find_tag) {
      await Tags.update(
        {
          tag_name: tag_name,
        },
        { where: { tag_name_id: tag_name_id, user_id: req.user_id } }
      );
      let tags = await Tags.findOne({
        where: {
          tag_name_id: tag_name_id,
          user_id: req.user_id,
        },
        attributes: { include: ["createdAt", "updatedAt"] },
      });
      tags.dataValues.createdAt = tags.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");
      tags.dataValues.updatedAt = tags.dataValues.updatedAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Update Tag",
        // description: `Tag-name ${tags.tag_name} gets edited in Manage Tags.`
        description: `${tags.tag_name} tag edited.`
      });
      return tags;
    }
    {
      return 0;
    }
  },

  /* delete */
  async delete(req, res) {
    let { tag_name_id } = req.query;
    var find_tag = await Tags.findOne({
      where: {
        tag_name_id: tag_name_id,
        user_id: req.user_id,

      },
    });

    if (find_tag) {
      await Tags.destroy({ where: { tag_name_id: tag_name_id, user_id: req.user_id, } });
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Delete Tag",
        description: `${find_tag.tag_name} tag deleted.`
      });
      return 1;
    }
    {
      return 0;
    }
  },
};
