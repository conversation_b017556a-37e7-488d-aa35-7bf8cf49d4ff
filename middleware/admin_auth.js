/**
 * Admin Authentication Middleware - JWT Token Validation for CMS
 *
 * This middleware provides JWT-based authentication for admin/CMS endpoints
 * in the KyuleBag application. It validates admin authentication tokens and
 * establishes admin context for protected CMS operations.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

const AdminTokens = require('../database/models').tbl_admin_tokens;
const JWT = require('jsonwebtoken');
const constant = require('../config/constant');
const message = require('../config/cms.message');
const status = require('../config/status');

exports.adminAuthentication = async (req, res, next) => {
  if (req.headers.authorization) {
    try {
      let jwtGetAdminDetail = await JWT.verify(
        req.headers.authorization,
        constant.JWTTOKEN.secret,
        { algorithm: constant.JWTTOKEN.algo }
      );

      let getAdminAuthDetails = await AdminTokens.findOne({
        where: {
          admin_id: jwtGetAdminDetail.admin_id,
          token: req.headers.authorization,
        },
      });

      if (getAdminAuthDetails) {
        req.admin_id = getAdminAuthDetails.admin_id;
        next();
      } else {
        res.status(status.status.UNAUTHORIZEDUSER).send({
          data: {},
          message: message.cmsMessage.INVALIDHEADERS,
          status: status.status.ERROR,
        });
      }
    } catch (error) {
      res.status(status.status.UNAUTHORIZEDUSER).send({
        data: {},
        message: message.cmsMessage.TOKENNOTMATCHED,
        status: status.status.ERROR,
      });
    }
  } else {
    res.status(status.status.UNAUTHORIZEDUSER).send({
      data: {},
      message: message.cmsMessage.TOKENREQUIRED,
      status: status.status.ERROR,
    });
  }
};
