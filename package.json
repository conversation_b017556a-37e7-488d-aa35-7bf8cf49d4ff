{"name": "kyulebag", "version": "0.0.0", "private": true, "main": "app.js", "scripts": {"start": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose"}, "dependencies": {"@google-cloud/pubsub": "^4.10.0", "@google-cloud/storage": "^7.0.1", "@google-cloud/video-intelligence": "^5.0.1", "@google-cloud/vision": "^4.0.2", "bcryptjs": "^2.4.3", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "crypto": "^1.0.1", "date-and-time": "^1.0.0", "debug": "~2.6.9", "dotenv": "^8.2.0", "ejs": "^3.1.5", "express": "^4.16.4", "express-validator": "^6.10.0", "extract-urls": "^1.3.2", "fcm-node": "^1.3.0", "get-audio-duration": "^3.0.0", "get-video-duration": "^4.0.0", "googleapis": "^144.0.0", "hbs": "^4.2.0", "html-pdf-node": "^1.0.7", "http-errors": "~1.6.3", "image-size": "^1.0.0", "jsonwebtoken": "^9.0.2", "link-preview-js": "^3.0.4", "moment": "^2.29.1", "morgan": "~1.9.1", "multer": "^1.4.4", "mysql": "^2.18.1", "mysql2": "^2.1.0", "node-cron": "^3.0.0", "node-xlsx": "^0.4.0", "nodejs-nodemailer-outlook": "^1.2.4", "nodemailer": "^6.4.11", "nodemon": "^2.0.4", "path": "^0.12.7", "promise-mysql": "^5.2.0", "puppeteer": "^23.11.1", "request": "^2.88.2", "sequelize": "^6.33.0", "socket.io": "^4.4.0", "telnyx": "^1.23.0"}, "devDependencies": {"sequelize-cli": "^6.4.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "jest-environment-node": "^29.7.0", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["services/**/*.js", "controllers/**/*.js", "middleware/**/*.js", "helper/**/*.js", "!**/node_modules/**", "!**/coverage/**"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"], "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"]}}