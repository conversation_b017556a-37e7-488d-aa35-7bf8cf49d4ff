"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */
    return queryInterface.bulkInsert(
      "tbl_admins",
      [
        {
          admin_id: 1,
          firstName: "Nirad",
          lastName: "Lalani",
          photo: "1641220181031.jpeg",
          email: "<EMAIL>",
          password: "$2a$10$pe9G0qMO3wQOYuACeyaVAuc5F.7Cb7WdkpQJI9Y9D.7cG7IlLTgWa",
          adminType: "superAdmin",
          isLogin: "false",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          admin_id: 2,
          firstName: "Nilesh",
          lastName: "Patil",
          photo: "1641220126046.png",
          email: "<EMAIL>",
          password: "$2a$10$pe9G0qMO3wQOYuACeyaVAuc5F.7Cb7WdkpQJI9Y9D.7cG7IlLTgWa",
          adminType: "subAdmin",
          isLogin: "false",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          admin_id: 3,
          firstName: "Kyulebag",
          lastName: "Admin",
          photo: "1641220091933.png",
          email: "<EMAIL>",
          password: "$2a$10$iNPy6J.bS7SJnrrqH7T99.ITHqnlZbC0eMfnfLoDdjuxDuU6ATUwe",
          adminType: "superAdmin",
          isLogin: "false",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {}
    );
  },

  down: async (queryInterface, Sequelize) => {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete("tbl_admins",{},null);
  },
};
