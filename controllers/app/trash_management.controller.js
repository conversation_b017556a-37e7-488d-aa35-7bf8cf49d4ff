/*
 * Summary:     tags.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const tags_service = require("../../services/app/tags.service");
const trash_service = require("../../services/app/trash.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* list */
  async list(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let trash = await trash_service.list(req, res);
      if (trash) {
        res.status(status.SUCCESSSTATUS).send({
          data: trash,
          message: message.TRASHRETRIEVE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOITEMFOUND,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* delete */
  async restore(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let restore = await trash_service.restore(req, res);
      if (restore) {
        res.status(status.SUCCESSSTATUS).send({
          data: restore,
          message: message.ITEMRESTORED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOITEMFOUND,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* delete */
  async delete(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let trash = await trash_service.delete(req, res);
      if (trash) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ITEMDELETED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOITEMFOUND,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
