const vision = require('@google-cloud/vision');
const videoIntelligence = require('@google-cloud/video-intelligence');
const CREDENTIALS = require("../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json");
const CONSTANT = require("../config/constant");
var moment = require('moment');
const { Storage } = require('@google-cloud/storage');
const { google } = require('googleapis');
const path = require('path');
const constant = require('../config/constant');
const keyFilePath = path.join(__dirname, '../assets/subscription.json');

const storage = new Storage({
  projectId: CONSTANT.GCP_PROJECT_ID,
  credentials: require("../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json")
});

const CONFIG = {
  credentials: {
    private_key: CREDENTIALS.private_key,
    client_email: CREDENTIALS.client_email
  }
};

const client = new vision.ImageAnnotatorClient(CONFIG);
const clientVideo = new videoIntelligence.VideoIntelligenceServiceClient(CONFIG);

const createAPIRequest = async (path, featuresType) => {
  return {
    inputUri: `gs://${CONSTANT.GCP_BUCKET_NAME}/${path}`,
    features: [featuresType]
  }
}

async function authenticate() {
  const auth = new google.auth.GoogleAuth({
    keyFile: keyFilePath,
    scopes: ['https://www.googleapis.com/auth/androidpublisher'],
  });

  return google.androidpublisher({
    version: 'v3',
    auth,
  });
}

const getSubscriptionDetails = async (subscriptionId, purchaseToken) => {
  try {
    const packageName = constant.PLAY_CONSOLE_PACKAGE_NAME;
    const auth = await authenticate();

    // Fetch subscription details
    // const response = await auth.purchases.products.get({
    const response = await auth.purchases.subscriptions.get({
      packageName,
      subscriptionId,
      token: purchaseToken,
    });
    // productId: subscriptionId,

    console.log("🚀 ~ getSubscriptionDetails ~  response.data:",  response.data)
    // return  response.data; // Subscription details
    return { status: true, ...response.data }; // Subscription details
  } catch (error) {
    console.error("Error fetching subscription details:", error.response?.data || error.message);
  }
}

const createSubscriptionProduct = async (subscription) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.monetization.subscriptions.create({
      packageName: subscription.packageName,
      productId: subscription.productId,
      requestBody: subscription,
      'regionsVersion.version': '2022/02',
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const updateSubscriptionProduct = async (subscription) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.monetization.subscriptions.patch({
      packageName: subscription.packageName,
      productId: subscription.productId,
      requestBody: subscription,
      updateMask: 'basePlans,listings',
      'regionsVersion.version': '2022/02',
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const inactivateSubscriptionProduct = async (packageName, productId, basePlanId) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.monetization.subscriptions.basePlans.deactivate({
      packageName: packageName,
      productId: productId,
      basePlanId: basePlanId,
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const activateSubscriptionProduct = async (packageName, productId, basePlanId) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.monetization.subscriptions.basePlans.activate({
      packageName: packageName,
      productId: productId,
      basePlanId: basePlanId,
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const createInAppProduct = async (productConfig) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.inappproducts.insert({
      packageName: productConfig.packageName,
      requestBody: productConfig,
      autoConvertMissingPrices: true,
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const updateInAppProduct = async (productConfig) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.inappproducts.update({
      packageName: productConfig.packageName,
      sku: productConfig.sku,
      requestBody: productConfig,
      autoConvertMissingPrices: true,
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const deleteInAppProduct = async (packageName, productId) => {
  try {
    const playApi = await authenticate();
    return await playApi.inappproducts.delete({
      packageName: packageName,
      sku: productId,
    });
  } catch (error) {
    throw error;
  }
}

const activateAndDeactivateInAppProduct = async (packageName, productId, status) => {
  try {
    const playApi = await authenticate();
    const res = await playApi.inappproducts.patch({
      packageName: packageName,
      sku: productId,
      requestBody: {
        packageName: packageName,
        sku: productId,
        status: status,
      },
    });

    return res.data;
  } catch (error) {
    throw error;
  }
}

const imageLabelDetectionAndGetResults = async (path, confidence) => {
  console.log("🚀 ~ file: gcpUtils.js:24 ~ imageLabelDetectionAndGetResults ~ confidence:", confidence)
  const Confidence = confidence ? confidence === 1 ? 0.9 : confidence : 0.4
  const [operation] = await client.labelDetection(`gs://${CONSTANT.GCP_BUCKET_NAME}/${path}`);
  const labels = operation.labelAnnotations;
  console.log("🚀 ~ file: gcpUtils.js:35 ~ imageLabelDetectionAndGetResults ~ labels:", labels)
  let Arr = [];
  labels.forEach(label => {
    const Obj = {};
    if (label.score >= Confidence) {
      Obj["Name"] = label.description
      Obj["Confidence"] = label.score
      Arr.push(Obj);
    }
  });

  if (operation.error) {
    // operation.error.message = operation.error.message === "Bad image data." ? "Error encountered while analyzing the image data." : operation.error.message
    operation.error.message = "Error encountered while analyzing the image data."
    // return { "Labels": Arr, "Error": "Error encountered while analyzing the image data." }
    return { "Labels": Arr, "Error": operation.error }
  } else {
    return { "Labels": Arr }
  }
}

const imageOCRDetectionAndGetResults = async (path) => {
  const [operation] = await client.textDetection(`gs://${CONSTANT.GCP_BUCKET_NAME}/${path}`);
  const OCR = operation.fullTextAnnotation ? operation.fullTextAnnotation.text.split("\n") : [];
  let Arr = [];
  OCR.forEach(ocr => {
    const Obj = {};
    Obj["Type"] = "LINE",
      Obj["DetectedText"] = ocr
    Arr.push(Obj);
  });
  return Arr
}

const deleteGCPFolder = async (path) => {
  const [files] = await storage.bucket(CONSTANT.GCP_BUCKET_NAME).getFiles({ prefix: path });

  // Delete each object in the folder
  await Promise.all(files.map(file => file.delete()));

  console.log(`Objects in folder ${path} deleted successfully.`);
  return
}

// const videoLabelDetectionAndGetResults = async (path, confidence) => {
//   const Confidence = confidence ? confidence : 0.4
//   const apiRequest = await createAPIRequest(path, CONSTANT.GCP_INTELLIGENCE_FEATURE_TYPE.LABEL_DETECTION)
//   const [operation] = await clientVideo.annotateVideo(apiRequest);
//   const [operationResult] = await operation.promise();
//   const annotations = operationResult.annotationResults[0];
//   const labels = annotations.segmentLabelAnnotations;
//   let Arr = [];
//   labels.forEach(label => {
//     const Obj = {};
//     label.segments.forEach(segment => {
//       if (segment.confidence >= Confidence) {
//         Obj["Name"] = label.entity.description
//         Obj["Confidence"] = segment.confidence
//         Arr.push(Obj);
//       }
//     });
//   });

//   if (operation.error) {
//     operation.error.message = operation.error.message === "Bad image data." ? "Error encountered while analyzing the image data." : operation.error.message
//     return { "Labels": Arr, "Error": operation.error }
//   } else {
//     return { "Labels": Arr }
//   }
// }

const videoLabelDetectionAndGetResults = async (path, confidence) => {
  const Confidence = confidence ? confidence === 1 ? 0.9 : confidence : 0.4
  const apiRequest = await createAPIRequest(path, CONSTANT.GCP_INTELLIGENCE_FEATURE_TYPE.LABEL_DETECTION);
  const [operation] = await clientVideo.annotateVideo(apiRequest);

  try {
    const [operationResult] = await operation.promise();
    const annotations = operationResult?.annotationResults?.[0];
    if (!annotations) {
      throw new Error("No annotation results found.");
    }

    const labels = annotations.segmentLabelAnnotations || [];
    const Arr = labels.flatMap(label =>
      label.segments
        .filter(segment => segment.confidence >= Confidence)
        .map(segment => ({
          "Name": label.entity.description,
          "Confidence": segment.confidence
        }))
    );

    return { "Labels": Arr };
  } catch (error) {
    error.message = "Error encountered while analyzing the video data.";
    return { "Labels": [], "Error": error };
  }
};


const videoOCRDetectionAndGetResults = async (path, confidence) => {
  // const Confidence = confidence ? confidence : 0.4
  const apiRequest = await createAPIRequest(path, CONSTANT.GCP_INTELLIGENCE_FEATURE_TYPE.TEXT_DETECTION)
  const [operation] = await clientVideo.annotateVideo(apiRequest);
  const [operationResult] = await operation.promise();
  const annotations = operationResult.annotationResults[0];
  const OCR = annotations.textAnnotations;
  // let Arr = [];
  // OCR.forEach(ocr => {
  //   const Obj = {};
  //   // ocr.segments.forEach(segment => {
  //   //   if (segment.confidence >= Confidence) {
  //   Obj["Type"] = "LINE"
  //   Obj["DetectedText"] = ocr.text
  //   // Obj["Confidence"] = segment.confidence
  //   Arr.push(Obj);
  //   //   }
  //   // });
  // });

  // Arr.filter((a, b) => Arr.findIndex((i) => a.DetectedText === i.DetectedText) === b)

  // return Arr

  const uniqueTexts = new Set();
  const result = OCR.map(ocr => ({
    Type: "LINE",
    DetectedText: ocr.text
  })).filter(item => {
    // Filter and add unique texts to the Set
    if (!uniqueTexts.has(item.DetectedText)) {
      uniqueTexts.add(item.DetectedText);
      return true;
    }
    return false;
  });

  return result;
}

const getPrivateUrl = async (bucketName, gcsFileName, config) => new Promise((resolve, reject) => {
  config = config || {
    action: 'read',
    // expires: moment().add(5, 'minute').format(),
    expires: moment().add(2, 'day').format(),
  }

  // If you don't pass config, it will set read as the default action and 1 day from current time as the expiry time.
  // _.defaults(config, {
  //   action: 'read',
  //   expires: moment().add(1, 'day').format(),
  // });

  const bucket = storage.bucket(bucketName);
  const file = bucket.file(gcsFileName);

  file.getSignedUrl(config, (err, res) => {
    if (err) {
      return reject(err);
    }

    resolve(res);
  });
});

module.exports = {
  createAPIRequest,
  imageLabelDetectionAndGetResults,
  videoLabelDetectionAndGetResults,
  imageOCRDetectionAndGetResults,
  videoOCRDetectionAndGetResults,
  getPrivateUrl,
  deleteGCPFolder,
  createSubscriptionProduct,
  activateSubscriptionProduct,
  inactivateSubscriptionProduct,
  updateSubscriptionProduct,
  createInAppProduct,
  updateInAppProduct,
  deleteInAppProduct,
  activateAndDeactivateInAppProduct,
  getSubscriptionDetails
};