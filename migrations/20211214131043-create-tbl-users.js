"use strict";
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable("tbl_users", {
      user_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      first_name: {
        type: Sequelize.STRING,
      },
      last_name: {
        type: Sequelize.STRING,
      },
      facebook_id: {
        type: Sequelize.STRING,
      },
      google_id: {
        type: Sequelize.STRING,
      },
      apple_id: {
        type: Sequelize.STRING,
      },
      email: {
        unique: true,
        type: Sequelize.STRING,
      },
      password: {
        type: Sequelize.STRING,
      },
      private_note_password: {
        type: Sequelize.STRING,
      },
      phone: {
        unique: true,
        type: Sequelize.STRING,
      },
      country_code: {
        type: Sequelize.STRING,
      },
      gender: {
        type: Sequelize.ENUM("male", "female", "other"),
      },
      photo: {
        type: Sequelize.STRING,
      },
      login_type: {
        allowNull: false,
        type: Sequelize.ENUM("KyuleBag", "Google", "FaceBook"),
        defaultValue: "KyuleBag",
      },
      push_notification: {
        type: Sequelize.INTEGER,
        defaultValue: 1,
      },
      status: {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "active",
      },
      is_verified: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      verification_code: {
        type: Sequelize.STRING,
      },
      device_type: {
        type: Sequelize.ENUM("ios", "android"),
      },
      device_token: {
        type: Sequelize.STRING,
      },
      total_storage: {
        type: Sequelize.FLOAT,
      },
      used_storage: {
        type: Sequelize.FLOAT,
      },
      is_web_link_preview_set: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      AI_set: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      OCR_set: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      AI_confidence_level: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      biometric_authentication: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      hide_AI_label: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      hide_OCR_label: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      total_ai_api: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      used_ai_api: {
        type: Sequelize.FLOAT,
        defaultValue: 0,
      },
      version: {
        allowNull: false,
        type: Sequelize.STRING,
      },
      is_deleted: {
        allowNull: false,
        type: Sequelize.ENUM("0", "1"),
        defaultValue: "0",
      },
      email_verified: {
        allowNull: false,
        type: Sequelize.ENUM("0", "1"),
        defaultValue: "0",
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal(
          "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
      },
      // createdAt: {
      //   allowNull: false,
      //   type: Sequelize.DATE,
      //   defaultValue: Sequelize.NOW,
      // },
      // updatedAt: {
      //   allowNull: false,
      //   type: Sequelize.DATE,
      //   defaultValue: Sequelize.NOW,
      // },
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable("tbl_users");
  },
};
