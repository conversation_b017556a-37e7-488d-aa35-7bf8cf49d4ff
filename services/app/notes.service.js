const Notes = require("../../database/models").tbl_item_notes;
const Items = require("../../database/models").tbl_items;
const Activity_logs = require("../../database/models").tbl_activity_logs;

module.exports = {
  /* add */
  async note_add(req, res) {
    let { item_id, note_description } = req.body;
    var find_items = await Items.findOne({
      where: {
        item_id: item_id,
      },
    });

    if (find_notes) {
      var find_notes = await Notes.findOne({
        where: {
          item_id: find_items.item_id,
        },
      });
    }

    if (find_notes) {
      return 0;
    }
    {
      let notes = await Notes.create({
        item_id: item_id,
        note_description: note_description,
      });
      notes.dataValues.createdAt = notes.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");
      notes.dataValues.updatedAt = notes.dataValues.updatedAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");

      let itemType = find_items.dataValues.item_type.charAt(0).toUpperCase() + find_items.dataValues.item_type.substr(1).toLowerCase();
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Add Note",
        // description: `Notes get added into item.`
        description: `Notes added to ${itemType}.`
      });

      return notes;
    }
  },

  /* edit */
  async note_edit(req, res) {
    let { item_id, note_id, note_description } = req.body;
    var find_item = await Items.findOne({
      where: {
        item_id: item_id,
      },
    });
    var find_note = await Notes.findOne({
      where: {
        id: note_id,
        item_id: item_id,
      },
    });
    if (find_note) {
      await Notes.update(
        {
          note_description: note_description,
        },
        { where: { id: find_note.id, item_id: item_id } }
      );
      let notes = await Notes.findOne({
        where: {
          id: find_note.id,
          item_id: item_id,
        },
      });
      notes.dataValues.createdAt = notes.dataValues.createdAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");
      notes.dataValues.updatedAt = notes.dataValues.updatedAt
        .toISOString()
        .substr(0, 19)
        .replace("T", " ");

      let itemType = find_item.dataValues.item_type.charAt(0).toUpperCase() + find_item.dataValues.item_type.substr(1).toLowerCase();
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Update Note",
        //  description:`Notes get edited for item ${notes.id}`
        // description: `Notes get edited for item.`
        description: `${itemType} related notes edited.`
      });
      return notes;
    }
    {
      return 0;
    }
  },
  /* delete */
  async note_delete(req, res) {
    var find_item = await Items.findOne({
      where: {
        item_id: req.query.item_id,
      },
    });
    var find_note = await Notes.findOne({
      where: {
        item_id: req.query.item_id,

        id: req.query.note_id,
      },
    });

    if (find_note) {
      await Notes.destroy({
        where: {
          item_id: find_note.item_id,
          id: find_note.id,
        },
      });

      let itemType = find_item.dataValues.item_type.charAt(0).toUpperCase() + find_item.dataValues.item_type.substr(1).toLowerCase();
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Delete Note",
        // description: `Notes get deleted from item.`
        description: `Notes deleted from ${itemType}.`
      });
      return 1;
    }
    {
      return 0;
    }
  },
};
