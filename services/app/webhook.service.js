const User = require("../../database/models").tbl_users;
const UserSubscription = require("../../database/models").tbl_user_subscriptions;
const NewStoragePlan = require("../../database/models").tbl_new_subscriptions;
const ActivityLogs = require("../../database/models").tbl_activity_logs;
const { getSubscriptionDetails } = require("../../helper/gcpUtils");
const status = require("../../config/status").status;
const moment = require('moment');
const Sequelize = require("sequelize");

exports.webhook = async (req, res) => {
  // return getSubscriptionDetails("new_basic_plan","ddlfhocbeocngnhhmjjfbpod.AO-J1OxW_rWWEQfzrvK57ZMEe_awWyV8YiqpHjcMkGHCzQq9SYeuZ1kIQZ_6qq3AoLja8IDDqQGhcDt03h4CwP2sjm94FR2JTg")
  // return getSubscriptionDetails("ai_small","hclcidcfofogpanmlijofphc.AO-J1OzHRY7ghBZx38L-RQfUwdO4ZBx3wFwPeWlMQgplp-mf_nLSHmDnBT5fBQc_CEhUnzMrLti94GJdd-WT8Li0ydxvSJhK2Q")

  const message = req.body.message;

  if (!message || !message.data) {
    return res.status(400).send('Invalid Pub/Sub message format');
  }

  // Decode the Base64 message
  const jsonData = Buffer.from(message.data, 'base64').toString('utf8');
  const notification = JSON.parse(jsonData);

  console.log('Received Notification:', notification);

  if (notification.subscriptionNotification) {

    let subscriptionReceiptValResult = await getSubscriptionDetails(notification.subscriptionNotification.subscriptionId, notification.subscriptionNotification.purchaseToken);
    let startDate;
    let endDate;
    if (subscriptionReceiptValResult.status) {

      switch (notification.subscriptionNotification.notificationType) {
        case 1:
          console.log('Subscription RECOVERED:', notification);
          break;
        case 2:
          console.log('Subscription renewed:', notification);

          if (subscriptionReceiptValResult?.obfuscatedExternalAccountId) {
            const activeSubscriptionPlan = await NewStoragePlan.findOne({
              where: { subscriptions_product_id: notification.subscriptionNotification.subscriptionId },
            });

            startDate = moment.utc(parseInt(subscriptionReceiptValResult.startTimeMillis)).format();
            endDate = moment.utc(parseInt(subscriptionReceiptValResult.expiryTimeMillis)).format();

            await Promise.all([
              UserSubscription.update(
                { is_active: "0", is_expired: "1" },
                { where: { is_active: "1", is_expired: "0", is_canceled: "0", user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId } }
              ),
              UserSubscription.create({
                user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
                subscription_id: activeSubscriptionPlan.subscription_id,
                transaction_id: subscriptionReceiptValResult.orderId,
                payment_amount: (subscriptionReceiptValResult.priceAmountMicros / 1000000),
                purchase_token: notification.subscriptionNotification.purchaseToken,
                start_date: startDate,
                end_date: endDate,
                status: 'completed',
                is_active: true,
                is_expired: false,
                is_canceled: false,
              }),
              ActivityLogs.create({
                user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
                title: "Subscription renewed",
                description: "Your storage plan has been renewed.",
              })
            ]);

          } else {
            console.log("User not found for RENEWED notification");

          }
          break;

        case 3:
          console.log('Subscription canceled:', notification);
          let userId;
          if (subscriptionReceiptValResult?.obfuscatedExternalAccountId) {
            userId = subscriptionReceiptValResult.obfuscatedExternalAccountId;
          } else {
            console.log("User not found for CANCELED notification");
            const userLastPlan = await UserSubscription.findOne({
              where: {
                purchase_token: notification.subscriptionNotification.purchaseToken,
              },
              order: [[Sequelize.literal(`createdAt`), `DESC`]]
            });

            userId = userLastPlan?.user_id || null;
          }
          // const userDetailPromise = User.findOne({
          //   where: { user_id: userId },
          // });

          // const subscriptionPlanDetailPromise = NewStoragePlan.findOne({
          //   where: { subscriptions_product_id: notification.subscriptionNotification.subscriptionId },
          // });

          // const [userDetail, subscriptionPlanDetail] = await Promise.all([userDetailPromise, subscriptionPlanDetailPromise]);

          // let extraStorageKbValues = 0;
          // if (subscriptionPlanDetail) {

          //   const regex = /(\d+)\s*(KB|MB|GB|TB|kb|mb|gb|tb)/i;
          //   const match = subscriptionPlanDetail.subscriptions_benefits.match(regex);

          //   if (match) {
          //     const value = match[1];
          //     const intValue = parseInt(value);
          //     const unit = match[2].toUpperCase();

          //     switch (unit) {
          //       case 'KB':
          //         extraStorageKbValues = intValue;
          //         break
          //       case 'MB':
          //         extraStorageKbValues = (intValue * 1000);
          //         break
          //       case 'GB':
          //         extraStorageKbValues = (intValue * 1000 * 1000);
          //         break
          //       case 'TB':
          //         extraStorageKbValues = (intValue * 1000 * 1000 * 1000);
          //         break
          //       default:
          //         extraStorageKbValues = (intValue * 1000 * 1000);
          //         break
          //     }
          //   }
          // }

          if (userId) {
            const canceledDate = moment.utc(parseInt(subscriptionReceiptValResult?.userCancellationTimeMillis ? subscriptionReceiptValResult?.userCancellationTimeMillis : subscriptionReceiptValResult.expiryTimeMillis)).format();

            // const userExtraStorage = userDetail.extra_storage - extraStorageKbValues;
            // const userDeletePromise = User.update(
            //   { extra_storage: userExtraStorage },
            //   { where: { user_id: userId } }
            // );
            const userSubscriptionUpdatePromise = UserSubscription.update(
              { is_canceled: '1', canceled_date: canceledDate },
              {
                where: {
                  is_active: '1',
                  is_expired: '0',
                  is_canceled: '0',
                  user_id: userId,
                },
              }
            );            

            await Promise.all([/*userDeletePromise,*/ userSubscriptionUpdatePromise]);

          }
          break;

        case 4:
          console.log('Subscription purchased:', notification);

          if (subscriptionReceiptValResult?.obfuscatedExternalAccountId) {

            const userActivePlanPromise = UserSubscription.findOne({
              where: {
                user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
                is_active: "1",
                is_expired: "0",
                is_canceled: "0",
              },
              order: [['createdAt', 'DESC']]
            });

            const subscriptionPlanPromise = NewStoragePlan.findOne({
              where: { subscriptions_product_id: notification.subscriptionNotification.subscriptionId },
            });

            const userPromise = User.findOne({
              where: { user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId },
            });

            const [userActivePlan, subscriptionPlan, user] = await Promise.all([userActivePlanPromise, subscriptionPlanPromise, userPromise]);


            if (userActivePlan) {
              await UserSubscription.update(
                {
                  is_active: "0",
                  is_expired: "1",
                },
                {
                  where: {
                    // user_subscription_id: userActivePlan.user_subscription_id,
                    user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
                    is_active: "1",
                  }
                }
              );
            }

            let extraStorageKbValue = 0;
            if (subscriptionPlan) {

              const regex = /(\d+)\s*(KB|MB|GB|TB|kb|mb|gb|tb)/i;
              const match = subscriptionPlan.subscriptions_benefits.match(regex);

              if (match) {
                const value = match[1];
                const intValue = parseInt(value);
                const unit = match[2].toUpperCase();

                switch (unit) {
                  case 'KB':
                    console.log("inside KB case");
                    extraStorageKbValue = intValue;
                    break
                  case 'MB':
                    console.log("inside MB case");
                    extraStorageKbValue = (intValue * 1000);
                    break
                  case 'GB':
                    console.log("inside GB case");
                    extraStorageKbValue = (intValue * 1000 * 1000);
                    break
                  case 'TB':
                    console.log("inside TB case");
                    extraStorageKbValue = (intValue * 1000 * 1000 * 1000);
                    break
                  default:
                    console.log("in default case");
                    extraStorageKbValue = (intValue * 1000 * 1000);
                    break
                }
              }
            }
            console.log(`Extra storage in KB: ${extraStorageKbValue}`);

            startDate = moment.utc(parseInt(subscriptionReceiptValResult.startTimeMillis)).format();
            endDate = moment.utc(parseInt(subscriptionReceiptValResult.expiryTimeMillis)).format();
            // const endDate = moment.utc(startDate).add(1, body.subscriptionType).subtract(1, 'seconds');

            const userSubscriptionCreatePromise = UserSubscription.create({
              user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
              subscription_id: subscriptionPlan.subscription_id,
              transaction_id: subscriptionReceiptValResult.orderId,
              payment_amount: (subscriptionReceiptValResult.priceAmountMicros / 1000000),
              purchase_token: notification.subscriptionNotification.purchaseToken,
              start_date: startDate,
              end_date: endDate,
              status: 'completed',
              is_active: true,
              is_expired: false,
              is_canceled: false,
            })

            const upgradeUserExtraStorage = user.extra_storage + extraStorageKbValue;
            const userUpdatePromise = User.update(
              { extra_storage: upgradeUserExtraStorage },
              { where: { user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId } }
            );

            ActivityLogs.create({
              user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
              title: "Subscription purchased",
              description: `You have purchased a subscription of ${subscriptionPlan?.subscriptions_benefits} for ${subscriptionPlan?.price} ${subscriptionPlan?.price_currency_code} per ${subscriptionPlan?.duration}.`,
            })
          } else {
            console.log("User not found for PURCHASED notification");

          }

          break;
        case 6:
          console.log('Subscription IN_GRACE_PERIOD:', notification);

          if (subscriptionReceiptValResult?.obfuscatedExternalAccountId) {
            const planDetail = await NewStoragePlan.findOne({
              where: { subscriptions_product_id: notification.subscriptionNotification.subscriptionId },
            });

            const userInfo = await User.findOne({
              where: { user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId },
            });

            let storageKbValue = 0;
            if (planDetail) {

              const regex = /(\d+)\s*(KB|MB|GB|TB|kb|mb|gb|tb)/i;
              const match = planDetail.subscriptions_benefits.match(regex);

              if (match) {
                const value = match[1];
                const intValue = parseInt(value);
                const unit = match[2].toUpperCase();

                switch (unit) {
                  case 'KB':
                    storageKbValue = intValue;
                    break
                  case 'MB':
                    storageKbValue = (intValue * 1000);
                    break
                  case 'GB':
                    storageKbValue = (intValue * 1000 * 1000);
                    break
                  case 'TB':
                    storageKbValue = (intValue * 1000 * 1000 * 1000);
                    break
                  default:
                    storageKbValue = (intValue * 1000 * 1000);
                    break
                }
              }
            }
            const userRemoveExtraStorage = userInfo.extra_storage - storageKbValue;
            const userRemoveExtraStoragePromise = User.update(
              { extra_storage: userRemoveExtraStorage },
              { where: { user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId } }
            );
            const expiredUserExtraStoragePromise = UserSubscription.update(
              { is_expired: "1" },
              { where: { is_active: "0", is_expired: "0"/*, is_canceled: "1"*/, user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId } }
            )

            await Promise.all([userRemoveExtraStoragePromise, expiredUserExtraStoragePromise]);
            break;
          } else {
            console.log("User not found for IN_GRACE_PERIOD notification");

          }
        case 12:
        case 13:
          console.log('Subscription EXPIRED & REVOKED:', notification);

          if (subscriptionReceiptValResult?.obfuscatedExternalAccountId) {
            const subscriptionPlanDetails = await NewStoragePlan.findOne({
              where: { subscriptions_product_id: notification.subscriptionNotification.subscriptionId },
            });

            const userDetails = await User.findOne({
              where: { user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId },
            });

            let storageKbValues = 0;
            if (subscriptionPlanDetails) {

              const regex = /(\d+)\s*(KB|MB|GB|TB|kb|mb|gb|tb)/i;
              const match = subscriptionPlanDetails.subscriptions_benefits.match(regex);

              if (match) {
                const value = match[1];
                const intValue = parseInt(value);
                const unit = match[2].toUpperCase();

                switch (unit) {
                  case 'KB':
                    storageKbValues = intValue;
                    break
                  case 'MB':
                    storageKbValues = (intValue * 1000);
                    break
                  case 'GB':
                    storageKbValues = (intValue * 1000 * 1000);
                    break
                  case 'TB':
                    storageKbValues = (intValue * 1000 * 1000 * 1000);
                    break
                  default:
                    storageKbValues = (intValue * 1000 * 1000);
                    break
                }
              }
            }
            const userRemoveStorage = userDetails.extra_storage - storageKbValues;
            const userRemoveStoragePromise = User.update(
              { extra_storage: userRemoveStorage },
              { where: { user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId } }
            );
            const expiredUserStoragePromise = UserSubscription.update(
              { is_active: '0', is_expired: '1' },
              {
                where: {
                  is_active: '1',
                  is_expired: '0' /*, is_canceled: "1"*/,
                  is_canceled: '1',
                  user_id:
                    subscriptionReceiptValResult.obfuscatedExternalAccountId,
                },
              }
            );

            await Promise.all([userRemoveStoragePromise, expiredUserStoragePromise, ActivityLogs.create({
              user_id: subscriptionReceiptValResult.obfuscatedExternalAccountId,
              title: "Subscription EXPIRED",
              description: "Your storage subscription plan expired.",
            })]);

          } else {
            console.log("User not found for EXPIRED & REVOKED notification");

          }
          break;
        default:
          console.log('Other notification type:', notification);
      }
    }
  }

  res.send();
  // return res.status(status.SUCCESSSTATUS);
};
