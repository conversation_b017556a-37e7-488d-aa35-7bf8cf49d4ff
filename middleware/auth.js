/**
 * User Authentication Middleware - JWT Token Validation
 *
 * This middleware module provides JWT-based authentication for protecting API endpoints
 * in the KyuleBag application. It validates JWT tokens, verifies user sessions, and
 * establishes user context for authenticated requests.
 *
 * Business Logic Connection:
 * - Secures all protected API endpoints requiring user authentication
 * - Validates user sessions and prevents unauthorized access to user data
 * - Establishes user context (req.user_id) for subsequent business logic operations
 * - Integrates with user token management system for session control
 * - Supports mobile app authentication with device-specific tokens
 * - Enables secure access to user content, settings, and premium features
 *
 * Key Features:
 * - JWT token validation using HMAC SHA-256 algorithm
 * - Database-backed token verification for enhanced security
 * - User session management with token storage and validation
 * - Automatic user context establishment for authenticated requests
 * - Comprehensive error handling with proper HTTP status codes
 * - Integration with user token lifecycle management
 *
 * Security Implementation:
 * - JWT signature validation using application secret key
 * - Database token verification to prevent token reuse after logout
 * - Proper error responses to prevent information disclosure
 * - Request context isolation between users
 * - Protection against token tampering and replay attacks
 *
 * Authentication Flow:
 * 1. Extract JWT token from Authorization header
 * 2. Verify token signature and decode payload
 * 3. Query database to confirm token is still valid/active
 * 4. Extract user_id from validated token
 * 5. Set req.user_id for use in subsequent middleware/controllers
 * 6. Call next() to proceed to protected route handler
 *
 * Default Behaviors:
 * - Requires Authorization header with valid JWT token
 * - Returns 401 Unauthorized for missing or invalid tokens
 * - Establishes user context via req.user_id for authenticated requests
 * - Blocks access to protected resources without valid authentication
 *
 * Error Handling:
 * - Missing Authorization header: 401 with TOKENREQUIRED message
 * - Invalid/expired token: 401 with TOKENNOTMATCHED message
 * - Database token validation failure: 401 with INVALIDHEADERS message
 * - JWT verification errors: 401 with appropriate error message
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

const UserToken = require('../database/models').tbl_user_tokens;
const JWT = require('jsonwebtoken');
const constant = require('../config/constant');
const message = require('../config/web.message');
const status = require('../config/status');

/**
 * User Authentication Middleware Function
 *
 * Express middleware function that validates JWT tokens and establishes
 * authenticated user context for protected API endpoints.
 *
 * @param {Object} req - Express request object containing headers and request data
 * @param {Object} res - Express response object for sending responses
 * @param {Function} next - Express next function to continue to next middleware/route
 *
 * @throws {401} Unauthorized - When token is missing, invalid, or expired
 * @throws {500} Internal Server Error - When unexpected errors occur during validation
 *
 * Request Headers Required:
 * - Authorization: JWT token string for user authentication
 *
 * Request Context Established:
 * - req.user_id: Authenticated user's ID for subsequent operations
 *
 * Usage Example:
 * ```javascript
 * router.get('/protected-route', userAuthentication, (req, res) => {
 *   // req.user_id is now available and verified
 *   const userId = req.user_id;
 *   // ... protected route logic
 * });
 * ```
 */
exports.userAuthentication = async (req, res, next) => {
  if (req.headers.authorization) {
    try {
      /**
       * JWT Token Verification Process
       *
       * Verify the JWT token signature and decode the payload using the
       * application's secret key and specified algorithm. This ensures
       * the token hasn't been tampered with and was issued by our application.
       */
      let jwtGetUserDetail = await JWT.verify(
        req.headers.authorization,
        constant.JWTAPPTOKEN.secret,
        { algorithm: constant.JWTAPPTOKEN.algo }
      );

      /**
       * Database Token Validation
       *
       * Verify that the token exists in our database and is still active.
       * This prevents the use of tokens after logout or account deactivation
       * and provides an additional layer of security beyond JWT verification.
       */
      let getUserAuthDetails = await UserToken.findOne({
        where: {
          user_id: jwtGetUserDetail.user_id,
          token: req.headers.authorization,
        },
      });

      /**
       * Authentication Success Path
       *
       * If both JWT verification and database validation succeed,
       * establish the user context and proceed to the protected route.
       */
      if (getUserAuthDetails) {
        req.user_id = getUserAuthDetails.user_id;
        next();
      } else {
        /**
         * Database Token Validation Failure
         *
         * JWT was valid but token not found in database.
         * This could indicate:
         * - User has logged out (token removed from database)
         * - Token has been revoked/expired
         * - Account has been deactivated
         */
        res.status(status.status.UNAUTHORIZEDUSER).send({
          data: {},
          message: message.INVALIDHEADERS,
          status: status.ERROR,
        });
      }
    } catch (error) {
      /**
       * JWT Verification Error Handling
       *
       * Handle various JWT-related errors such as:
       * - Invalid signature (token tampered with)
       * - Expired token (past expiration time)
       * - Malformed token (invalid format)
       * - Algorithm mismatch
       */
      console.log('TCL: exports.authenticationApi -> error', error);
      res.status(status.status.UNAUTHORIZEDUSER).send({
        data: {},
        message: message.appMessage.TOKENNOTMATCHED,
        status: status.status.ERROR,
      });
    }
  } else {
    /**
     * Missing Authorization Header
     *
     * Request doesn't contain the required Authorization header.
     * This is the most common authentication failure case for
     * clients that haven't included proper authentication.
     */
    res.status(status.status.UNAUTHORIZEDUSER).send({
      data: {},
      message: message.appMessage.TOKENREQUIRED,
      status: status.status.ERROR,
    });
  }
};

/**
 * Additional Security Considerations for Future Enhancement:
 *
 * 1. Rate Limiting: Implement rate limiting to prevent brute force attacks
 * 2. Token Refresh: Add token refresh mechanism for better user experience
 * 3. Device Tracking: Log and track authentication attempts by device
 * 4. Session Management: Implement proper session timeout and cleanup
 * 5. Audit Logging: Log all authentication attempts for security monitoring
 * 6. Multi-Factor Authentication: Add MFA support for enhanced security
 * 7. Token Blacklisting: Implement token blacklist for immediate revocation
 * 8. IP-based Restrictions: Add IP-based access controls where appropriate
 *
 * Current Security Strengths:
 * - JWT signature validation prevents token tampering
 * - Database validation prevents reuse of revoked tokens
 * - Proper error handling prevents information disclosure
 * - User context isolation through req.user_id
 * - HMAC SHA-256 provides strong cryptographic security
 */
