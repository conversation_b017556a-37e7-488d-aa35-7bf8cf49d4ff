/*
 * Summary:     staticPages.services file for handling all USER related actions.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules,models and constants for configuration */
const StaticContent = require("../../database/models").tbl_static_contents;

module.exports = {
  /* terms_of_use */
  async terms_of_use(req, res) {
    var find_content = await StaticContent.findOne({
      where: {
        static_contents_id: 2,
      },
    });
    if (!find_content) {
      return 0;
    } else {
      return find_content;
    }
  },

  /* terms_of_use */
  async about_us(req, res) {
    var find_content = await StaticContent.findOne({
      where: {
        static_contents_id: 1,
      },
    });
    if (!find_content) {
      return 0;
    } else {
      return find_content;
    }
  },

  /* help */
  async help(req, res) {
    var find_content = await StaticContent.findOne({
      where: {
        static_contents_id: 3,
      },
    });
    if (!find_content) {
      return 0;
    } else {
      return find_content;
    }
  },

  /* privacy-policy */
  async privacy_policy(req, res) {
    var find_content = await StaticContent.findOne({
      where: {
        static_contents_id: 4,
      },
    });
    if (!find_content) {
      return 0;
    } else {
      return find_content;
    }
  },
};
