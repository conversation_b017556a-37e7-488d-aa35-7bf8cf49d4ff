/**
 * Content Items Entity Model - KyuleBag Content Management
 *
 * This Sequelize model defines the core content item entity for the KyuleBag platform,
 * managing all types of user-generated content including text, images, videos, documents,
 * links, audio files, and other media. It serves as the central entity for content
 * storage, organization, and management operations.
 *
 * Business Logic Connection:
 * - Central entity for all user content storage and management
 * - Supports multi-format content types (text, media, documents, links)
 * - Enables content organization through tagging and categorization
 * - Provides privacy controls and sharing functionality
 * - Integrates with AI/ML services for content analysis and processing
 * - Manages content lifecycle including soft delete and recovery
 * - Supports collaborative features through notes and sharing
 *
 * Key Features:
 * - Multi-format content support (image, video, audio, pdf, doc, text, link, contact, location)
 * - Privacy controls with is_private flag for sensitive content
 * - Bookmark functionality for important content organization
 * - Soft delete with recovery options for data protection
 * - Upload status tracking for large file operations
 * - Content versioning and modification tracking
 * - Integration with cloud storage for media files
 *
 * Content Types Supported:
 * - text: Plain text notes and content
 * - image: Photos, screenshots, and image files
 * - video: Video recordings and uploaded video files
 * - audio: Audio recordings and music files
 * - pdf: PDF documents and files
 * - doc: Word documents and text files
 * - link: Web URLs and bookmarked links
 * - contact: Contact information and vCards
 * - location: Geographic locations and addresses
 * - create_doc: Generated documents from templates
 *
 * Relationships:
 * - Many-to-One with tbl_users (content ownership)
 * - One-to-Many with tbl_item_notes (content annotations)
 * - Many-to-Many with tbl_tags via tbl_item_tags (content organization)
 * - One-to-Many with tbl_media (associated media files)
 * - One-to-One with tbl_sharing_details (content sharing configuration)
 * - One-to-One with tbl_item_ocr_labels (AI/OCR analysis results)
 *
 * Default Behaviors:
 * - Default content type: "text" for new items
 * - Default delete type: "NORMAL" for standard deletion
 * - Privacy and bookmark flags default to false (public, not bookmarked)
 * - Upload flag defaults to true (content is uploaded/ready)
 * - Automatic timestamp tracking for content creation
 *
 * Privacy and Security:
 * - Private content accessible only to owner
 * - Sharing controls through separate sharing_details relationship
 * - Soft delete functionality preserves data for recovery
 * - Upload status tracking prevents incomplete content access
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

'use strict';
const { Model } = require('sequelize');

/**
 * Export Content Items Model Definition Function
 *
 * This function is called by Sequelize to initialize the items model with
 * the database connection and DataTypes. It defines the model structure,
 * field validations, associations, and default values.
 *
 * @param {Object} sequelize - Sequelize instance for database connection
 * @param {Object} DataTypes - Sequelize data types for field definitions
 * @returns {Object} Initialized tbl_items model class
 */
module.exports = (sequelize, DataTypes) => {
  /**
   * Content Items Model Class Definition
   *
   * Extends Sequelize Model class to define content item entity structure,
   * associations, and business logic methods for content management.
   */
  class tbl_items extends Model {
    /**
     * Define Model Associations
     *
     * Establishes relationships between Items model and related entities.
     * These associations enable complex content queries, organization features,
     * and content relationship management. Called automatically by Sequelize
     * after all models are loaded.
     *
     * @param {Object} models - Collection of all loaded Sequelize models
     */
    static associate(models) {
      // Item Notes Relationship
      // One item can have multiple notes/annotations for detailed documentation
      tbl_items.hasMany(models.tbl_item_notes, {
        targetKey: 'item_id', // Primary key in items table
        foreignKey: 'item_id', // Foreign key in notes table
        as: 'items_note_lists', // Alias for query operations
        // Enables: item.getItemsNoteLists(), item.items_note_lists
      });

      // Item Tags Relationship (Many-to-Many through junction table)
      // One item can have multiple tags for organization and categorization
      tbl_items.hasMany(models.tbl_item_tags, {
        targetKey: 'item_id', // Primary key in items table
        foreignKey: 'item_id', // Foreign key in junction table
        as: 'items_tag_lists', // Alias for query operations
        // Enables: item.getItemsTagLists(), item.items_tag_lists
      });

      // Media Files Relationship
      // One item can have multiple associated media files (images, videos, etc.)
      tbl_items.hasMany(models.tbl_media, {
        targetKey: 'item_id', // Primary key in items table
        foreignKey: 'item_id', // Foreign key in media table
        as: 'image_details', // Alias for query operations (legacy naming)
        // Enables: item.getImageDetails(), item.image_details
        // Note: Despite alias name, this includes all media types, not just images
      });

      // Content Sharing Configuration Relationship
      // One item has one sharing configuration defining access permissions
      tbl_items.hasOne(models.tbl_sharing_details, {
        targetKey: 'item_id', // Primary key in items table
        foreignKey: 'item_id', // Foreign key in sharing table
        as: 'item_sharing_details', // Alias for query operations
        // Enables: item.getItemSharingDetails(), item.item_sharing_details
      });

      // AI/OCR Analysis Results Relationship
      // One item has one OCR/AI analysis result containing extracted text and labels
      tbl_items.hasOne(models.tbl_item_ocr_labels, {
        targetKey: 'item_id', // Primary key in items table
        foreignKey: 'item_id', // Foreign key in OCR labels table
        as: 'item_ocr_labels', // Alias for query operations
        // Enables: item.getItemOcrLabels(), item.item_ocr_labels
      });

      // Additional potential associations:
      // - belongsTo with tbl_users (content ownership)
      // - hasMany with tbl_activity_logs (item-related actions)
      // - hasMany with tbl_item_versions (content versioning)
    }
  }

  /**
   * Content Items Model Field Definitions
   *
   * Defines all database fields, data types, constraints, and default values
   * for the content items entity. Each field includes comprehensive business
   * logic documentation and usage patterns.
   */
  tbl_items.init(
    {
      /**
       * Primary Key Field
       * Unique string identifier for each content item
       * Uses string format to support custom ID generation patterns
       */
      item_id: {
        allowNull: false, // Required field
        // autoIncrement: true,         // Disabled - using custom string IDs
        primaryKey: true, // Primary key constraint
        type: DataTypes.STRING, // String type for flexible ID formats
        comment: 'Unique identifier for content items (custom string format)',
      },

      /**
       * Content Ownership
       * Foreign key linking content to the user who created/owns it
       */
      user_id: {
        allowNull: false, // Required field - all content must have owner
        type: DataTypes.INTEGER, // References tbl_users.user_id
        comment: 'Foreign key to users table - identifies content owner',
      },

      /**
       * Content Metadata Fields
       * Basic information about the content item
       */
      title: {
        type: DataTypes.TEXT, // Unlimited length text for long titles
        comment: 'Content item title or name for identification and display',
      },

      description: {
        type: DataTypes.TEXT, // Unlimited length text for detailed descriptions
        comment: 'Detailed description or content body for text-based items',
      },

      /**
       * Content Type Classification
       * Defines the format and handling method for the content
       */
      item_type: {
        type: DataTypes.ENUM(
          'image', // Photo/image files (JPEG, PNG, GIF, etc.)
          'video', // Video files (MP4, AVI, MOV, etc.)
          'audio', // Audio files (MP3, WAV, AAC, etc.)
          'pdf', // PDF documents
          'doc', // Word documents and text files
          'text', // Plain text content and notes
          'link', // Web URLs and bookmarked links
          'contact', // Contact information and vCards
          'location', // Geographic locations and addresses
          'create_doc' // Generated documents from templates
        ),
        defaultValue: 'text', // Default to text type for new items
        comment: 'Content type classification for processing and display logic',
      },

      /**
       * Deletion Management
       * Controls how content deletion is handled across different contexts
       */
      delete_type: {
        type: DataTypes.ENUM(
          'NORMAL', // Standard deletion through app interface
          'MOBILE', // Deletion initiated from mobile device
          'CLOUD' // Deletion from cloud storage cleanup
        ),
        defaultValue: 'NORMAL', // Default to normal deletion type
        comment: 'Deletion context for tracking deletion sources and methods',
      },

      /**
       * Content Organization Flags
       * User-defined flags for content organization and priority
       */
      is_bookmarked: {
        type: DataTypes.BOOLEAN, // True/false bookmark status
        defaultValue: false, // Not bookmarked by default
        comment:
          'Bookmark flag for marking important or frequently accessed content',
      },

      is_private: {
        type: DataTypes.BOOLEAN, // True/false privacy status
        defaultValue: false, // Public by default
        comment: 'Privacy flag - private content accessible only to owner',
      },

      /**
       * Data Lifecycle Management
       * Controls content availability and lifecycle state
       */
      is_deleted: {
        type: DataTypes.BOOLEAN, // True/false deletion status
        defaultValue: false, // Active by default
        comment:
          'Soft delete flag for data retention and recovery (false=active, true=deleted)',
      },

      is_upload: {
        type: DataTypes.BOOLEAN, // True/false upload completion status
        defaultValue: true, // Uploaded by default
        comment:
          'Upload completion flag - false indicates incomplete/in-progress upload',
      },

      /**
       * Timestamp Fields
       * Track content creation and upload timing for organization and analytics
       */
      uploadedAt: {
        allowNull: false, // Required field
        type: DataTypes.DATE, // Date/time type with timezone support
        comment:
          'Timestamp when content was uploaded/created - used for sorting and organization',
      },

      // Note: createdAt and updatedAt are disabled in model options
      // uploadedAt serves as the primary timestamp for content organization
    },
    {
      sequelize, // Sequelize instance
      createdAt: false, // Disable automatic createdAt timestamp
      updatedAt: false, // Disable automatic updatedAt timestamp
      modelName: 'tbl_items', // Model name for Sequelize operations
      tableName: 'tbl_items', // Explicit table name in database
      comment:
        'Core content entity managing all types of user-generated content and media',

      // Database Indexes for Performance
      indexes: [
        {
          // Index on user_id for efficient user content queries
          fields: ['user_id'],
          name: 'idx_items_user_id',
        },
        {
          // Index on item_type for content type filtering
          fields: ['item_type'],
          name: 'idx_items_type',
        },
        {
          // Composite index for user + privacy queries
          fields: ['user_id', 'is_private', 'is_deleted'],
          name: 'idx_items_user_privacy',
        },
        {
          // Index on uploadedAt for chronological sorting
          fields: ['uploadedAt'],
          name: 'idx_items_uploaded_at',
        },
      ],
    }
  );

  /**
   * Return initialized Content Items model
   * Makes the model available for import and use throughout the application
   * for content management, organization, and business logic operations
   */
  return tbl_items;
};
