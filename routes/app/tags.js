/*
 * Summary:     tags.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const user_auth = require("../../middleware/auth").userAuthentication;
const validators = require("../../assets/validators");
const tags_controller = require("../../controllers/app/tags.controller");
const item_tags_controller = require("../../controllers/app/item.tags.controller");
router.post("/list", user_auth, tags_controller.list);
router.post("/add", user_auth, validators.app.tags.add, tags_controller.add);
router.post(
  "/item_tags",
  user_auth,
  validators.app.item_tags.add,
  item_tags_controller.add
);
router.put("/edit", user_auth, validators.app.tags.edit, tags_controller.edit);
router.delete(
  "/delete",
  user_auth,
  validators.app.tags.delete,
  tags_controller.delete
);

module.exports = router;
