const Items = require("../../database/models").tbl_items;
const Tags = require("../../database/models").tbl_tag_names;
const Item_tags = require("../../database/models").tbl_item_tags;
const Activity_logs = require("../../database/models").tbl_activity_logs;
const Notes = require("../../database/models").tbl_item_notes;
const Images = require("../../database/models").tbl_media;
const Sharing_details = require("../../database/models").tbl_sharing_details;
const OCRLabels = require("../../database/models").tbl_item_ocr_labels;
const constant = require("../../config/constant");
const image_size = require("../../helper/general.helper");

module.exports = {
  /* add */
  async item_tag_add(req, res) {
    let { item_id, tag_name_id } = req.body;

    var tag_ids = JSON.parse(tag_name_id); // parsing the array string [1,2,3,4....]
    let tagType;
    if (tag_ids.length === 0) {
      tagType = 1;
    } else if (tag_ids.length === 1) {
      tagType = 2;
    } else if (tag_ids.length > 1) {
      tagType = 3;
    }

    var find_items = await Items.findOne({
      where: {
        item_id: item_id,
        user_id: req.user_id, // adding user at the time of signin...
        // is_deleted: 0,
      },
    });
    //  Add tags if item id and tag id exists...
    if (find_items) {
      await Item_tags.destroy({
        where: {
          item_id: item_id,
        },
      });
      for (let index = 0; index < tag_ids.length; index++) {
        var find_tags = await Tags.findOne({
          where: {
            user_id: req.user_id,
            tag_name_id: tag_ids[index],
          },
        });
        if (find_tags !== null) {
          await Item_tags.destroy({
            where: {
              item_id: find_items.item_id,
              tag_name_id: find_tags.tag_name_id,
            },
          });

          await Item_tags.create({
            item_id: find_items.item_id,
            tag_name_id: find_tags.tag_name_id,
          });
        }
        const itemType = find_items.dataValues.item_type.charAt(0).toUpperCase() + find_items.dataValues.item_type.substr(1).toLowerCase();

        var item_activity_log = await Activity_logs.create({
          user_id: req.user_id,
          title: "Add Tag",
          // description:`Tags get added into item.${find_tags.tag_name_id}`
          // description: "Tags get added into item.",
          description: `Tags get added to ${itemType}.`,
        });
      }
    } else {
      await Item_tags.create({
        item_id: find_items.item_id,
        tag_name_id: find_tags.tag_name_id,
      });
      var item_activity_log = await Activity_logs.create({
        user_id: req.user_id,
        title: "Add Tag",
        // description:`Tags get added into item ${find_items.item_id}`
        description: `Tags get added into item.`,
      });
    }

    let item = await Items.findOne({
      where: {
        is_deleted: 0,
        item_id: item_id,
      },
      include: [
        {
          model: Images,
          as: "image_details",
          attributes: {
            exclude: ["createdAt", "updatedAt"],
          },
        },
        {
          model: Sharing_details,
          as: "item_sharing_details",
          attributes: {
            exclude: ["item_id"],
          },
        },
        {
          model: Notes,
          as: "items_note_lists",
          attributes: {
            exclude: ["item_id"],
          },
        },
        {
          model: Item_tags,
          as: "items_tag_lists",
          attributes: {
            exclude: ["item_id", "id"],
          },

          include: [
            {
              model: Tags,
              as: "items_tag_names",
              attributes: {
                exclude: ["tag_name_id", "user_id"],
              },
            },
          ],
        },
        {
          model: OCRLabels,
          attributes: ["ocr_label", "id"],
          as: "item_ocr_labels",
        },
      ],
      attributes: [
        "item_id",
        "user_id",
        "title",
        "description",
        "item_type",
        "is_bookmarked",
        "is_private",
        "delete_type",
        "is_deleted",
        "is_upload",
        "createdAt",
      ],
    });

    let media_details = item.dataValues.image_details;
    if (media_details !== []) {
      item.dataValues.size = "0";
    }
    media_details.map((data) => {
      // data.dataValues.media =
      //   constant.GCP_URL +
      //   constant.GCP_BUCKET_NAME +
      //   "/" +
      //   constant.GCP_BUCKET_FOLDER +
      //   constant.GCP_USER_FOLDER +
      //   data.dataValues.user_id +
      //   "/" +
      //   constant.GCP_ITEM_FOLDER +
      //   data.dataValues.item_id +
      //   "/" +
      //   data.dataValues.media;
      data.dataValues.mediaName = data.dataValues.media;
      let imgs = image_size.formatSize(data.dataValues.size * 1000);
      let b = Math.round(Number(imgs.split(" ")[0]) * 100) / 100;

      let size = b.toString() + " " + imgs.split(" ")[1];
      item.dataValues.size = data.dataValues.size.toString();
      data.dataValues.size = size;
    });

    item.dataValues.items_tag_lists.map((data, index) => {
      if (
        data.createdAt &&
        data.dataValues.is_deleted === false &&
        data.dataValues.items_tag_names !== null
      ) {
        data.dataValues.createdAt = data.dataValues.createdAt
          .toISOString()
          .substr(0, 19)
          .replace("T", " ");
        data.dataValues.updatedAt = data.dataValues.updatedAt
          .toISOString()
          .substr(0, 19)
          .replace("T", " ");

        if (data.dataValues.items_tag_names !== null) {
          data.dataValues.tag_name = data.items_tag_names.dataValues.tag_name;
        }
      }
    });

    item.dataValues.items_tag_lists.map((data1) => {
      delete data1.dataValues.items_tag_names;
    });

    // delete item.dataValues.items_note_lists;

    // item = await image_size.arrayMediaURL(item)

    return { item, tagType };
  },
};
