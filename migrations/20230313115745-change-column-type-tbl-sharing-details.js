'use strict';

module.exports = {
	async up(queryInterface, Sequelize) {
		return Promise.all([
			queryInterface.changeColumn(
				'tbl_sharing_details',
				'link',
				{
					type: Sequelize.TEXT,
				},
				{
					logging: true,
				}
			),
		]);
	},

	async down(queryInterface, Sequelize) {
		return Promise.all([
			queryInterface.changeColumn(
				'tbl_sharing_details',
				'link',
				{
					type: Sequelize.STRING,
				},
				{
					logging: true,
				}
			),
		]);
	},
};
