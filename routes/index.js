/**
 * Main Routing Index - Central Route Distribution
 *
 * This file serves as the central routing hub for the KyuleBag API, organizing and
 * distributing incoming HTTP requests between the client/mobile application routes
 * and the content management system (admin) routes. It establishes the dual API
 * architecture that serves both end users and administrative functions.
 *
 * Business Logic Connection:
 * - Enables dual API structure serving different user types and use cases
 * - Separates client application routes from administrative/management routes
 * - Provides clear API endpoint organization for mobile app and web dashboard
 * - Establishes consistent URL patterns and API versioning structure
 * - Enables different authentication and authorization strategies per API type
 * - Supports scalable routing architecture for growing application features
 *
 * API Architecture:
 * - `/app-api/*` - Client/Mobile Application Routes
 *   - User authentication and profile management
 *   - Content creation, editing, and organization
 *   - File uploads and media management
 *   - Social features and content sharing
 *   - Subscription and payment processing
 *
 * - `/cms-api/*` - Content Management System Routes
 *   - Admin authentication and user management
 *   - System analytics and reporting
 *   - User account administration
 *   - Content moderation and management
 *   - Subscription plan management
 *   - System configuration and settings
 *
 * Key Features:
 * - Centralized route organization and management
 * - Clear separation of concerns between user types
 * - Scalable architecture for adding new route groups
 * - Consistent API endpoint patterns and conventions
 * - Welcome endpoint for API health checking and documentation
 * - Environment-aware responses for debugging and monitoring
 *
 * Default Behaviors:
 * - Root endpoint (/) returns welcome message with environment info
 * - All app routes prefixed with `/app-api/`
 * - All CMS routes prefixed with `/cms-api/`
 * - Uses global STATUS_CODES for consistent response formatting
 * - Includes application name and environment in welcome response
 *
 * Route Organization:
 * - Modular route imports from dedicated directories
 * - Express Router middleware for route grouping
 * - Consistent error handling across all route groups
 * - Support for middleware chaining and authentication
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

/**
 * Dependencies and Route Imports
 * Import Express framework and route modules for different API sections
 */

// Express framework for routing functionality
const express = require('express');

// Client/Mobile application routes module
const app_router = require('./app'); // redirect app/index.js

// Content Management System (admin) routes module
const cms_router = require('./cms'); // redirect cms/index.js

// Express application instance for middleware setup
const app = express();

/**
 * Main Route Configuration Function
 *
 * This function is called from app.js to set up all application routes.
 * It configures the main route structure, establishes API endpoint prefixes,
 * and provides a welcome endpoint for API documentation and health checks.
 *
 * @param {Object} app - Express application instance from main app.js
 *
 * Route Structure Established:
 * - GET  /                 - API welcome and health check endpoint
 * - ALL  /app-api/*        - Client application routes (mobile, web)
 * - ALL  /cms-api/*        - Admin panel routes (dashboard, management)
 *
 * Usage Example:
 * ```javascript
 * // From app.js
 * const index_router = require("./routes");
 * index_router(app);  // This function is called to set up routes
 * ```
 */
module.exports = (app) => {
  /**
   * API Welcome and Health Check Endpoint
   *
   * Provides a simple endpoint for API health checking, documentation,
   * and environment verification. Returns basic API information including
   * environment status and application name for monitoring and debugging.
   *
   * @route GET /
   * @returns {Object} JSON response with welcome message and environment info
   *
   * Response Format:
   * ```json
   * {
   *   "message": "Welcome to development KyuleBag",
   *   "status": 200
   * }
   * ```
   *
   * Use Cases:
   * - API health monitoring and uptime checking
   * - Environment verification during deployment
   * - Basic API documentation and discovery
   * - Load balancer health probe endpoint
   */
  app.get('/', (req, res) => {
    res
      .status(STATUS_CODES.SUCCESSSTATUS)
      .send('Welcome to ' + process.env.NODE_ENV + ' ' + process.env.APPNAME);
  });

  /**
   * Client/Mobile Application API Routes
   *
   * Mount all client-facing routes under the `/app-api/` prefix.
   * These routes handle end-user functionality including authentication,
   * content management, file uploads, and user interactions.
   *
   * @route ALL /app-api/*
   * @middleware app_router - Handles all client application routes
   *
   * Route Categories Included:
   * - /app-api/user/*           - User authentication and profile
   * - /app-api/items/*          - Content item management (CRUD)
   * - /app-api/tags/*           - Content tagging and organization
   * - /app-api/notes/*          - Item annotations and notes
   * - /app-api/static/*         - Static content and pages
   * - /app-api/trash/*          - Deleted item management
   * - /app-api/activity-logs/*  - User activity tracking
   * - /app-api/announcements/*  - System announcements
   * - /app-api/contact-us/*     - Contact and support
   * - /app-api/webhook/*        - Webhook integrations
   *
   * Authentication:
   * - Most routes require JWT authentication via Authorization header
   * - Public routes available for registration and password reset
   * - Device-specific authentication for mobile apps
   */
  app.use('/app-api', app_router);

  /**
   * Content Management System (Admin) API Routes
   *
   * Mount all administrative routes under the `/cms-api/` prefix.
   * These routes handle administrative functions including user management,
   * system analytics, content moderation, and platform configuration.
   *
   * @route ALL /cms-api/*
   * @middleware cms_router - Handles all admin panel routes
   *
   * Route Categories Included:
   * - /cms-api/admin/*              - Admin authentication and management
   * - /cms-api/admin-role/*         - Role-based access control
   * - /cms-api/user/*               - User account administration
   * - /cms-api/dashboard/*          - Analytics and reporting
   * - /cms-api/static-content/*     - Static content management
   * - /cms-api/email-notification/* - Email system management
   * - /cms-api/announcements/*      - System announcement management
   * - /cms-api/comments/*           - Comment moderation
   * - /cms-api/subscription/*       - Subscription plan management
   * - /cms-api/payment-transaction/* - Payment processing oversight
   * - /cms-api/ai-subscription/*    - AI service plan management
   *
   * Authentication:
   * - Admin-level authentication required for all routes
   * - Role-based access control for different admin functions
   * - Session management with admin-specific tokens
   * - Audit logging for all administrative actions
   */
  app.use('/cms-api', cms_router);
};

/**
 * Additional Route Organization Benefits:
 *
 * 1. Scalable Architecture:
 *    - Easy to add new route groups (e.g., /partner-api/, /webhook-api/)
 *    - Clear separation allows independent development of different API sections
 *    - Enables different versioning strategies per API type
 *
 * 2. Security Benefits:
 *    - Different authentication strategies per route group
 *    - Isolated error handling and logging per API section
 *    - Separate rate limiting and security policies
 *
 * 3. Development Efficiency:
 *    - Clear code organization improves developer productivity
 *    - Modular structure enables team specialization
 *    - Easier testing and deployment of specific API sections
 *
 * 4. Monitoring and Analytics:
 *    - Route-level metrics and performance monitoring
 *    - Separate logging and error tracking per API type
 *    - Usage analytics for different user types
 *
 * 5. API Documentation:
 *    - Clear API structure for documentation generation
 *    - Logical grouping for API consumer understanding
 *    - Consistent patterns across all endpoint groups
 */
