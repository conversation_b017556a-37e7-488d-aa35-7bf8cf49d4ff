const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const common_service = require("../../services/cms/common.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* login-in */

  async logIn(req, res) {
    const error = validationResult(req);
    if (!error.isEmpty()) {
      let message = '';
      displayOnlyFirstError(error.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let admin_login = await common_service.logIn(req, res);
      if (admin_login === 1) {
        return res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.INCORRECTPASSWORD,
          status: status.ERROR,
        });
      }
      if (admin_login === 0) {
        return res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.EMAILNOTFOUND,
          status: status.ERROR,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: admin_login,
        message: message.LOGNINSUCCESS,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log('logIn -> error', error);
      return res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // logout
  async logOut(req, res) {
    try {
      await common_service.lognOut(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: [],
        status: status.SUCCESS,
        message: message.LOGOUTSUCCESS,
      });
    } catch (error) {
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  //  forgot password
  async forgotPassword(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let forgot_password_res = await common_service.forgotPassword(req, res);

      if (forgot_password_res === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          status: status.ERROR,
          message: message.EMAILNOTFOUND,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: forgot_password_res,
          status: status.SUCCESS,
          message: message.EMAILSENTSUCCESSFULLY,
        });
      }
    } catch (error) {
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  //  reset password
  async resetPassword(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let admin = await common_service.resetPassword(req, res);
      if (admin === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.INCORRECTOLDPASSWORD,
          status: status.ERROR,
        });
      } else if (admin === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.CONFIRMPASSWORDNOTMATCH,
          status: status.ERROR,
        });
      } else if (admin === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.PASSWORDRESET,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.ADMINNOTFOUND,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log('resetPassword -> error', error);

      res.status(status.INTERNALSERVERERRORSTATUS).send({
        //response on internal server error
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // edit-profile
  async updateProfile(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let edit_admin = await common_service.updateProfile(req, res);

      // response of update admin
      res.status(status.SUCCESSSTATUS).send({
        data: edit_admin,
        message: message.ADMINUPDATED,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log('updateProfile -> error', error);

      // response on internal server error

      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  // change-password
  async changePassword(req, res) {
    try {
      let change_password = await common_service.changePassword(req, res);
      if (change_password) {
        res.status(status.SUCCESSSTATUS).send({
          // response on successfully change password
          data: [],
          message: message.PASSWORDCHANGED,
          status: status.SUCCESS,
        });
      } else {
        // response on old passwprd mis-match
        res.status(status.SUCCESSSTATUS).send({
          dat: [],
          message: message.INCORRECTOLDPASSWORD,
          status: status.ERROR,
        });
      }
    } catch (error) {
      //response on internal server error
      console.log('changePassword -> error', error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
