DB_CONNECTION=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=kyulebag_ind
DB_USERNAME=kyulebag_ind
DB_PASSWORD=z$b35vj13E9G*2uCz

NODE_ENV = 'development'

APP_PORT = 7832

SHOULD_RUN_ON_HTTP=true

S3_ACCESSKEY= ********************
S3_SECRETKEY= AfC4i1yGZQTc+fhNcy8uWAEHgU6FhgM4pEGMqD9I
S3_REGION= us-east-2

AWS_ACCESS_KEY_ID= ********************
AWS_SECRET_ACCESS_KEY= AfC4i1yGZQTc+fhNcy8uWAEHgU6FhgM4pEGMqD9I
AWS_REGION= us-east-2

S3_BUCKET_NAME=kyulebag
S3_AWS_URL=https://kyulebag.s3.us-east-2.amazonaws.com/

GCP_PROJECT_ID=kyulebag-95d98
GCP_BUCKET_NAME=kyulebag-ind
GCP_REGION=asia-south1
GCP_URL=https://storage.googleapis.com/
# GCP_URL=https://storage.cloud.google.com/
GCP_BUCKET_FOLDER=development/
GCP_ADMIN_FOLDER=admin/
GCP_USER_FOLDER=user/
GCP_ITEM_FOLDER=items/
GCP_STATIC_FOLDER=statics/

PLAY_CONSOLE_PACKAGE_NAME=com.app.kyulebag

# MAIL_FROM = <EMAIL>
MAIL_FROM = <EMAIL>
MAIL_FROM_AUTH = <EMAIL>
# MAIL_PASSWORD = vnbvxluvusnpmwyb
MAIL_PASSWORD = P1anetearth
MAIL_SERVICE = gmail
# MAIL_HOST = smtp.gmail.com
MAIL_HOST = smtpout.secureserver.net
MAIL_PORT = 465
MAIL_METHOD = SMTP
MAIL_SECURE = true
MAIL_SECURE_CONNECTION = false
MAIL_REQUIRE_TLS = true
MAIL_DEBUG = true

WEBURL=https://dev-api.kyulebag.com/app-api/
WEBPAGEURL=https://dev-webpage.kyulebag.com/
CMSURL=https://dev-api.kyulebag.com/
# LOGOURL=https://storage.cloud.google.com/kyulebag/kyulebag_logo.png
LOGOURL=https://storage.cloud.google.com/kyulebag-ind/kyulebag_logo.png
# WEBURL=https://kyulebag-api.apps.openxcell.dev/app-api/
# CMSURL=https://kyulebag-api.apps.openxcell.dev/
# LOGOURL=https://kyulebag.s3.us-east-2.amazonaws.com/kyulebag/kyulebag_logo.png
APPNAME=kyulebag

APPCOLOR=#875739
