var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

/* require for Controller */
const subscription_controller = require("../../controllers/cms/subscription.controller");

/*require for validators */
const validators = require('../../assets/validators');

router.post("/add", authentication, /**validators.cms.announcement.addStoragePlan,*/ subscription_controller.addStoragePlan);
router.post("/list", authentication, subscription_controller.listStoragePlan);
router.put("/edit/:id", authentication, /**validators.cms.staticContent.updateStaticPage,*/ subscription_controller.editStoragePlan);
router.put("/delete/:id", authentication, subscription_controller.deleteStoragePlan);
router.post("/retrive/:id", authentication, subscription_controller.retriveStoragePlan);
router.post("/status/:id", authentication, subscription_controller.changeStatusPlan);

module.exports = router;