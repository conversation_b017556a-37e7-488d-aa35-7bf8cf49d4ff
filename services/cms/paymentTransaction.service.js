require("dotenv").config();
const PaymentTransaction = require("../../database/models").tbl_user_subscriptions;
const AiPaymentTransaction = require("../../database/models").tbl_user_ai_subscriptions;
const User = require("../../database/models").tbl_users;
// const Plan = require("../../database/models").tbl_subscriptions;
const Plan = require("../../database/models").tbl_new_subscriptions;
const AiPlan = require("../../database/models").tbl_new_ai_subscriptions;

const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");

module.exports = {
  async listPaymentTransaction(req, res) {
    let { sort_by, search, order, status } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search = search === undefined ? "" : search;
    let searchObj = {
      where: {
        [Op.or]: [
          {
            status: { [Op.like]: `%${search}%` },
          },
          //   {
          //     price: { [Op.like]: `%${search}%` },
          //   },
          //   {
          //     duration: { [Op.like]: `%${search}%` },
          //   }
        ],
      },
    };

    if (status) {
      // searchObj.where.status = parseInt(status);
      searchObj.where.status = req.body.status;
    }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* storage plan find query */
    let transaction_list = await PaymentTransaction.findAndCountAll({
      where: searchObj.where,
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,

      include: [
        {
          model: User,
          attributes: ["first_name", "email", "phone", "country_code"],
        },
        {
          model: Plan,
          attributes: [/*"price",*/ "price_currency_code", "subscriptions_benefits", "duration"]
        },
      ],
    });
    var string = JSON.stringify(transaction_list);
    const obj = JSON.parse(string);

    for (let index = 0; index < obj.rows.length; index++) {
      obj.rows[index].name = obj?.rows[index]?.tbl_user?.first_name ? obj?.rows[index]?.tbl_user?.first_name : null;
      obj.rows[index].email = obj?.rows[index]?.tbl_user?.email ? obj?.rows[index]?.tbl_user?.email : null;
      // obj.rows[index].phone = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.phone : null;
      // obj.rows[index].country_code = obj?.rows[index]?.tbl_user?.country_code ? obj?.rows[index]?.tbl_user?.country_code : null;
      // obj.rows[index].amount = obj?.rows[index]?.tbl_new_subscription?.price ? obj?.rows[index]?.tbl_new_subscription?.price : null;
      obj.rows[index].storage = obj?.rows[index]?.tbl_new_subscription?.subscriptions_benefits ? obj?.rows[index]?.tbl_new_subscription?.subscriptions_benefits : null;
      obj.rows[index].duration = obj?.rows[index]?.tbl_new_subscription?.duration ? obj?.rows[index]?.tbl_new_subscription?.duration : null;
      obj.rows[index].price_currency_code = obj?.rows[index]?.tbl_new_subscription?.price_currency_code ? obj?.rows[index]?.tbl_new_subscription?.price_currency_code : null;
      obj.rows[index].user_number = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.country_code ? (`+${obj?.rows[index]?.tbl_user?.country_code} ${obj?.rows[index]?.tbl_user?.phone}`) : obj?.rows[index]?.tbl_user?.phone : null;

      delete obj.rows[index].tbl_user;
      delete obj.rows[index].tbl_new_subscription;
      delete obj.rows[index].user_id;
      delete obj.rows[index].subscription_id;
      delete obj.rows[index].createdAt;
      delete obj.rows[index].purchase_token;
      delete obj.rows[index].is_active;
      delete obj.rows[index].is_expired;
      delete obj.rows[index].is_canceled;
      delete obj.rows[index].canceled_date;
    }
    return obj;
  },

  async allListPaymentTransaction(req, res) {

    /* storage plan find query */
    let transaction_list = await PaymentTransaction.findAndCountAll({
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`createdAt`), `DESC`]],

      include: [
        {
          model: User,
          attributes: ["first_name", "email", "phone", "country_code"]
        },
        {
          model: Plan,
          attributes: [/*"price",*/ "price_currency_code", "subscriptions_benefits", "duration"]
          // attributes: ["price", "storage", "duration"]
        },
      ],
    });
    var string = JSON.stringify(transaction_list);
    const obj = JSON.parse(string);

    for (let index = 0; index < obj.rows.length; index++) {
      obj.rows[index].name = obj?.rows[index]?.tbl_user?.first_name ? obj?.rows[index]?.tbl_user?.first_name : null;
      obj.rows[index].email = obj?.rows[index]?.tbl_user?.email ? obj?.rows[index]?.tbl_user?.email : null;
      // obj.rows[index].phone = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.phone : null;
      // obj.rows[index].country_code = obj?.rows[index]?.tbl_user?.country_code ? obj?.rows[index]?.tbl_user?.country_code : null;
      // obj.rows[index].amount = obj?.rows[index]?.tbl_new_subscription?.price ? obj?.rows[index]?.tbl_new_subscription?.price : null;
      obj.rows[index].storage = obj?.rows[index]?.tbl_new_subscription?.subscriptions_benefits ? obj?.rows[index]?.tbl_new_subscription?.subscriptions_benefits : null;
      obj.rows[index].duration = obj?.rows[index]?.tbl_new_subscription?.duration ? obj?.rows[index]?.tbl_new_subscription?.duration : null;
      obj.rows[index].price_currency_code = obj?.rows[index]?.tbl_new_subscription?.price_currency_code ? obj?.rows[index]?.tbl_new_subscription?.price_currency_code : null;
      obj.rows[index].user_number = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.country_code ? (`+${obj?.rows[index]?.tbl_user?.country_code} ${obj?.rows[index]?.tbl_user?.phone}`) : obj?.rows[index]?.tbl_user?.phone : null;

      delete obj.rows[index].tbl_user;
      delete obj.rows[index].tbl_new_subscription;
      delete obj.rows[index].user_id;
      delete obj.rows[index].subscription_id;
      delete obj.rows[index].createdAt;
      delete obj.rows[index].purchase_token;
      delete obj.rows[index].is_active;
      delete obj.rows[index].is_expired;
      delete obj.rows[index].is_canceled;
      delete obj.rows[index].canceled_date;
    }
    return obj;
  },

  async aiListPaymentTransaction(req, res) {
    let { sort_by, search, order, status } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search = search === undefined ? "" : search;
    let searchObj = {
      where: {
        [Op.or]: [
          {
            status: { [Op.like]: `%${search}%` },
          },
        ],
      },
    };

    if (status) {
      // searchObj.where.status = parseInt(status);
      searchObj.where.status = req.body.status;
    }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* Ai payment transaction find query */
    let transaction_list = await AiPaymentTransaction.findAndCountAll({
      where: searchObj.where,
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,

      include: [
        {
          model: User,
          attributes: ["first_name", "email", "phone", "country_code"],
        },
        {
          model: AiPlan,
          attributes: ["title", "description", "number_of_api", "price", "price_currency_code"]
        },
      ],
    });
    var string = JSON.stringify(transaction_list);
    const obj = JSON.parse(string);

    for (let index = 0; index < obj.rows.length; index++) {
      obj.rows[index].name = obj?.rows[index]?.tbl_user?.first_name ? obj?.rows[index]?.tbl_user?.first_name : null;
      obj.rows[index].email = obj?.rows[index]?.tbl_user?.email ? obj?.rows[index]?.tbl_user?.email : null;
      // obj.rows[index].phone = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.phone : null;
      // obj.rows[index].country_code = obj?.rows[index]?.tbl_user?.country_code ? obj?.rows[index]?.tbl_user?.country_code : null;
      // obj.rows[index].amount = obj?.rows[index]?.tbl_new_ai_subscription?.price ? obj?.rows[index]?.tbl_new_ai_subscription?.price : null;
      // obj.rows[index].title = obj?.rows[index]?.tbl_new_ai_subscription?.title ? obj?.rows[index]?.tbl_new_ai_subscription?.title : null;
      // obj.rows[index].description = obj?.rows[index]?.tbl_new_ai_subscription?.description ? obj?.rows[index]?.tbl_new_ai_subscription?.description : null;
      // obj.rows[index].number_of_api = obj?.rows[index]?.tbl_new_ai_subscription?.number_of_api ? obj?.rows[index]?.tbl_new_ai_subscription?.number_of_api : null;
      obj.rows[index].price_currency_code = obj?.rows[index]?.tbl_new_ai_subscription?.price_currency_code ? obj?.rows[index]?.tbl_new_ai_subscription?.price_currency_code : null;
      obj.rows[index].user_number = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.country_code ? (`+${obj?.rows[index]?.tbl_user?.country_code} ${obj?.rows[index]?.tbl_user?.phone}`) : obj?.rows[index]?.tbl_user?.phone : null;

      delete obj.rows[index].tbl_user;
      delete obj.rows[index].tbl_new_ai_subscription;
      delete obj.rows[index].user_id;
      delete obj.rows[index].subscription_id;
      // delete obj.rows[index].createdAt;
      delete obj.rows[index].purchase_token;
      delete obj.rows[index].ai_subscription_id;
    }
    return obj;
  },

  async allAiListPaymentTransaction(req, res) {

    /* Ai payment transaction find query */
    let transaction_list = await AiPaymentTransaction.findAndCountAll({
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`createdAt`), `DESC`]],

      include: [
        {
          model: User,
          attributes: ["first_name", "email", "phone", "country_code"]
        },
        {
          model: AiPlan,
          attributes: ["title", "description", "number_of_api", "price", "price_currency_code"]
        },
      ],
    });
    var string = JSON.stringify(transaction_list);
    const obj = JSON.parse(string);

    for (let index = 0; index < obj.rows.length; index++) {
      obj.rows[index].name = obj?.rows[index]?.tbl_user?.first_name ? obj?.rows[index]?.tbl_user?.first_name : null;
      obj.rows[index].email = obj?.rows[index]?.tbl_user?.email ? obj?.rows[index]?.tbl_user?.email : null;
      // obj.rows[index].phone = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.phone : null;
      // obj.rows[index].country_code = obj?.rows[index]?.tbl_user?.country_code ? obj?.rows[index]?.tbl_user?.country_code : null;
      // obj.rows[index].amount = obj?.rows[index]?.tbl_new_ai_subscription?.price ? obj?.rows[index]?.tbl_new_ai_subscription?.price : null;
      // obj.rows[index].title = obj?.rows[index]?.tbl_new_ai_subscription?.title ? obj?.rows[index]?.tbl_new_ai_subscription?.title : null;
      // obj.rows[index].description = obj?.rows[index]?.tbl_new_ai_subscription?.description ? obj?.rows[index]?.tbl_new_ai_subscription?.description : null;
      // obj.rows[index].number_of_api = obj?.rows[index]?.tbl_new_ai_subscription?.number_of_api ? obj?.rows[index]?.tbl_new_ai_subscription?.number_of_api : null;
      obj.rows[index].price_currency_code = obj?.rows[index]?.tbl_new_ai_subscription?.price_currency_code ? obj?.rows[index]?.tbl_new_ai_subscription?.price_currency_code : null;
      obj.rows[index].user_number = obj?.rows[index]?.tbl_user?.phone ? obj?.rows[index]?.tbl_user?.country_code ? (`+${obj?.rows[index]?.tbl_user?.country_code} ${obj?.rows[index]?.tbl_user?.phone}`) : obj?.rows[index]?.tbl_user?.phone : null;

      delete obj.rows[index].tbl_user;
      delete obj.rows[index].tbl_new_ai_subscription;
      delete obj.rows[index].user_id;
      delete obj.rows[index].subscription_id;
      // delete obj.rows[index].createdAt;
      delete obj.rows[index].purchase_token;
      delete obj.rows[index].ai_subscription_id;
    }
    return obj;
  },
};
