/*
 * Summary:     Multer is middleware for upload image
 * Author:      Openxcell(empCode-N00039)
 */
const multer = require("multer");
const path = require("path");
const os = require("os");
const fs = require("fs");
const tmpdir = os.tmpdir();
var storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, tmpdir);
  },
  filename: function (req, file, cb) {
    const imageName = Date.now() + path.extname(file.originalname);
    const filepath = path.join(tmpdir, imageName);
    file.originalname = imageName;
    fs.mkdtemp(filepath, (err, folder) => {
      if (err) throw err;
      cb(null, imageName);
    });
  },
});

exports.singleProfilePic = multer({
  storage: storage,
}).single("photo");

exports.singleImage = multer({
  storage: storage,
}).single("media");

exports.multiProfilePic = multer({
  storage: storage,
}).any();
