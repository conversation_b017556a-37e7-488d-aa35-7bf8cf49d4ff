'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_alternative_emails extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      tbl_alternative_emails.belongsTo(models.tbl_users, {
        targetKey: "user_id",
        foreignKey: "user_id",
        // as: "user_details",
      });
    }
  }
  tbl_alternative_emails.init({
    id:{
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    user_id:{
      allowNull: false,
      type: DataTypes.INTEGER,
    },
    email: DataTypes.STRING,
    country_code: DataTypes.STRING,
    phone_number: DataTypes.STRING
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_alternative_emails',
  });
  return tbl_alternative_emails;
};