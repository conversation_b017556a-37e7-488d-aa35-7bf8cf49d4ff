'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
	class tbl_contact_us extends Model {
		/**
		 * Helper method for defining associations.
		 * This method is not a part of Sequelize lifecycle.
		 * The `models/index` file will call this method automatically.
		 */
		static associate(models) {
			// define association here
		}
	}
	tbl_contact_us.init(
		{
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			name: {
				type: DataTypes.STRING,
			},
			email: {
				type: DataTypes.STRING,
			},
			phone_number: {
				type: DataTypes.STRING,
			},
			message: {
				type: DataTypes.TEXT,
			},
		},
		{
			sequelize,
			createdAt: false,
			updatedAt: false,
			modelName: 'tbl_contact_us',
		}
	);
	return tbl_contact_us;
};
