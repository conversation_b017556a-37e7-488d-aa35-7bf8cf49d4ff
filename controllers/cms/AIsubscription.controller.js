const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const ai_subscription_service = require("../../services/cms/AIsubscription.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async addAIsubscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let add_ai_subscription_res =
        await ai_subscription_service.addAIsubscription(req, res);
      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: add_ai_subscription_res,
        message: message.AISTORAGEPLANADDED,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("addAIsubscription -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async listAIsubscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_ai_subscription_res =
        await ai_subscription_service.listAIsubscription(req, res);
      if (list_ai_subscription_res) {
        list_ai_subscription_res.rows.map((item) => {
          item.price = item.price != "Free" ? `${item.price}` : item.price;
        });
        res.status(status.SUCCESSSTATUS).send({
          data: list_ai_subscription_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.STORAGEPLANNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listAIsubscription -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async editAIsubscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let edit_storagePlan_res =
        await ai_subscription_service.editAIsubscription(req, res);
      if (edit_storagePlan_res === 0) {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          data: [],
          message: message.STORAGEPLANNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          // data: [],
          data: edit_storagePlan_res,
          message: message.AISTORAGEPLANUPDATED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("editStoragePlan -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async deleteAIsubscription(req, res) {
    try {
      let data = await ai_subscription_service.deleteAIStoragePlan(req, res);
      // if (delete_storagePlan) {
      res.status(status.SUCCESSSTATUS).send({
        data: data.data,
        message: data.message,
        status: status.SUCCESS,
      });
      // }
    } catch (error) {
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: error,
        message: error?.message ? error?.message : message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async retriveAIsubscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_subscriptionPlan_res =
        await ai_subscription_service.retriveAIsubscription(req, res);
      if (!list_subscriptionPlan_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_subscriptionPlan_res,
          message: message.STORAGEPLANNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: list_subscriptionPlan_res,
          message: message.STORAGEPLANLIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("retriveAIsubscription -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
