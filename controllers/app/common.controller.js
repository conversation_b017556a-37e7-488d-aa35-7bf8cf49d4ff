/*
 * Summary:     common.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const common_service = require("../../services/app/common.service");
const path = require("path");
const ejs = require("ejs");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* signin */
  async signin(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.signin(req, res);
      if (user === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERNOTFOUND,
          status: status.ERROR,
        });
      } else if (user === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.INVALIDPASSWORD,
          status: status.ERROR,
        });
      } else if (user === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.VERIFYEMAILACCOUNT,
          status: status.ERROR,
        });
      } else if (user === 4) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.DELETEDUSER,
          status: status.ERROR,
        });
      } else if (user === 5) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERINACTIVE,
          status: status.ERROR,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: user,
          message: message.LOGINSUCCESS,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /*Signup*/
  async signup(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.signup(req, res);
      if (user === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERALREADYEXIST,
          status: status.ERROR,
        });
      } else if (user === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.GOOGLEUSERALREADYEXIST,
          status: status.ERROR,
        });
      } else if (user === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NORMALUSERALREADYEXIST,
          status: status.ERROR,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: user,
          message:
            user.dataValues.login_value === 1
              ? message.LOGINSUCCESS
              : message.SIGNUPSUCCESS,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /*verify OTP/Code*/
  async verify_otp(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.verify_otp(req, res);
      if (user === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERNOTFOUND,
          status: status.ERROR,
        });
      } else if (user === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.INVALIDOTP,
          status: status.ERROR,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.VARIFIEDOTP,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      //response on internal server error
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Forgot Password */
  async forgot_password(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.forgot_password(req, res);
      if (user === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERINACTIVE,
          status: status.ERROR,
        });
      } else if (user) {
        let mess = user.type === "email" ? message.SENTOTPBYEMAIL : user.type === "phone" ? message.SENTOTPBYPHONE : message.SENTOTP;
        res.status(status.SUCCESSSTATUS).send({
          data: user.find_user,
          message: mess,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ACCOUNTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Verify Code */
  async verify_code(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.verify_code(req, res);
      if (user === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERNOTFOUND,
          status: status.ERROR,
        });
      }    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Check User */
  async check_user(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let userExist = await common_service.check_user(req, res);
        res.status(status.SUCCESSSTATUS).send({
          data: { userExist },
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Verify User */
  async verify_user(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.verify_user(req, res);
      if (user === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERVARIFIED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Reset Password */
  async reset_password(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let { new_password, confirm_password } = req.body;
      if (new_password === confirm_password) {
        let user = await common_service.reset_password(req, res);
        if (user === 1) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.PASSWORDRESET,
            status: status.SUCCESS,
          });
        } else if (user === 2) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.USERNOTFOUND,
            status: status.ERROR,
          });
        } else {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.INVALIDOTP,
            status: status.ERROR,
          });
        }
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.PASSWORDNOTSAME,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Verify Email */
  async verify_email(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let verify = await common_service.verify_email(req, res);
      if (verify === 1) {
        res.status(status.SUCCESSSTATUS).send({
          message: message.VERIFIED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          message: message.VERIFICATIOCODENOTVALID,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* logout */
  async logout(req, res) {
    try {
      let getdetails = await common_service.logout(req, res);
      if (getdetails === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.LOGOUTSUCCESFULLY,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.INTERNALSERVERERRORSTATUS).send({
          data: [],
          message: message.INTERNALSERVERERROR,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async terms_of_use(req, res) {
    res.writeHead(200, { "Content-Type": "text/plain" });
    res.write("terms of service");
    res.end();
  },

  /* Edit Profile */
  async edit_profile(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await common_service.edit_profile(req, res);
      if (user === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERALREADYEXIST,
          status: status.ERROR,
        });
      } else if (user === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_EXIST,
          status: status.ERROR,
        });
      } else if (user) {
        res.status(status.SUCCESSSTATUS).send({
          data: user,
          message: message.PROFILEUPDATED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Changes Password */
  async change_password(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let { new_password, confirm_password } = req.body;
      if (new_password === confirm_password) {
        let user = await common_service.change_password(req, res);
        if (user === 1) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.PASSWORDRESET,
            status: status.SUCCESS,
          });
        } else if (user === 2) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.OLDPASSWORDNOTSAME,
            status: status.ERROR,
          });
        } else {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.USERNOTFOUND,
            status: status.ERROR,
          });
        }
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.PASSWORDNOTSAME,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  //set the web link preview true or false
  async set_web_link_preview(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let web_link_preview_set = await common_service.set_web_link_preview(
        req,
        res
      );
      if (web_link_preview_set) {
        res.status(status.SUCCESSSTATUS).send({
          data: web_link_preview_set,
          message: message.WEBLINKSET,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOTEIDNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  //set the AI true or false
  async set_AI(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let AI_set = await common_service.set_AI(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: AI_set,
        message: message.AISET,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
  /* storage used by particular user */
  async storage(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let storage = await common_service.storage(req, res);

      if (storage) {
        res.status(status.SUCCESSSTATUS).send({
          data: storage,
          message: message.STORAGEUSED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOUSERFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* user subscription create */
  async subscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n ")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let subscription = await common_service.subscription(req, res);

      if (subscription) {
        res.status(status.SUCCESSSTATUS).send({
          data: subscription,
          message: message.USERSUBSCRIPTIONADD,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOUSERFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* update subscription */
  async update_subscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n ")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await common_service.update_subscription(req, res);

      if (data === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERSUBSCRIPTIONUPDATE,
          status: status.SUCCESS,
        });
      } else if (data === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.SUBSCRIPTIONNOTUPDATE,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOUSERFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* AI subscription */
  async ai_subscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n ")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await common_service.ai_subscription(req, res);

      if (data === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.AISUBSCRIPTIONNOTFOUND,
          status: status.SUCCESS,
        });
      } else if (data === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.AIUSERSUBSCRIPTIONADD,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOUSERFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* cancel subscription */
  async cancel_subscription(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n ")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await common_service.cancel_subscription(req, res);

      if (data === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERSUBSCRIPTIONCANCEL,
          status: status.SUCCESS,
        });
      } else if (data === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USERSUBSCRIPTIONNOTCANCEL,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOUSERFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**alternative email and phone */
  async alternative_email(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let alternative = await common_service.alternative_email(req, res);

      if (alternative.type === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_LOGIN_EMAIL_SAME,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_PHONE_SAME,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_LOGIN_PHONE_SAME,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USER_EMAIL_EXIST,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 4) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.USER_PHONE_EXIST,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 5) {
        res.status(status.SUCCESSSTATUS).send({
          data: alternative.data,
          message: message.ALTERNATIVEPHONEADDED,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 9) {
        res.status(status.SUCCESSSTATUS).send({
          data: alternative.data,
          message: message.ALTERNATIVEPHONEUPDATE,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 6) {
        res.status(status.SUCCESSSTATUS).send({
          data: alternative.data,
          message: message.ALTERNATIVEEMAILADDED,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 10) {
        res.status(status.SUCCESSSTATUS).send({
          data: alternative.data,
          message: message.ALTERNATIVEEMAILUPDATE,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 7) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_EMAIL_EXIST,
          status: status.SUCCESS,
        });
      } else if (alternative.type === 8) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_PHONE_EXIST,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: alternative.data,
          message: message.ALTERNATIVEADDED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.errorStatus).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**Delete alternative email and phone */
  async delete_alternative(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await common_service.delete_alternative(req, res);

      if (data === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_EMAIL_NOT_EXIST,
          status: status.SUCCESS,
        });
      } else if (data === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_PHONE_NOT_EXIST,
          status: status.SUCCESS,
        });
      } else if (data === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_EMAIL_DELETE,
          status: status.SUCCESS,
        });
      } else if (data === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ALTERNATIVE_PHONE_DELETE,
          status: status.SUCCESS,
        });
      } else {
        return res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NORECORDFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.errorStatus).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * set the OCR true or false
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setOCR(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.setOCR(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.OCRSET,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * set AI confidence level
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setAIConfidenceLevel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.setAIConfidenceLevel(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.AIConfidenceLevelSET,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   *  user AI API count
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async userAICount(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let items = await common_service.userAICount(req, res);
      if (items) {
        return res.status(status.SUCCESSSTATUS).send({
          data: items,
          message: message.USERUSEDAIAPICALL,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   *  Subscription Storage and AI Products Plan ID API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async activeSubscriptionProductId(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await common_service.activeSubscriptionProductId(req, res);
      if (data) {
        return res.status(status.SUCCESSSTATUS).send({
          data: data,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   *  Storage Subscription Plan API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async storageSubscriptionPlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let storage_plan_details = await common_service.storageSubscriptionPlan(req, res);
      if (storage_plan_details) {
        return res.status(status.SUCCESSSTATUS).send({
          data: storage_plan_details,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   *  Storage Subscription Plan API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async newStorageSubscriptionPlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let storage_plan_details = await common_service.newStorageSubscriptionPlan(req, res);
      if (storage_plan_details) {
        return res.status(status.SUCCESSSTATUS).send({
          data: storage_plan_details,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   *  AI Subscription API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async subscriptionPlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let plan_details = await common_service.subscriptionPlan(req, res);
      if (plan_details) {
        return res.status(status.SUCCESSSTATUS).send({
          data: plan_details,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   *  AI Subscription API
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async newSubscriptionPlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let plan_details = await common_service.newSubscriptionPlan(req, res);
      if (plan_details) {
        return res.status(status.SUCCESSSTATUS).send({
          data: plan_details,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      }
      return res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.NOITEMFOUND,
        status: status.ERROR,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * set push notification status
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setNotificationStatus(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.setNotificationStatus(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.SetNotificationStatus,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * set biometric authentication
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async setBiometricAuthentication(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.setBiometricAuthentication(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.BiometricAuthenticationSET,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * manage AI label visibility
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async hideAILabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.hideAILabel(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.HIDEAILABEl,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * manage OCR label visibility
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async hideOCRLabel(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.hideOCRLabel(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.HIDEOCRLABEl,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * User hard delete
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async userDelete(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      await common_service.userDelete(req, res);
      res.status(status.SUCCESSSTATUS).send({
        data: {},
        message: message.USERDELETED,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /**
   * User subscription payment history
   * @param {*} req
   * @param {*} res
   * @returns
   */
  async paymentHistory(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let payment_history = await common_service.paymentHistory(req, res);
      if (payment_history) {
        res.status(status.SUCCESSSTATUS).send({
          data: payment_history,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.NOHISTORYFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  }
};
