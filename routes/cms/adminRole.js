/** NPM-modules */
const express = require("express");
const router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;
const multer_middleware = require("../../middleware/multer");

/* require for Validators */
const validators = require("../../assets/validators");

/* require for  Controller*/
const adminRole_controller = require("../../controllers/cms/adminRole.controller");

// routes for admin list
router.post("/add-role", authentication, adminRole_controller.addNewRole); /** add role */
router.put("/edit-role/:id", authentication, adminRole_controller.editRole); /** edit role */
router.put("/delete/:id", authentication, adminRole_controller.deleteRole); /** delete role */
router.post("/role-list", authentication, adminRole_controller.listAdminRole);
router.post("/role-retrive/:id", authentication, adminRole_controller.retriveAdminRole);
router.post("/assign-role", authentication, adminRole_controller.subadminAssignRole);
router.post("/assignrole-list/:id", authentication, adminRole_controller.subadminAssignRolelist);

module.exports = router;