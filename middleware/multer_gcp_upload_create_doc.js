
const { Storage } = require('@google-cloud/storage');
const CONSTANT = require("../config/constant");

const storage = new Storage({
    projectId: CONSTANT.GCP_PROJECT_ID,
    credentials: require("../assets/cloudvision-key-kyulebag-95d98-a13ed9bc6ddc.json")
});

const bucketName = CONSTANT.GCP_BUCKET_NAME;

/**
 *
 * @param { File } object file object that will be uploaded
 * @description - This function does the following
 * - It uploads a file to the image bucket on Google Cloud
 * - It accepts an object as an argument with the
 *   "originalname" and "buffer" as keys
 */

function gcpBufferUpload(files, destinationFilePath) {
    return new Promise((resolve, reject) => {
      const bucket = storage.bucket(bucketName);
      const file = bucket.file(destinationFilePath);

      // })

      file
        .createWriteStream()
        .on('finish', resolve)
        .on('error', (error) => {
          console.log('functionGCPUpload -> e', error);
          reject({
            message: 'Could not upload file, something went wrong',
            err: error,
          });
        })
        .end(files);
    });
}

module.exports = gcpBufferUpload;