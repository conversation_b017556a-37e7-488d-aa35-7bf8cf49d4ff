var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

/* require for Controller */
const notification_controller = require("../../controllers/cms/notification.controller");

/*require for validators */
const validators = require('../../assets/validators');

router.post("/add", authentication, validators.cms.notification.addNotification, notification_controller.addNotification);
router.post("/list", authentication, notification_controller.listNotificaton);
router.post("/resend/:id", authentication, notification_controller.resendNotification);

module.exports = router;