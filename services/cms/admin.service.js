const { DataTypes, where } = require("sequelize");
const { sequelize } = require("../../database/models");
const Admin = require("../../database/models/tbl_admins")(sequelize, DataTypes);
const AssignRolelist = require("../../database/models/tbl_admin_roles")(sequelize, DataTypes);
const AdminToken = require("../../database/models/tbl_admin_tokens")(sequelize, DataTypes);
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const Bcryptjs = require("bcryptjs");
const generatedSalt = Bcryptjs.genSaltSync(10);
const constant = require("../../config/constant");
const generalHelper = require("../../helper/general.helper");
const gcpUtils = require("../../helper/gcpUtils");

module.exports = {
  // list of admin
  async listAdmin(req, res) {
    let {
      sort_by,
      search_name,
      search_last_name,
      search_email,
      order,
      is_verified,
    } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search_name = search_name === undefined ? "" : search_name;
    search_last_name = search_name === undefined ? "" : search_last_name;
    search_email = search_name === undefined ? "" : search_email;
    let searchObj = {
      where: {
        is_deleted: "0",
        adminType: "superAdmin",

        [Op.or]: [
          {
            firstName: { [Op.like]: `%${search_name}%` },
          },
          {
            lastName: { [Op.like]: `%${search_name}%` },
          },
          {
            email: { [Op.like]: `%${search_name}%` },
          },
        ],
      },
    };

    // if (is_verified) {
    //   searchObj.where.is_verified = parseInt(is_verified);
    // }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* user find query */
    let admin_list = await Admin.findAndCountAll({
      where: searchObj.where,
      attributes: {
        exclude: ["password"],
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });

    // admin_list.rows = await generalHelper.userProfileMediaURL(admin_list.rows)

    return admin_list;
  },

  // list of sub admin
  async listSubAdmin(req, res) {
    let {
      sort_by,
      search_name,
      search_last_name,
      search_email,
      order,
      is_verified,
    } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search_name = search_name === undefined ? "" : search_name;
    search_last_name = search_name === undefined ? "" : search_last_name;
    search_email = search_name === undefined ? "" : search_email;
    let searchObj = {
      where: {
        is_deleted: "0",
        adminType: "subAdmin",

        [Op.or]: [
          {
            firstName: { [Op.like]: `%${search_name}%` },
          },
          {
            lastName: { [Op.like]: `%${search_name}%` },
          },
          {
            email: { [Op.like]: `%${search_name}%` },
          },
        ],
      },
    };

    // if (is_verified) {
    //   searchObj.where.is_verified = parseInt(is_verified);
    // }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* user find query */
    let sub_admin_list = await Admin.findAndCountAll({
      where: searchObj.where,
      attributes: {
        exclude: ["password"],
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });

    // sub_admin_list.rows = await generalHelper.userProfileMediaURL(sub_admin_list.rows)

    return sub_admin_list;
  },

  // admin retrive
  async retriveAdmin(req, res) {
    let find_admin = await Admin.findOne({
      where: { admin_id: req.params.id },
    });

    if (find_admin?.photo) {
      find_admin.photo = await gcpUtils.getPrivateUrl(
        constant.GCP_BUCKET_NAME,
        constant.GCP_BUCKET_FOLDER +
          constant.GCP_ADMIN_FOLDER +
          find_admin.dataValues.admin_id +
          '/' +
          find_admin.photo
      );
    }

    return find_admin;
  },

  // add admin
  async addAdmin(req) {
    let password = await Bcryptjs.hash(req.body.password, generatedSalt);
    let add_admin = null;
    let admin = await Admin.findOne({
      where: {
        email: req.body.email,
        is_deleted: "0"
      },
    });
    // if (
    //   admin != null &&
    //   admin.dataValues.is_deleted &&
    //   admin.dataValues.email === req.body.email
    // ) 
    // {
    //   add_admin = await Admin.create({
    //     firstName: req.body.firstName,
    //     lastName: req.body.lastName,
    //     email: req.body.email,
    //     password: password,
    //     adminType: req.body.adminType,
    //   });
    // }
    if (!admin) {
      add_admin = await Admin.create({
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        password: password,
        adminType: req.body.adminType,
      });
      if (req.body.adminType === "subAdmin") { 
      // if (!admin) {
        await AssignRolelist.bulkCreate([
          {role_id:1,admin_id:add_admin.admin_id,view:1},
          {role_id:2,admin_id:add_admin.admin_id},
          {role_id:3,admin_id:add_admin.admin_id}
        ])
      } else {
        await AssignRolelist.bulkCreate([
          {role_id:1,admin_id:add_admin.admin_id,view:1,edit:1,add:1,delete:1},
          {role_id:2,admin_id:add_admin.admin_id,view:1,edit:1,add:1,delete:1},
          {role_id:3,admin_id:add_admin.admin_id,view:1,edit:1,add:1,delete:1}
        ])
      }
    }
    return add_admin;
  },

  // edit admin
  async editAdmin(req) {
    // let edit_password = await Bcryptjs.hash(req.body.password, generatedSalt);
    // let add_admin = null;
    // var edit_admin = await Admin.findOne({
    //   where: {
    //     admin_id: req.params.id,
    //     // email: req.body.email
    //   },
    // });
    // let admin = await Admin.findOne({
    //   where: {
    //     email: req.body.email,
    //   },
    // });
    let admin_edit_obj = {
      firstName: req.body.firstName && req.body.firstName,
      lastName: req.body.lastName && req.body.lastName,
      // email: req.body.email && req.body.email,
      adminType: req.body.adminType && req.body.adminType,
      // password: edit_password && edit_password,
      // password: req.body.password != "" ? edit_password : "" ,
    };

    if (req.body.password) {
      
      let edit_password = await Bcryptjs.hash(req.body.password, generatedSalt);
      admin_edit_obj["password"] = edit_password
    }
    // let admin_edit_obj_without_pass = {
    //   firstName: req.body.firstName && req.body.firstName,
    //   lastName: req.body.lastName && req.body.lastName,
    //   email: req.body.email && req.body.email,
    //   adminType: req.body.adminType && req.body.adminType,
    // };
    // if (!edit_admin) {
      // if (req.body.password !== "") {
        let edit_admins = await Admin.update(admin_edit_obj, {
          where: { admin_id: req.params.id },
        });
      // } else {
      //   let edit_admins = await Admin.update(admin_edit_obj_without_pass, {
      //     where: { admin_id: req.params.id },
      //   });
      // }
    // }

    if (req.body.password) {      
      await AdminToken.destroy({
        where: {
          admin_id: req.params.id,
        },
      });
    }
    var edit_admin = await Admin.findOne({
      where: {
        admin_id: req.params.id,
      },
    });
    // if (req.body.email !== edit_admin.email ) {
    //   return edit_admin;
    // }
    return edit_admin;
  // }
  // else {
  //   return 0
  // }
  },

  // delete admin
  async deleteAdmin(req, res) {
    let find_admin = await Admin.update(
      {
        is_deleted: "1",
      },
      {
        where: {
          admin_id: req.params.id,
        },
      }
    );
    return find_admin;
  },
};
