const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const staticPage_service = require("../../services/cms/StaticPage.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");


module.exports = {
  async addStaticPage(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let static_content_res = await staticPage_service.addStaticPage(req, res);
      res.status(status.SUCCESSSTATUS).send({
        //response on email is not-found
        // data: [],
        data: static_content_res,
        message: message.CONTENTPAGEADDED,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log('addStaticPage -> error', error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
  async editStaticPage(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let static_content_res = await staticPage_service.editStaticPage(
        req,
        res
      );
      if (static_content_res === 0) {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          data: [],
          message: message.CONTENTPAGEDNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          //response on email is not-found
          // data: [],
          data: static_content_res,
          message: message.CONTENTPAGEDUPDATED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log('editStaticPage -> error', error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
  async listStaticPage(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_static_page_res = await staticPage_service.listStaticPage(
        req,
        res
      );
      if (list_static_page_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_static_page_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.CONTENTPAGEDNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log('listStaticPage -> error', error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
  // async changeStatusStaticPage(req, res) {
  //   const errors = validationResult(req);
  //   if (!errors.isEmpty()) {
  //     let message = "";
  //     displayOnlyFirstError(errors.array()).map((el, i, arr) =>
  //       arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
  //     );
  //     return res
  //       .status(status.SUCCESSSTATUS)
  //       .send({ data: {}, message: message, status: 0 });
  //   }
  //   try {
  //     let change_static_page_res = await staticPage_service.changeStatusStaticPage(req);
  //     if(change_static_page_res === 0) {
  //       res.status(status.SUCCESSSTATUS).send({
  //         data: [],
  //         message: message.STATICPAGEDNOTFOUND,
  //         status: status.SUCCESS,
  //       });
  //     }
  //     else {
  //       res.status(status.SUCCESSSTATUS).send({
  //         data: [],
  //         message: message.STATICPAGEDSTATUS,
  //         status: status.SUCCESS,
  //       });
  //     }
  //   } catch (error) {

  //     res.status(status.INTERNALSERVERERRORSTATUS).send({
  //       error: error,
  //       message: message.INTERNALSERVERERROR,
  //       status: status.ERROR,
  //     });
  //   }
  // },

  async retriveStaticPage(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = '';
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + '\n')
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_static_page_res = await staticPage_service.retriveStaticPage(
        req,
        res
      );
      if (!list_static_page_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_static_page_res,
          message: message.CONTENTPAGEDNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: list_static_page_res,
          message: message.CONTENTPAGELIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log('retrieveStaticPage -> error', error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};