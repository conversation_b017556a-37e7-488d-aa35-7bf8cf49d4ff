const { DataTypes, where } = require("sequelize");
const { sequelize } = require("../../database/models");
const Role = require("../../database/models/tbl_roles")(sequelize, DataTypes);
const AdminToken = require("../../database/models/tbl_admin_tokens")(sequelize, DataTypes);
const AdminRole = require("../../database/models/tbl_admin_roles")(sequelize, DataTypes);
const AssignRolelist = require("../../database/models/tbl_admin_roles")(sequelize, DataTypes);
const Sequelize = require("sequelize");
const constant = require("../../config/constant");

module.exports = {
  // add new role
  async addNewRole(req) {
    let add_admin_role = await Role.create({
      module_name: req.body.module_name,
    });
    return add_admin_role;
  },

  // edit role
  async editRole(req) {
    let role_edit_obj = {
      module_name: req.body.module_name && req.body.module_name,
    };
    // const isStaticPage = await StaticContent.findOne({where : {static_contents_id : req.params.id}})
    // if(isStaticPage !== null) {
    let edit_admin_role = await Role.update(role_edit_obj, {
      where: { role_id: req.params.id },
    });
    var edit_role = await Role.findOne({
      where: {
        role_id: req.params.id,
      },
    });
    return edit_role;
  },

  // delete role
  async deleteRole(req, res) {
    let find_role = await Role.update(
      {
        is_deleted: "1",
      },
      {
        where: {
          role_id: req.params.id,
        },
      }
    );
    return find_role;
  },

  // listing roles
  async listAdminRole(req, res) {
    let { sort_by, search, order } = req.body;
    search = search === undefined ? "" : search;
    let searchObj = {
      where: {
        is_deleted: "0",
        // [Op.or]:
        //   {
        //     module_name: { [Op.like]: `%${search}%` },
        //   },
      },
    };
    /*pagination*/
    const limit = constant.LIMIT;
    const offset = (req.body.page - 1) * constant.LIMIT;

    /* user find query */
    var role_list = await Role.findAndCountAll({
      where: searchObj.where,
      // limit: limit,
      // offset: offset,
      // order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
    });
    return role_list;
  },

  // retrive role
  async retriveAdminRole(req, res) {
    let find_admin_role = await Role.findOne({
      where: { role_id: req.params.id },
    });
    return find_admin_role;
  },

  // subadminAssignRole
  async subadminAssignRole(req, res) {
    for(let i=0; i<req.body.checkData.length; i++) {
      let item = req.body.checkData[i]
      let tempObj = {
        admin_id: req.body.admin_id,
        role_id: item.role_id,
      }
      if(item.e){
        tempObj[item.name.toLowerCase()]="1";
      } else {
        tempObj[item.name.toLowerCase()]="0";
      }
      let checked = await AdminRole.findOne({
          where: {
            role_id: tempObj.role_id,
            admin_id: tempObj.admin_id
          },
        });
        if (checked) {
          let obj = {
            view : checked.view,
            edit : checked.edit,
            add : checked.add,
            delete : checked.delete
          }
          obj[item.name.toLowerCase()]=tempObj[item.name.toLowerCase()]
          await AdminRole.update(obj,{
            where: {
              role_id: tempObj.role_id,
              admin_id: tempObj.admin_id
            },
          })
        } else {
          let add_admin_role = await AdminRole.create(tempObj);
        }
        await AdminToken.destroy({
          where: {
            admin_id: tempObj.admin_id,
          }
        });
      // data.push(tempObj);
    };   

    // let { admin_id, checkData } = req.body;
    // // let deletedObj = req.body.checkData;
    // // var checkDatas = req.body.checkData.role_id
    
    // var role_ids = JSON.parse(checkData);
    // // var role_ids = JSON.parse(JSON.stringify(checkData));
    // var views = JSON.parse(JSON.stringify(checkData));
    // var edits = JSON.parse(JSON.stringify(checkData));
    // var adds = JSON.parse(JSON.stringify(checkData));
    // var deletes = JSON.parse(JSON.stringify(checkData));

    // var add_admin_role = await Admin.findOne(row{
    //   where: {
    //     admin_id: admin_id
    //   },
    // });
    // // [{[role_id:1,[{name:view, e:true,name:edit, e:true}}]
    // if (add_admin_role) {
    //   role_ids.forEach(async(element, index) => {
    //     views.forEach(async(element,index1) => {
    //       if (index===index1){
    //         var add_role = await AdminRole.create({
    //           role_id: role_ids[index],
    //           admin_id: admin_id,
    //           view: views[index1],
    //           edit: edits[index1],
    //           add: adds[index1],
    //           delete: deletes[index1],
    //         });
    //       }
    //     })

    //   });
    // }
    // return 1;
    return 1; 
  },

  // subadmin assign rolelist
  async subadminAssignRolelist(req, res) {
    let find_admin_rolelist = await AssignRolelist.findAndCountAll({
      where: { admin_id: req.params.id },
    });
    return find_admin_rolelist;
  },
};
