const Items = require("../../database/models").tbl_items;
const Activity_logs = require("../../database/models").tbl_activity_logs;

module.exports = {
  async item_bookmarked_edit(req, res) {
    let { item_id, item_bookmarked } = req.body;
    let items = await Items.findOne({
      where: {
        item_id: item_id,
        is_deleted: 0

      },
    });
    if (items) {
      await Items.update(
        {
          is_bookmarked: item_bookmarked,
        },
        {
          where: {
            item_id: items.item_id,
            is_deleted: 0

          },
        }
      );
      var bookmarked_items = await Items.findOne({
        where: {
          item_id: item_id,
          is_deleted: 0,
        },
        attributes: {
          include: ["createdAt"],
        },
      });

      // var item_activity_log = await Activity_logs.create({
      //   user_id: req.user_id,
      //   title: "Bookmark Item",
      //   description: `Bookmarked set to ${bookmarked_items.dataValues.is_bookmarked} from ${items.is_bookmarked} of item.`
      // });
      bookmarked_items.dataValues.createdAt = bookmarked_items.dataValues.createdAt
        .toISOString().substr(0, 19).replace("T", " ");
      return bookmarked_items;
    }
  },
};
