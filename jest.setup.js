/**
 * Jest Setup Configuration
 * Global test setup for KyuleBag project
 */

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_APP_SECRET = 'test-jwt-app-secret';
process.env.APPNAME = 'KyuleBag Test';
process.env.WEBPAGEURL = 'https://test.kyulebag.com/';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock Sequelize Op
jest.mock('sequelize', () => {
  const actualSequelize = jest.requireActual('sequelize');
  return {
    ...actualSequelize,
    Op: {
      and: Symbol('and'),
      or: Symbol('or'),
      like: Symbol('like'),
      in: Symbol('in'),
      notIn: Symbol('notIn'),
    },
  };
});

// Global test timeout
jest.setTimeout(10000); 