/**
 * Global Configuration Setup
 *
 * This file initializes global variables and configurations that need to be available
 * throughout the entire KyuleBag application without explicit imports. It sets up
 * commonly used constants that are accessed frequently across multiple modules.
 *
 * Business Logic Connection:
 * - Makes status codes globally accessible to all controllers and services
 * - Eliminates the need for repeated imports of status constants
 * - Ensures consistent status code usage across the entire application
 * - Simplifies controller logic by providing direct access to STATUS_CODES
 *
 * Global Variables Initialized:
 * - STATUS_CODES: HTTP and application status codes for API responses
 *
 * Usage Pattern:
 * After this file is required in app.js, any module can use:
 * - STATUS_CODES.SUCCESSSTATUS instead of importing status constants
 * - STATUS_CODES.ERROR for error responses
 * - STATUS_CODES.SUCCESS for success responses
 *
 * Security Considerations:
 * - Global variables should be used sparingly to avoid namespace pollution
 * - Only essential constants should be made global
 * - Consider using dependency injection for better testability
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

/**
 * Initialize Global Status Codes
 *
 * Makes the status codes from the status configuration module available globally
 * throughout the application. This allows controllers and services to access
 * standard HTTP status codes and application-level status flags without
 * explicitly importing the status module in each file.
 *
 * The status object contains:
 * - HTTP status codes (200, 400, 401, 404, 500)
 * - Application status flags (SUCCESS: 1, ERROR: 0)
 *
 * Global Access Pattern:
 * - STATUS_CODES.SUCCESSSTATUS (200) - HTTP OK response
 * - STATUS_CODES.ERROR (0) - Application error flag
 * - STATUS_CODES.SUCCESS (1) - Application success flag
 * - STATUS_CODES.UNAUTHORIZEDUSER (401) - Authentication failure
 * - STATUS_CODES.INTERNALSERVERERRORSTATUS (500) - Server error
 */
global.STATUS_CODES = require('../config/status').status;
