var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;

/* require for Controller */
const transaction_controller = require("../../controllers/cms/paymentTransaction.controller");

/*require for validators */
const validators = require('../../assets/validators');

router.post("/list", authentication, transaction_controller.listPaymentTransaction);
router.post("/allList", authentication, transaction_controller.allListPaymentTransaction);
router.post("/aiList", authentication, transaction_controller.aiListPaymentTransaction);
router.post("/allAiList", authentication, transaction_controller.allAiListPaymentTransaction);

module.exports = router;