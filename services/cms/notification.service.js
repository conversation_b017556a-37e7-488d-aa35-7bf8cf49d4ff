require("dotenv").config();
const EmailNotification =
  require("../../database/models").tbl_email_notifications;
const User = require("../../database/models").tbl_users;
const Mail = require("../../helper/sendmail");
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");

module.exports = {
  async addNotification(req, res) {
    let find_active_user = await User.findAll({
      where: {
        is_deleted: "0",
        is_verified: 1,
      },
    });
    if (find_active_user) {
      let add_notification = await EmailNotification.create({
        description: req.body.description,
        admin_id: req.body.admin_id,
        subject: req.body.subject,
      });
      var subject = add_notification.subject;

      find_active_user.map(async (data) => {
        
        var mailbody =
          "<div><p>Hello " +
          data.dataValues.first_name +
          ", <br/>" +
          add_notification.description;

        let send_mail = await Mail.sendmail(
          res,
          data.dataValues.email,
          subject,
          mailbody
        );
      });

      return add_notification;
    } else {
      return 0;
    }
  },

  async listNotificaton(req, res) {
    let { sort_by, search, order } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search = search === undefined ? "" : search;
    let searchObj = {
      where: {
        [Op.or]: [
          {
            description: { [Op.like]: `%${search}%` },
          },
        ],
      },
    };

    // if (is_verified) {
    //   searchObj.where.is_verified = parseInt(is_verified);
    // }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* notification find query */
    let notification_list = await EmailNotification.findAndCountAll({
      where: searchObj.where,
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });
    return notification_list;
  },

  async resendNotification(req, res) {
    let find_active_user = await User.findAll({
      where: {
        is_deleted: "0",
        is_verified: 1,
      },
    });
    let find_notification = await EmailNotification.findOne({
      where: { email_notification_id: req.params.id },
    });
    if (find_notification) {
      var subject = find_notification.subject;

      find_active_user.map(async (data) => {
        var mailbody =
          "<div><p>Hello " +
          data.dataValues.first_name +
          ", <br/>" +
          find_notification.description;

        let send_mail = await Mail.sendmail(
          res,
          data.dataValues.email,
          subject,
          mailbody
        );
      });
      return find_notification;
    } else {
      return 0;
    }
    // return find_notification;
  },
};
