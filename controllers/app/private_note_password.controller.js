/*
 * Summary:     tags.controller file for handling all requests and response of Common
 * Author:      Openxcell(empCode-N00039)
 */

/*Messages,status code and services require*/
const status = require("../../config/status").status;
const message = require("../../config/web.message").appMessage;
const private_note_pswd = require("../../services/app/private_note_password.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  /* add */
  async private_note_add(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let private_note = await private_note_pswd.private_note_pswd_add(req, res);
      //   let items = await items_service.add(req, res);
      if (private_note) {
        res.status(status.SUCCESSSTATUS).send({
          data: private_note,
          message: message.PRIVATENOTEPASSWORDADDED,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.PRIVATENOTEPASSWORDALREADYADDED,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.errorStatus).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async change_private_note_password(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let { new_private_note_password, confirm_private_note_password } =
        req.body;
      if (new_private_note_password === confirm_private_note_password) {
        let user = await private_note_pswd.change_private_note_password(
          req,
          res
        );
        if (user === 1) {
          const private_note_password = {};
          private_note_password.private_note_password =
            new_private_note_password;
          res.status(status.SUCCESSSTATUS).send({
            data: private_note_password,
            message: message.PRIVATENOTEPASSWORDRESET,
            status: status.SUCCESS,
          });
        } else if (user === 2) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.PRIVATENOTEPASSWORDNOTMATCHED,
            status: status.ERROR,
          });
        } else if (user === 4) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.PRIVATENOTEPASSWORDNOTFOUND,
            status: status.ERROR,
          });
        } else {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.USERNOTFOUND,
            status: status.ERROR,
          });
        }
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.PRIVATENOTEPASSWORDNOTSAME,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Forgot private note password */
  async forgot_private_note_password(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let user = await private_note_pswd.forgot_private_note_password(req, res);
      if (user) {
        res.status(status.SUCCESSSTATUS).send({
          data: user,
          message: message.SENTOTPBYEMAIL,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.ACCOUNTNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  /* Reset private note password */
  async reset_private_note_password(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let { new_password, confirm_password } = req.body;
      if (new_password === confirm_password) {
        let user = await private_note_pswd.reset_private_note_password(req, res);
        if (user === 1) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.PINRESET,
            status: status.SUCCESS,
          });
        } else if (user === 2) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.USERNOTFOUND,
            status: status.ERROR,
          });
        } else if (user === 3) {
          res.status(status.SUCCESSSTATUS).send({
            data: {},
            message: message.INVALIDOTP,
            status: status.ERROR,
          });
        }
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: {},
          message: message.PASSWORDNOTSAME,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("TCL:error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: {},
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
