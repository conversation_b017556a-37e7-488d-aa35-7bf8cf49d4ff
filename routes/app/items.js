/*
 * Summary:     tags.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const user_auth = require("../../middleware/auth").userAuthentication;
const validators = require("../../assets/validators");
const imageMiddleware = require("../../middleware/multer");
const item_controller = require("../../controllers/app/items.controller");
const item_bookmarked_controller = require("../../controllers/app/item_bookmarked.controller");
const item_is_private = require("../../controllers/app/item_is_private.controller");

// router.post("/list", user_auth, item_controller.list);
router.post(
  "/item_private",
  user_auth,
  validators.app.item_is_private.edit,
  item_is_private.edit
);
router.post(
  "/item_bookmark",
  user_auth,
  validators.app.item_bookmark.edit,
  item_bookmarked_controller.edit
);
router.post(
  "/add",
  user_auth,
  imageMiddleware.singleImage,
  validators.app.items.add,
  item_controller.add
);

router.post(
  "/bulkAdd",
  user_auth,
  imageMiddleware.singleImage,
  validators.app.items.add,
  item_controller.bulkAdd
);

router.post(
  "/uploadMedia",
  user_auth,
  imageMiddleware.singleImage,
  item_controller.uploadMedia
);
router.post(
  "/list",
  user_auth,
  validators.app.items.list,
  item_controller.list
);
router.post(
  "/delete",
  user_auth,
  validators.app.items.delete,
  item_controller.delete
);
router.put(
  "/edit",
  user_auth,
  imageMiddleware.singleProfilePic,
  validators.app.items.edit,
  item_controller.edit
);

// delete AI label
router.post(
  "/delete-ai-label",
  user_auth,
  validators.app.items.deleteAILabel,
  item_controller.deleteAILabel
);

// reset AI label
router.post(
  "/reset-ai-label",
  user_auth,
  validators.app.items.resetAILabel,
  item_controller.resetAILabel
);

//reobserve AI
router.post(
  "/reobserve-ai-label",
  user_auth,
  validators.app.items.reobserveAILabel,
  item_controller.reobserveAILabel
);

//reobserve OCR
router.post(
  "/reobserve-ocr-label",
  user_auth,
  validators.app.items.reobserveOCRLabel,
  item_controller.reobserveOCRLabel
);

// delete OCR label
router.post(
  "/delete-ocr-label",
  user_auth,
  validators.app.items.deleteOCRLabel,
  item_controller.deleteOCRLabel
);

module.exports = router;
