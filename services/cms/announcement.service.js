require("dotenv").config();
const Announcement = require("../../database/models").tbl_announcements;
const User = require("../../database/models").tbl_users;
const CommentSettings = require("../../database/models").tbl_comment_settings;
const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");
const notificationFunction =
  require("../../helper/pushNotification").sendNotification;
const newNotificationFunction =
  require("../../helper/pushNotification").sendFcmNewNotification;

module.exports = {
  async addAnnouncement(req, res) {
    let find_active_user = await User.findAll({
      where: {
        is_verified: 1,
      },
    });
    if (find_active_user) {
      let add_announcement = await Announcement.create({
        content: req.body.content,
        admin_id: req.body.admin_id,
        is_announcement: req.body.is_announcement,
      });
      let notificationObj = {
        content: req.body.content,
        announcement_id: add_announcement.dataValues.announcement_id,
        admin_id: add_announcement.dataValues.admin_id,
        createdAt: add_announcement.dataValues.createdAt,
      };
      // await notificationFunction({}, notificationObj);
      await newNotificationFunction({}, notificationObj);

      return add_announcement;
    } else {
      return 0;
    }
  },

  async deleteAnnouncement(req, res) {
    const findAnnouncement = await Announcement.findOne({
      where: {
        announcement_id: req.query.announcement_id,
      },
      raw: true,
    });

    if (findAnnouncement) {
      await Announcement.update(
        {
          isDeleted: true,
        },
        {
          where: {
            announcement_id: Number(req.query.announcement_id),
          },
        }
      );

      return 1;
    } else {
      return 0;
    }
  },

  async editCommentSettings(req, res) {
    try {
      await CommentSettings.update(
        {
          ...req.body,
        },
        {
          where: {
            // As of now only one comment setting so it is hardcoded
            commentSettingId: 1,
          },
        }
      );
    } catch (error) {
      throw error;
    }
  },

  async listAnnouncement(req, res) {
    let { sort_by, search, order } = req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search = search === undefined ? "" : search;

    let searchObj = {
      where: {
        [Op.and]: [
          {
            isDeleted: false,
          },
          {
            content: { [Op.like]: `%${search}%` },
          },
          {
            is_announcement: true,
          },
        ],
      },
    };

    // if (is_verified) {
    //   searchObj.where.is_verified = parseInt(is_verified);
    // }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* notification find query */
    let announcement_list = await Announcement.findAndCountAll({
      where: searchObj.where,
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });
    return announcement_list;
  },

  async allListAnnouncement(req, res) {

    /* notification find query */
    let announcement_list = await Announcement.findAndCountAll({
      where: {
        isDeleted: false,
        is_announcement: true,
      },
      attributes: {
        include: ["createdAt"],
      },
      order: [[Sequelize.literal(`createdAt`), `DESC`]],
    });
    return announcement_list;
  },

  async resendAnnouncement(req, res) {
    let find_active_user = await User.findAll({
      where: {
        is_verified: 1,
      },
    });
    let find_announcement = await Announcement.findOne({
      where: { announcement_id: req.params.id },
    });
    if (find_announcement) {
      // still panding
      return find_announcement;
    } else {
      return 0;
    }
    // return find_notification;
  },
};
