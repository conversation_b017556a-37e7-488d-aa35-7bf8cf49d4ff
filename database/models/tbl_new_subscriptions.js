'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_new_subscriptions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      tbl_new_subscriptions.hasMany(models.tbl_user_subscriptions, {
        targetKey: "subscription_id",
        foreignKey: "subscription_id",
        // as: "subscription_plan_details",
      });
    }
  }
  tbl_new_subscriptions.init({
    subscription_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    subscriptions_product_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    subscriptions_benefits: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    base_plan_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    duration: {
      type: DataTypes.ENUM("monthly", "yearly"),
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    price_amount_micros: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    price_currency_code: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [3, 3],
      },
    },
    status: {
      type: DataTypes.ENUM("inactive", "active"),
      defaultValue: "inactive",
    },
    is_deleted: {
      allowNull: false,
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
    is_free: {
      allowNull: false,
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
  }, {
    sequelize,
    createdAt: false,
    updatedAt: false,
    modelName: 'tbl_new_subscriptions',
  });
  return tbl_new_subscriptions;
};