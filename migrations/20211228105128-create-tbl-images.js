"use strict";
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable("tbl_media", {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      item_id: {
        type: Sequelize.STRING,
      },
      media: {
        type: Sequelize.STRING,
      },
      media_type: {
        type: Sequelize.ENUM("image", "video", "audio", "pdf", "doc", "text", "link", "contact", "location", "create_doc"),
        defaultValue: "image",
      },
      resolution: {
        type: Sequelize.STRING,
      },
      source: {
        type: Sequelize.STRING,
      },
      AI_objects: {
        type: Sequelize.JSON,
      },
      extention: {
        type: Sequelize.STRING,
      },
      duration: {
        type: Sequelize.STRING,
      },
      size: {
        type: Sequelize.FLOAT,
      },
      user_id: {
        type: Sequelize.INTEGER,
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },

      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        defaultValue: Sequelize.literal(
          "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
      },
      // createdAt: {
      //   allowNull: false,
      //   type: Sequelize.DATE,
      // },
      // updatedAt: {
      //   allowNull: false,
      //   type: Sequelize.DATE,
      // },
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable("tbl_media");
  },
};
