"use strict";
const { Model } = require("sequelize");
module.exports = (sequelize, DataTypes) => {
  class tbl_sharing_details extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  tbl_sharing_details.init(
    {
      item_id: DataTypes.STRING,
      name: DataTypes.STRING,
      phone_number: DataTypes.STRING,
      longitude: DataTypes.STRING,
      latitude: DataTypes.STRING,
      link: DataTypes.TEXT,
      link_preview_image: DataTypes.STRING,
      link_preview_video: DataTypes.STRING,
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize,
      modelName: "tbl_sharing_details",
    }
  );
  return tbl_sharing_details;
};
