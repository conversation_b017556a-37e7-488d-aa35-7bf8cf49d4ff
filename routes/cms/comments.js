var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;
// const multer_middleware = require('../../middleware/multer');

/* require for Controller */
const comment_controller = require("../../controllers/cms/comment.controller");

/*require for validators */
const validators = require('../../assets/validators');

router.post("/list", authentication, comment_controller.listComments);
router.post("/allList", authentication, comment_controller.allListComments);

/* API to get comment settings */
router.get("/settings", authentication, comment_controller.getCommentSettings)

/* API to edit comment settings */
router.put("/settings", authentication, comment_controller.editCommentSettings)


module.exports = router;