/**
 * Database Configuration Module
 *
 * This file contains database connection configurations for different deployment environments.
 * It manages Sequelize ORM connection parameters for MySQL database across development,
 * staging, and production environments, ensuring proper data persistence for the KyuleBag platform.
 *
 * Business Logic Connection:
 * - Ensures reliable data storage for user accounts, content items, subscriptions, and system data
 * - Supports different database instances for development, testing, and production workloads
 * - Enables proper separation of environments for secure development lifecycle
 * - Configures connection pooling and query optimization for production performance
 *
 * Environment Support:
 * - development: Local development database
 * - gcp-development: Google Cloud Platform development instance
 * - new-gcp-development: Updated GCP development environment
 * - staging: Pre-production testing environment
 * - production: Live production database
 *
 * Security Features:
 * - All credentials loaded from environment variables
 * - No hardcoded database credentials in source code
 * - Logging disabled in production to prevent sensitive data exposure
 *
 * Default Behaviors:
 * - Uses MySQL dialect for all environments
 * - Logging disabled for production security
 * - Connection parameters retrieved from environment variables
 * - Falls back to development environment if NODE_ENV not specified
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

// Load environment variables for database configuration
require('dotenv').config();

/**
 * Database configuration object for different environments
 * Each environment contains connection parameters for Sequelize ORM
 *
 * Configuration Structure:
 * - username: Database user account
 * - password: Database user password
 * - database: Target database name
 * - host: Database server hostname/IP
 * - port: Database server port (typically 3306 for MySQL)
 * - dialect: Database type (MySQL in this case)
 * - logging: Query logging configuration (disabled for production)
 */
module.exports = {
  /**
   * Development Environment Configuration
   * Used for local development and testing
   * Typically connects to local MySQL instance or development database
   */
  development: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_CONNECTION,
    logging: false, // Disable SQL query logging for cleaner console output
  },

  /**
   * Google Cloud Platform Development Environment
   * Used for development testing on GCP infrastructure
   * Connects to Cloud SQL MySQL instance for development
   */
  'gcp-development': {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_CONNECTION,
    logging: false, // Disable logging for development performance
  },

  /**
   * New Google Cloud Platform Development Environment
   * Updated GCP development configuration for newer infrastructure
   * Supports latest Cloud SQL features and optimizations
   */
  'new-gcp-development': {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_CONNECTION,
    logging: false, // Consistent logging configuration
  },

  /**
   * Staging Environment Configuration
   * Pre-production environment for final testing before deployment
   * Mirrors production configuration but with separate infrastructure
   */
  staging: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_CONNECTION,
    // Logging can be enabled in staging for debugging deployment issues
  },

  /**
   * Production Environment Configuration
   * Live production database serving real users and data
   * Optimized for performance, security, and reliability
   */
  production: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_CONNECTION,
    // No logging in production for security and performance
  },
};
