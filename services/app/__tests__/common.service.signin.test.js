/**
 * Unit Tests for Sign-in Business Logic
 * Based on QA E2E Test Scenarios for KyuleBag Login Flow
 * 
 * Tests cover:
 * - Email-based login (KyuleBag native accounts)
 * - Phone-based login (SMS authentication)
 * - Password verification and validation
 * - Account status checks (deleted, verified, active)
 * - JWT token generation and storage
 * - Device information tracking
 * - Alternative contact information retrieval
 * - Profile photo URL generation
 * - Cross-authentication scenarios
 * - Error handling and edge cases
 */

const { Op } = require('sequelize');

// Mock all external dependencies
jest.mock('../../../database/models', () => ({
  tbl_users: {
    findOne: jest.fn(),
    update: jest.fn(),
  },
  tbl_user_tokens: {
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  tbl_alternative_emails: {
    findOne: jest.fn(),
  },
}));

jest.mock('bcryptjs', () => ({
  compareSync: jest.fn(),
  genSaltSync: jest.fn(() => 'mock-salt'),
}));

jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
}));

jest.mock('../../../config/constant', () => ({
  JWTAPPTOKEN: {
    secret: 'test-secret',
    algo: 'HS256',
  },
  GCP_BUCKET_NAME: 'test-bucket',
  GCP_BUCKET_FOLDER: 'test-folder/',
  GCP_USER_FOLDER: 'users/',
}));

jest.mock('../../../helper/general.helper', () => ({
  staticFileImages: jest.fn(),
}));

jest.mock('../../../helper/gcpUtils', () => ({
  getPrivateUrl: jest.fn(),
}));

// Import the service after mocking
const commonService = require('../common.service');
const User = require('../../../database/models').tbl_users;
const UserToken = require('../../../database/models').tbl_user_tokens;
const AlternativeEmail = require('../../../database/models').tbl_alternative_emails;
const Bcryptjs = require('bcryptjs');
const jwt = require('jsonwebtoken');
const imageSize = require('../../../helper/general.helper');
const gcpUtils = require('../../../helper/gcpUtils');

describe('Sign-in Business Logic - Unit Tests', () => {
  let mockReq, mockRes;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock request object
    mockReq = {
      body: {
        email: '<EMAIL>',
        password: 'Password123!',
        login_type: 'KyuleBag',
        device_token: 'mock-device-token',
        phone: '',
        country_code: '',
      },
      headers: {
        device_type: 'mobile',
        version: '1.0.0',
      },
    };

    // Mock response object
    mockRes = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };

    // Default mock implementations
    Bcryptjs.compareSync.mockReturnValue(true);
    jwt.sign.mockReturnValue('mock-jwt-token');
    imageSize.staticFileImages.mockResolvedValue([]);
    gcpUtils.getPrivateUrl.mockResolvedValue('https://mock-gcp-url.com/photo.jpg');
  });

  describe('🔐 Email-Based Authentication', () => {
    describe('✅ Successful Login Scenarios', () => {
      it('should successfully authenticate user with valid email and password', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
          password: 'hashed-password',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
          phone: '+**********',
          dataValues: {
            user_id: 'user123',
            email: '<EMAIL>',
          },
        };

        User.findOne.mockResolvedValue(mockUser);
        UserToken.findOne.mockResolvedValue(null);
        UserToken.create.mockResolvedValue({});
        AlternativeEmail.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(User.findOne).toHaveBeenCalledWith({
          where: {
            email: '<EMAIL>',
            is_deleted: '0',
          },
        });
        expect(Bcryptjs.compareSync).toHaveBeenCalledWith('Password123!', 'hashed-password');
        expect(jwt.sign).toHaveBeenCalledWith(
          {
            phone: '+**********',
            user_id: 'user123',
          },
          'test-secret',
          { algorithm: 'HS256' }
        );
        expect(result.dataValues.token).toBe('mock-jwt-token');
        expect(result.dataValues.password).toBeUndefined();
      });

      it('should update device information on successful login', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          email: '<EMAIL>',
          password: 'hashed-password',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
          dataValues: { user_id: 'user123' },
        };

        User.findOne.mockResolvedValue(mockUser);
        UserToken.findOne.mockResolvedValue(null);
        AlternativeEmail.findOne.mockResolvedValue(null);

        // Act
        await commonService.signin(mockReq, mockRes);

        // Assert
        expect(User.update).toHaveBeenCalledWith(
          { device_token: 'mock-device-token', device_type: 'mobile' },
          { where: { user_id: 'user123' } }
        );
      });

      it('should create new user token when none exists', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
          password: 'hashed-password',
          dataValues: { user_id: 'user123' },
        };

        User.findOne.mockResolvedValue(mockUser);
        UserToken.findOne.mockResolvedValue(null);
        AlternativeEmail.findOne.mockResolvedValue(null);

        // Act
        await commonService.signin(mockReq, mockRes);

        // Assert
        expect(UserToken.create).toHaveBeenCalledWith({
          token: 'mock-jwt-token',
          user_id: 'user123',
        });
      });

      it('should update existing user token when token exists', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
          password: 'hashed-password',
          dataValues: { user_id: 'user123' },
        };
        const existingToken = { user_id: 'user123', token: 'old-token' };

        User.findOne.mockResolvedValue(mockUser);
        UserToken.findOne.mockResolvedValue(existingToken);
        AlternativeEmail.findOne.mockResolvedValue(null);

        // Act
        await commonService.signin(mockReq, mockRes);

        // Assert
        expect(UserToken.update).toHaveBeenCalledWith(
          { token: 'mock-jwt-token' },
          { where: { user_id: 'user123' } }
        );
        expect(UserToken.create).not.toHaveBeenCalled();
      });

      it('should include alternative contact information when available', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
          password: 'hashed-password',
          dataValues: { user_id: 'user123' },
        };
        const alternativeContact = {
          email: '<EMAIL>',
          phone_number: '+9876543210',
          country_code: '+1',
        };

        User.findOne.mockResolvedValue(mockUser);
        UserToken.findOne.mockResolvedValue(null);
        AlternativeEmail.findOne.mockResolvedValue(alternativeContact);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(result.dataValues.alternative_email).toBe('<EMAIL>');
        expect(result.dataValues.alternative_phone_number).toBe('+9876543210');
        expect(result.dataValues.alternative_country_code).toBe('+1');
      });

      it('should handle profile photo URL generation', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
          password: 'hashed-password',
          photo: 'profile.jpg',
          dataValues: { user_id: 'user123' },
        };

        User.findOne.mockResolvedValue(mockUser);
        UserToken.findOne.mockResolvedValue(null);
        AlternativeEmail.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(gcpUtils.getPrivateUrl).toHaveBeenCalledWith(
          'test-bucket',
          'test-folder/users/user123/profile.jpg'
        );
        expect(result.photo).toBe('https://mock-gcp-url.com/photo.jpg');
      });
    });

    describe('❌ Authentication Failure Scenarios', () => {
      it('should return error code 1 when user does not exist', async () => {
        // Arrange
        User.findOne.mockResolvedValue(null);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(result).toBe(1);
        expect(Bcryptjs.compareSync).not.toHaveBeenCalled();
      });

      it('should return error code 2 when password is incorrect', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          password: 'hashed-password',
          is_deleted: '0',
          email_verified: '1',
          status: 'active',
        };

        User.findOne.mockResolvedValue(mockUser);
        Bcryptjs.compareSync.mockReturnValue(false);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(result).toBe(2);
        expect(Bcryptjs.compareSync).toHaveBeenCalledWith('Password123!', 'hashed-password');
      });

      it('should return error code 3 when email is not verified', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          password: 'hashed-password',
          is_deleted: '0',
          email_verified: '0',
          status: 'active',
        };

        User.findOne.mockResolvedValue(mockUser);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(result).toBe(3);
      });

      it('should return error code 4 when account is deleted', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          password: 'hashed-password',
          is_deleted: '1',
          email_verified: '1',
          status: 'active',
        };

        User.findOne.mockResolvedValue(mockUser);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(result).toBe(4);
      });

      it('should return error code 5 when account is inactive', async () => {
        // Arrange
        const mockUser = {
          user_id: 'user123',
          password: 'hashed-password',
          is_deleted: '0',
          email_verified: '1',
          status: 'inactive',
        };

        User.findOne.mockResolvedValue(mockUser);

        // Act
        const result = await commonService.signin(mockReq, mockRes);

        // Assert
        expect(result).toBe(5);
      });
    });
  });

  describe('📱 Phone-Based Authentication', () => {
    beforeEach(() => {
      mockReq.body = {
        phone: '+**********',
        country_code: '+1',
        password: 'Password123!',
        login_type: 'KyuleBag',
        device_token: 'mock-device-token',
        email: '', // No email for phone auth
      };
    });

    it('should successfully authenticate user with phone and country code', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        phone: '+**********',
        country_code: '+1',
        password: 'hashed-password',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        dataValues: {
          user_id: 'user123',
          phone: '+**********',
        },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(User.findOne).toHaveBeenCalledWith({
        where: {
          phone: '+**********',
          country_code: '+1',
          is_deleted: '0',
        },
      });
      expect(result.dataValues.token).toBe('mock-jwt-token');
    });

    it('should return error code 1 when phone number not found', async () => {
      // Arrange
      User.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(result).toBe(1);
    });

    it('should verify password for phone-based authentication', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        phone: '+**********',
        password: 'hashed-password',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      await commonService.signin(mockReq, mockRes);

      // Assert
      expect(Bcryptjs.compareSync).toHaveBeenCalledWith('Password123!', 'hashed-password');
    });
  });

  describe('🔧 Device and Version Tracking', () => {
    it('should handle missing device type gracefully', async () => {
      // Arrange
      mockReq.headers = {}; // No headers
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      await commonService.signin(mockReq, mockRes);

      // Assert
      expect(User.update).toHaveBeenCalledWith(
        { device_token: 'mock-device-token', device_type: '' },
        { where: { user_id: 'user123' } }
      );
    });

    it('should handle missing device token gracefully', async () => {
      // Arrange
      mockReq.body.device_token = undefined;
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      await commonService.signin(mockReq, mockRes);

      // Assert
      expect(User.update).toHaveBeenCalledWith(
        { device_token: '', device_type: 'mobile' },
        { where: { user_id: 'user123' } }
      );
    });

    it('should track app version information', async () => {
      // Arrange
      mockReq.headers.version = '2.1.0';
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert - Version is extracted but not used in current implementation
      expect(result).toBeDefined();
    });
  });

  describe('🔄 Data Sanitization and Response', () => {
    it('should remove password from response data', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        password: 'hashed-password',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        dataValues: {
          user_id: 'user123',
          password: 'hashed-password',
          email: '<EMAIL>',
        },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(result.dataValues.password).toBeUndefined();
      expect(result.dataValues.email).toBe('<EMAIL>');
    });

    it('should include static images in response', async () => {
      // Arrange
      const mockStaticImages = ['icon1.png', 'icon2.png'];
      imageSize.staticFileImages.mockResolvedValue(mockStaticImages);

      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(result.dataValues.staticImage).toEqual(mockStaticImages);
    });

    it('should set empty alternative contact info when none exists', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(result.dataValues.alternative_email).toBe('');
      expect(result.dataValues.alternative_phone_number).toBe('');
      expect(result.dataValues.alternative_country_code).toBe('');
    });
  });

  describe('🚨 Edge Cases and Error Handling', () => {
    it('should handle database errors during user lookup', async () => {
      // Arrange
      User.findOne.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(commonService.signin(mockReq, mockRes)).rejects.toThrow('Database connection failed');
    });

    it('should handle JWT token generation failures', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      jwt.sign.mockImplementation(() => {
        throw new Error('JWT generation failed');
      });

      // Act & Assert
      await expect(commonService.signin(mockReq, mockRes)).rejects.toThrow('JWT generation failed');
    });

    it('should handle token storage failures gracefully', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      UserToken.create.mockRejectedValue(new Error('Token storage failed'));
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(commonService.signin(mockReq, mockRes)).rejects.toThrow('Token storage failed');
    });

    it('should handle alternative email lookup failures', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      UserToken.create.mockResolvedValue({}); // Reset token create to success
      AlternativeEmail.findOne.mockRejectedValue(new Error('Alternative email lookup failed'));

      // Act & Assert
      await expect(commonService.signin(mockReq, mockRes)).rejects.toThrow('Alternative email lookup failed');
    });

    it('should handle missing request body parameters', async () => {
      // Arrange
      mockReq.body = {}; // Empty request body
      User.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(result).toBe(1); // User not found when no lookup criteria provided
    });

    it('should handle password comparison with null/undefined password', async () => {
      // Arrange
      mockReq.body.password = undefined;
      const mockUser = {
        user_id: 'user123',
        password: 'hashed-password',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
      };

      User.findOne.mockResolvedValue(mockUser);
      Bcryptjs.compareSync.mockReturnValue(false);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert
      expect(result).toBe(2);
      expect(Bcryptjs.compareSync).toHaveBeenCalledWith(undefined, 'hashed-password');
    });
  });

  describe('🔀 Cross-Authentication Scenarios', () => {
    beforeEach(() => {
      // Reset mocks to ensure clean state
      User.findOne.mockReset();
      UserToken.findOne.mockReset();
      UserToken.create.mockReset();
      UserToken.update.mockReset();
      AlternativeEmail.findOne.mockReset();
    });
    it('should handle login attempt with both email and phone provided', async () => {
      // Arrange - Both email and phone provided, email takes precedence
      mockReq.body = {
        email: '<EMAIL>',
        phone: '+**********',
        country_code: '+1',
        password: 'Password123!',
      };

      const mockUser = {
        user_id: 'user123',
        email: '<EMAIL>',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      UserToken.create.mockResolvedValue({});
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      await commonService.signin(mockReq, mockRes);

      // Assert - Should use email lookup, not phone
      expect(User.findOne).toHaveBeenCalledWith({
        where: {
          email: '<EMAIL>',
          is_deleted: '0',
        },
      });
    });

    it('should handle different login types with proper authentication', async () => {
      // Arrange
      mockReq.body.login_type = 'Google';
      const mockUser = {
        user_id: 'user123',
        login_type: 'Google',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      UserToken.create.mockResolvedValue({});
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      const result = await commonService.signin(mockReq, mockRes);

      // Assert - OAuth accounts still go through password verification in current implementation
      expect(Bcryptjs.compareSync).toHaveBeenCalled();
      expect(result.dataValues.token).toBe('mock-jwt-token');
    });
  });

  describe('🖼️ Profile Photo Handling', () => {
    beforeEach(() => {
      // Reset mocks to ensure clean state
      User.findOne.mockReset();
      UserToken.findOne.mockReset();
      UserToken.create.mockReset();
      AlternativeEmail.findOne.mockReset();
      gcpUtils.getPrivateUrl.mockReset();
    });
    it('should not call getPrivateUrl when user has no photo', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        photo: null, // No photo
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      UserToken.create.mockResolvedValue({});
      AlternativeEmail.findOne.mockResolvedValue(null);

      // Act
      await commonService.signin(mockReq, mockRes);

      // Assert
      expect(gcpUtils.getPrivateUrl).not.toHaveBeenCalled();
    });

    it('should handle photo URL generation errors', async () => {
      // Arrange
      const mockUser = {
        user_id: 'user123',
        is_deleted: '0',
        email_verified: '1',
        status: 'active',
        password: 'hashed-password',
        photo: 'profile.jpg',
        dataValues: { user_id: 'user123' },
      };

      User.findOne.mockResolvedValue(mockUser);
      UserToken.findOne.mockResolvedValue(null);
      AlternativeEmail.findOne.mockResolvedValue(null);
      gcpUtils.getPrivateUrl.mockRejectedValue(new Error('GCP URL generation failed'));

      // Act & Assert
      await expect(commonService.signin(mockReq, mockRes)).rejects.toThrow('GCP URL generation failed');
    });
  });
}); 