/**
 * User Entity Model - KyuleBag User Management
 *
 * This Sequelize model defines the core user entity for the KyuleBag platform, managing
 * user accounts, authentication, preferences, and storage quotas. It serves as the central
 * entity for user-related operations and relationships throughout the application.
 *
 * Business Logic Connection:
 * - Central entity for user authentication and session management
 * - Manages storage quotas and subscription-based storage allocation
 * - Controls AI/ML service preferences and usage tracking
 * - Handles multi-provider authentication (KyuleBag, Google, Facebook, Apple)
 * - Stores user preferences for content processing and notifications
 * - Tracks user verification status and account security settings
 *
 * Key Features:
 * - Multi-provider OAuth authentication support
 * - Storage quota management with subscription upgrades
 * - AI/ML service configuration and usage tracking
 * - Email and phone verification workflows
 * - User preference management for app behavior
 * - Soft delete functionality with is_deleted flag
 * - Device-specific settings for mobile app integration
 *
 * Relationships:
 * - One-to-Many with user_subscriptions (storage and AI plans)
 * - One-to-Many with user_items (content ownership)
 * - One-to-Many with user_tokens (session management)
 * - One-to-Many with activity_logs (user action tracking)
 *
 * Default Behaviors:
 * - 5MB free storage allocation for new users
 * - AI and OCR features enabled by default
 * - Push notifications enabled
 * - 75% AI confidence level threshold
 * - 500 free AI API calls per user
 * - Link preview generation enabled
 * - Account verification required for access
 *
 * Security Features:
 * - bcrypt password hashing (handled in service layer)
 * - Separate private note password field
 * - Device token management for push notifications
 * - Email verification workflow
 * - Soft delete for data retention compliance
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2021
 */

'use strict';
const { Model } = require('sequelize');

/**
 * Export User Model Definition Function
 *
 * This function is called by Sequelize to initialize the user model with
 * the database connection and DataTypes. It defines the model structure,
 * field validations, and default values.
 *
 * @param {Object} sequelize - Sequelize instance for database connection
 * @param {Object} DataTypes - Sequelize data types for field definitions
 * @returns {Object} Initialized tbl_users model class
 */
module.exports = (sequelize, DataTypes) => {
  /**
   * User Model Class Definition
   *
   * Extends Sequelize Model class to define user entity structure,
   * associations, and business logic methods.
   */
  class tbl_users extends Model {
    /**
     * Define Model Associations
     *
     * Establishes relationships between User model and other entities.
     * These associations enable complex queries and maintain referential integrity.
     * Called automatically by Sequelize after all models are loaded.
     *
     * @param {Object} models - Collection of all loaded Sequelize models
     */
    static associate(models) {
      // User Subscription Relationship
      // One user can have multiple subscription plans (storage and AI)
      tbl_users.hasMany(models.tbl_user_subscriptions, {
        targetKey: 'user_id', // Primary key in users table
        foreignKey: 'user_id', // Foreign key in subscriptions table
        as: 'user_subscriptions', // Alias for query operations
        // Enables: user.getUserSubscriptions(), user.user_subscriptions
      });

      // Additional associations would be defined here:
      // - hasMany with tbl_items (user content)
      // - hasMany with tbl_user_tokens (authentication sessions)
      // - hasMany with tbl_activity_logs (user actions)
      // - hasMany with tbl_user_ai_subscriptions (AI service plans)
    }
  }

  /**
   * User Model Field Definitions
   *
   * Defines all database fields, data types, constraints, and default values
   * for the user entity. Each field includes business logic documentation.
   */
  tbl_users.init(
    {
      /**
       * Primary Key Field
       * Auto-incrementing unique identifier for each user record
       */
      user_id: {
        allowNull: false, // Required field
        autoIncrement: true, // Automatically generates unique IDs
        primaryKey: true, // Primary key constraint
        type: DataTypes.INTEGER, // 32-bit integer type
        comment: 'Unique identifier for user records',
      },

      /**
       * Personal Information Fields
       * Store user's basic personal details for profile and communication
       */
      first_name: {
        type: DataTypes.STRING, // Variable length string (default 255 chars)
        comment: "User's first name for personalization and communication",
      },

      last_name: {
        type: DataTypes.STRING, // Variable length string
        comment: "User's last name for personalization and communication",
      },

      /**
       * Third-Party Authentication IDs
       * Store external provider IDs for OAuth authentication flows
       */
      facebook_id: {
        type: DataTypes.STRING, // Facebook user ID
        comment: 'Facebook OAuth provider ID for social login',
      },

      google_id: {
        type: DataTypes.STRING, // Google user ID
        comment: 'Google OAuth provider ID for social login',
      },

      apple_id: {
        type: DataTypes.STRING, // Apple user ID
        comment: 'Apple OAuth provider ID for social login',
      },

      /**
       * Authentication Credentials
       * Primary authentication fields for account access
       */
      email: {
        // Note: unique constraint is commented out - should be enabled for production
        // unique: true,
        type: DataTypes.STRING, // Email address
        comment:
          'Primary email address for account identification and communication',
      },

      password: {
        type: DataTypes.STRING, // bcrypt hashed password
        comment:
          'bcrypt hashed password for account security (handled in service layer)',
      },

      private_note_password: {
        type: DataTypes.STRING, // Separate password for private notes
        comment: 'Additional password for accessing private/sensitive notes',
      },

      /**
       * Phone Number Fields
       * International phone number support with country codes
       */
      phone: {
        // Note: unique constraint is commented out - should be enabled for production
        // unique: true,
        type: DataTypes.STRING, // Phone number
        comment: "User's phone number for SMS verification and communication",
      },

      region_code: {
        type: DataTypes.STRING, // Geographic region code
        comment: 'Geographic region code for phone number formatting',
      },

      country_code: {
        type: DataTypes.STRING, // International dialing code
        comment: 'International country dialing code (e.g., +1 for US)',
      },

      /**
       * User Profile Information
       * Additional demographic and profile data
       */
      gender: {
        type: DataTypes.ENUM('male', 'female', 'other'), // Predefined options
        comment: "User's gender for personalization and analytics",
      },

      photo: {
        type: DataTypes.STRING, // Profile photo filename/path
        comment: 'Profile photo filename stored in cloud storage',
      },

      /**
       * Authentication Configuration
       * Controls how user authenticates with the platform
       */
      login_type: {
        allowNull: false, // Required field
        type: DataTypes.ENUM('KyuleBag', 'Google', 'FaceBook'), // Supported providers
        defaultValue: 'KyuleBag', // Default to native authentication
        comment: 'Authentication provider used for account creation',
      },

      /**
       * Notification Preferences
       * User's communication and notification settings
       */
      push_notification: {
        type: DataTypes.INTEGER, // 1 = enabled, 0 = disabled
        defaultValue: 1, // Enabled by default
        comment: 'Push notification preference flag (1=enabled, 0=disabled)',
      },

      /**
       * Account Status Management
       * Controls account access and lifecycle
       */
      status: {
        type: DataTypes.ENUM('inactive', 'active'), // Account status options
        defaultValue: 'active', // Active by default
        comment: 'Account status controlling access permissions',
      },

      /**
       * Storage Quota Management
       * Controls user's storage allocation and usage tracking
       */
      total_storage: {
        type: DataTypes.FLOAT, // Storage in bytes (supports decimal values)
        defaultValue: 5242880, // 5MB default allocation (5 * 1024 * 1024 bytes)
        comment: 'Total storage quota in bytes (default 5MB for free users)',
      },

      used_storage: {
        type: DataTypes.FLOAT, // Current storage usage in bytes
        comment: 'Current storage usage in bytes across all user content',
      },

      extra_storage: {
        type: DataTypes.FLOAT, // Additional storage from subscriptions
        defaultValue: 0, // No extra storage by default
        comment:
          'Additional storage allocation from subscription plans in bytes',
      },

      /**
       * Account Verification Status
       * Email verification and account activation control
       */
      is_verified: {
        type: DataTypes.INTEGER, // 1 = verified, 0 = unverified
        defaultValue: 0, // Unverified by default
        comment: 'Email verification status flag (1=verified, 0=unverified)',
      },

      verification_code: {
        type: DataTypes.TEXT, // Verification code for email confirmation
        comment: 'Temporary verification code for email confirmation process',
      },

      /**
       * Application Feature Preferences
       * User's preferences for app behavior and features
       */
      is_web_link_preview_set: {
        type: DataTypes.BOOLEAN, // Enable/disable link previews
        defaultValue: true, // Enabled by default
        comment: 'Enable automatic web link preview generation',
      },

      AI_set: {
        type: DataTypes.BOOLEAN, // Enable/disable AI features
        defaultValue: true, // Enabled by default
        comment: 'Enable AI-powered content analysis and processing',
      },

      OCR_set: {
        type: DataTypes.BOOLEAN, // Enable/disable OCR processing
        defaultValue: true, // Enabled by default
        comment:
          'Enable Optical Character Recognition for image text extraction',
      },

      AI_confidence_level: {
        type: DataTypes.INTEGER, // AI confidence threshold percentage
        defaultValue: 75, // 75% confidence threshold
        comment:
          'Minimum confidence level for AI analysis results (percentage)',
      },

      biometric_authentication: {
        type: DataTypes.BOOLEAN, // Enable biometric login
        defaultValue: false, // Disabled by default
        comment:
          'Enable biometric authentication (fingerprint, face ID) on mobile',
      },

      hide_AI_label: {
        type: DataTypes.BOOLEAN, // Hide AI processing labels
        defaultValue: true, // Hidden by default
        comment: 'Hide AI processing labels in content display',
      },

      hide_OCR_label: {
        type: DataTypes.BOOLEAN, // Hide OCR processing labels
        defaultValue: true, // Hidden by default
        comment: 'Hide OCR processing labels in content display',
      },

      /**
       * AI Service Usage Tracking
       * Manages AI API call quotas and usage monitoring
       */
      total_ai_api: {
        type: DataTypes.INTEGER, // Total AI API calls available
        defaultValue: 500, // 500 free API calls
        comment:
          'Total AI API calls available to user (includes free + purchased)',
      },

      used_ai_api: {
        type: DataTypes.FLOAT, // AI API calls consumed
        defaultValue: 0, // No usage initially
        comment: 'Number of AI API calls consumed by user',
      },

      /**
       * Mobile Device Integration
       * Device-specific settings for mobile app functionality
       */
      device_type: {
        type: DataTypes.ENUM('ios', 'android'), // Supported mobile platforms
        comment: "User's primary mobile device platform",
      },

      device_token: {
        type: DataTypes.STRING, // Push notification device token
        comment: 'Device-specific token for push notification delivery',
      },

      version: {
        allowNull: false, // Required field
        type: DataTypes.STRING, // App version string
        comment: 'Mobile app version for compatibility and feature support',
      },

      /**
       * Data Management Flags
       * Control data lifecycle and compliance features
       */
      is_deleted: {
        allowNull: false, // Required field
        type: DataTypes.ENUM('0', '1'), // Soft delete flag
        defaultValue: '0', // Not deleted by default
        comment:
          'Soft delete flag for data retention compliance (0=active, 1=deleted)',
      },

      email_verified: {
        allowNull: false, // Required field
        type: DataTypes.ENUM('0', '1'), // Email verification status
        defaultValue: '0', // Unverified by default
        comment: 'Email address verification status (0=unverified, 1=verified)',
      },
    },
    {
      sequelize, // Sequelize instance
      createdAt: false, // Disable automatic createdAt timestamp
      updatedAt: false, // Disable automatic updatedAt timestamp
      modelName: 'tbl_users', // Model name for Sequelize operations
      tableName: 'tbl_users', // Explicit table name in database
      comment:
        'Core user entity managing accounts, authentication, preferences, and quotas',
    }
  );

  /**
   * Return initialized User model
   * Makes the model available for import and use throughout the application
   */
  return tbl_users;
};
