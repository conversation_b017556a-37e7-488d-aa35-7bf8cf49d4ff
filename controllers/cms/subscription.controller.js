const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const subscription_service = require("../../services/cms/subscription.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async addStoragePlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await subscription_service.addStoragePlan(
        req,
        res
      );

      if (data.type === 3) {
        res.status(status.SUCCESSSTATUS).send({
          data: data.data,
          message: message.PRODUCTIDUSED,
          status: status.SUCCESS,
        });
      } else if (data.type === 0) {
        res.status(status.SUCCESSSTATUS).send({
          data: data.data,
          message: message.PRODUCTIDEXIST,
          status: status.SUCCESS,
        });
      } else if (data.type === 1) {
        res.status(status.SUCCESSSTATUS).send({
          data: data.data,
          message: message.STORAGEPLANADDED,
          status: status.SUCCESS,
        });
      } else if (data.type === 2) {
        res.status(status.SUCCESSSTATUS).send({
          data: data.data,
          message: message.AISTORAGEPLANADDED,
          status: status.SUCCESS,
        });
      }
      // res.status(status.SUCCESSSTATUS).send({
      //   //response on email is not-found
      //   // data: [],
      //   data: add_subscription_res,
      //   message: message.STORAGEPLANADDED,
      //   status: status.SUCCESS,
      // });
    } catch (error) {
      console.log("addStoragePlan -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: error?.errors.length ? error?.errors[0].message : message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async listStoragePlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_subscription_res = await subscription_service.listStoragePlan(
        req,
        res
      );
      if (list_subscription_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_subscription_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.STORAGEPLANNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listStoragePlan -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async editStoragePlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await subscription_service.editStoragePlan(req, res);
      // if(edit_storagePlan_res === 0) {
      //   res.status(status.SUCCESSSTATUS).send({
      //     //response on email is not-found
      //     data: [],
      // message: message.STORAGEPLANNOTFOUND,
      //     status: status.SUCCESS,
      //   });
      // }
      // else {
      //   res.status(status.SUCCESSSTATUS).send({
      //     //response on email is not-found
      //     // data: [],
      //     data: edit_storagePlan_res,
      message: message.STORAGEPLANUPDATED,
        //     status: status.SUCCESS,
        //   });
        // }
        res.status(status.SUCCESSSTATUS).send({
          data: data.data,
          message: data.message,
          status: status.SUCCESS,
        });
    } catch (error) {
      console.log("editStoragePlan -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: error?.errors.length ? error?.errors[0].message : message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async deleteStoragePlan(req, res) {
    try {
      let delete_storagePlan = await subscription_service.deleteStoragePlan(req, res);
      if (delete_storagePlan) {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.STORAGEPLANDELETED,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        data: [],
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async retriveStoragePlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_subscriptionPlan_res = await subscription_service.retriveStoragePlan(req, res);
      if (!list_subscriptionPlan_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_subscriptionPlan_res,
          message: message.STORAGEPLANNOTFOUND,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: list_subscriptionPlan_res,
          message: message.STORAGEPLANLIST,
          status: status.SUCCESS,
        });
      }
    } catch (error) {
      console.log("retriveStoragePlan -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async changeStatusPlan(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let data = await subscription_service.changeStatusPlan(req, res);
      // if (!list_subscriptionPlan_res) {
      //   res.status(status.SUCCESSSTATUS).send({
      //     data: list_subscriptionPlan_res,
      //     message: message.STORAGEPLANNOTFOUND,
      //     status: status.SUCCESS,
      //   });
      // } else {
      //   res.status(status.SUCCESSSTATUS).send({
      //     data: list_subscriptionPlan_res,
      //     message: message.STORAGEPLANLIST,
      //     status: status.SUCCESS,
      //   });
      // }
      res.status(status.SUCCESSSTATUS).send({
        data: data.data,
        message: data.message,
        status: status.SUCCESS,
      });
    } catch (error) {
      console.log("retriveStoragePlan -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
