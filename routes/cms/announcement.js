var express = require("express");
var router = express.Router();

/* require for Authentication */
const authentication =
  require('../../middleware/admin_auth').adminAuthentication;
// const multer_middleware = require('../../middleware/multer');

/* require for Controller */
const announcement_controller = require("../../controllers/cms/announcement.controller");

/*require for validators */
const validators = require('../../assets/validators');

router.post("/add", authentication, validators.cms.announcement.addAnnouncement, announcement_controller.addAnnouncement);
router.delete("/delete", authentication, announcement_controller.deleteAnnouncement);
router.post("/list", authentication, announcement_controller.listAnnouncement);
router.post("/allList", authentication, announcement_controller.allListAnnouncement);
router.post("/resend/:id", authentication, announcement_controller.resendAnnouncement);

/* API to edit comment settings */
router.put("/comment/settings", authentication, announcement_controller.editCommentSettings)

module.exports = router;