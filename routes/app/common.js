/*
 * Summary:     common.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const user_auth = require("../../middleware/auth").userAuthentication;
const common_controller = require("../../controllers/app/common.controller");
const private_note_password_controller = require("../../controllers/app/private_note_password.controller");
const imageMiddleware = require("../../middleware/multer");
const validators = require("../../assets/validators");

router.post(
  "/signup",
  imageMiddleware.singleProfilePic,
  validators.app.users.signup,
  common_controller.signup
);
router.post("/signin", validators.app.users.signin, common_controller.signin);
router.post(
  "/verify-otp",
  user_auth,
  validators.app.users.verify_otp,
  common_controller.verify_otp
);
router.post(
  "/forgot-password",
  validators.app.users.forgot_password,
  common_controller.forgot_password
);

router.post(
  "/verify-code",
  validators.app.users.verify_code,
  common_controller.verify_code
);

router.post(
  "/checkUser",
  validators.app.users.check_user,
  common_controller.check_user
);

router.post(
  "/verify-user",
  user_auth,
  // validators.app.users.verify_code,
  common_controller.verify_user
);

router.post(
  "/reset-password",
  validators.app.users.reset_password,
  common_controller.reset_password
);
router.post(
  "/set-web-link-preview",
  user_auth,
  common_controller.set_web_link_preview
);
router.post(
  "/set-ai",
  user_auth,
  common_controller.set_AI
);
router.get(
  "/verify_email/:code",
  validators.app.users.verify_email,
  common_controller.verify_email
);
router.delete("/logout", user_auth, common_controller.logout);
router.get("/terms-of-use", common_controller.terms_of_use);
router.put(
  "/edit-profile",
  user_auth,
  imageMiddleware.singleProfilePic,
  validators.app.users.edit_profile,
  common_controller.edit_profile
);
router.put(
  "/change-password",
  user_auth,
  validators.app.users.change_password,
  common_controller.change_password
);
router.post(
  "/private_note_password",
  user_auth,
  validators.app.users.private_note_password,
  private_note_password_controller.private_note_add
);
router.put(
  "/change-private-note-password",
  user_auth,
  validators.app.users.change_private_note_password,
  private_note_password_controller.change_private_note_password
);
router.post(
  "/forgot-private_note_password",
  user_auth,
  validators.app.users.forgot_private_note_password,
  private_note_password_controller.forgot_private_note_password
);
router.post(
  "/reset-private_note_password",
  user_auth,
  validators.app.users.reset_private_note_password,
  private_note_password_controller.reset_private_note_password
);
router.get(
  "/used-storage",
  user_auth,
  // validators.app.users.storage,
  common_controller.storage
);

router.post(
  "/subscription",
  user_auth,
  validators.app.users.subscription,
  common_controller.subscription
)

router.post(
  "/update-subscription",
  user_auth,
  validators.app.users.update_subscription,
  common_controller.update_subscription
)

router.post(
  "/ai-subscription",
  user_auth,
  validators.app.users.ai_subscription,
  common_controller.ai_subscription
)

router.post(
  "/cancel-subscription",
  user_auth,
  validators.app.users.cancel_subscription,
  common_controller.cancel_subscription
)

router.post(
  "/alternative-email",
  user_auth,
  validators.app.users.alternative_email,
  common_controller.alternative_email
)

router.delete(
  "/delete-alternative",
  user_auth,
  validators.app.users.delete_alternative,
  common_controller.delete_alternative
);

// set OCR
router.post("/set-ocr", user_auth, common_controller.setOCR);

// set AI confidence level
router.post(
  "/set-ai-confidence-level",
  user_auth,
  common_controller.setAIConfidenceLevel
);

// user AI API count
router.post(
  "/user-ai-count",
  user_auth,
  common_controller.userAICount
);

// Subscription Storage and AI Products Plan ID API
router.post(
  "/active-subscription-product-id",
  user_auth,
  common_controller.activeSubscriptionProductId
);

// Storage Subscription Plan API
router.post(
  "/storage-subscription-plan",
  user_auth,
  common_controller.storageSubscriptionPlan
);

// New Storage Subscription Plan API
router.post(
  "/new-storage-subscription-plan",
  user_auth,
  common_controller.newStorageSubscriptionPlan
);

// AI Subscription API
router.post(
  "/subscription-plan",
  user_auth,
  common_controller.subscriptionPlan
);

// New AI Subscription API
router.post(
  "/new-subscription-plan",
  user_auth,
  common_controller.newSubscriptionPlan
);

//set push notification status
router.post(
  "/set-notification",
  user_auth,
  common_controller.setNotificationStatus
);

//set biometric authentication
router.post(
  "/set-biometric-authentication",
  user_auth,
  common_controller.setBiometricAuthentication
);

//manage AI label visibility
router.post("/hide-ai-label", user_auth, common_controller.hideAILabel);

//manage OCR label visibility
router.post("/hide-ocr-label", user_auth, common_controller.hideOCRLabel);

//user hard delete
router.delete("/delete", user_auth, common_controller.userDelete);

//user subscription payment history
router.get(
  "/payment-history",
  user_auth,
  common_controller.paymentHistory
);

module.exports = router;
