require("dotenv").config();

const ejs = require("ejs");
const nodemailer = require("nodemailer");
var moment = require('moment');
const constant = require("../config/constant");
const message = require("../config/web.message").appMessage;
const status = require("../config/status").status;
const gcpUtils = require("./gcpUtils");

var sendmail = async function (
  res,
  email,
  subject,
  mailbody,
  attachments = ""
) {
  try {    
    var transporter = nodemailer.createTransport({
      // service: constant.MAIL_SERVICE,
      host: constant.MAIL_HOST,
      port: constant.MAIL_PORT,
      secure: constant.MAIL_SECURE,
      secureConnection: constant.MAIL_SECURE_CONNECTION,
      requireTLS: constant.MAIL_REQUIRE_TLS,
      debug: constant.MAIL_DEBUG,
      auth: {
        user: constant.MAIL_FROM,
        pass: constant.MAIL_PASSWORD,
      },
    });

    let logoUrl = await gcpUtils.getPrivateUrl(
      constant.GCP_BUCKET_NAME,
      "kyulebag_logo.png", /** { action: 'read', expires: moment().add(7, 'day').format() } */
    );
    let html_data = await ejs.renderFile(__dirname + "/email.ejs", {
      TITLE: subject,
      HTML_BODY: mailbody,
      LOGOURL: logoUrl,
      APPNAME: process.env.APPNAME,
      APPCOLOR: process.env.APPCOLOR,
      YEAR: moment().format("YYYY")
    });

    let mailoption = {
      from: constant.MAIL_FROM,
      to: email,
      html: html_data,
      subject: subject,
    };

    if (attachments != "") {
      mailoption.attachments = attachments;
    }
    let mailresponse = await transporter.sendMail(mailoption);
    console.log("Mail send successfully");
    return mailresponse;
  } catch (error) {
    console.log(error);
    
    // res.status(status.INTERNALSERVERERRORSTATUS).send({
    //   error: error,
    //   message: message.INTERNALSERVERERROR,
    //   status: status.ERROR,
    // });
  }
};
exports.sendmail = sendmail;