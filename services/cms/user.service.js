const User = require("../../database/models").tbl_users;
const UserToken = require("../../database/models").tbl_user_tokens;
const Items = require("../../database/models").tbl_items;
const ItemNotes = require("../../database/models").tbl_item_notes;
const ItemTags = require("../../database/models").tbl_item_tags;
const ItemSharingDetails = require("../../database/models").tbl_sharing_details;
const Images = require("../../database/models").tbl_media;
const OCRLabels = require("../../database/models").tbl_item_ocr_labels;
const Activity_logs = require("../../database/models").tbl_activity_logs;
const AlternativeEmail = require("../../database/models").tbl_alternative_emails;
const AnnouncementComment = require("../../database/models").tbl_announcements;
const TagNames = require("../../database/models").tbl_tag_names;
const UserSubscription = require("../../database/models").tbl_user_subscriptions;
const UserAISubscription = require("../../database/models").tbl_user_ai_subscriptions;
const gcpUtils = require("../../helper/gcpUtils");

const Sequelize = require("sequelize");
const Op = Sequelize.Op;
const constant = require("../../config/constant");
const generalHelper = require("../../helper/general.helper");

module.exports = {
  async listUser(req, res) {
    let { sort_by, search_name, search_last_name, search_email, order, is_verified } =req.body;
    // is_verified = is_verified === "null" ? "" : is_verified;
    search_name = search_name === undefined ? "" : search_name;
    search_last_name = search_name === undefined ? "" : search_last_name;
    search_email = search_name === undefined ? "" : search_email;
    let searchObj = {
      where: {
        is_deleted: "0",

        [Op.or]: [
          {
            first_name: { [Op.like]: `%${search_name}%` },
          },
          {
            last_name: { [Op.like]: `%${search_name}%` },
          },
          {
            email: { [Op.like]: `%${search_name}%` },
          },
        ],
      },
    };

    if (is_verified === "0") {
      searchObj.where[Op.and] = [
        {
          [Op.or]: [
            { is_verified: "0" },
            { email_verified: "0" }
          ]
        }
      ];
    } else if (is_verified) {
      searchObj.where.is_verified = is_verified;
      searchObj.where.email_verified = is_verified;
    }

    /*pagination*/
    const offset = (req.body.page - 1) * constant.LIMIT;
    const limit = constant.LIMIT;

    /* user find query */
    let user_list = await User.findAndCountAll({
      where: searchObj.where,
      attributes: {
        exclude: ["password", "verification_code", "device_token"],
        include: ["createdAt", "gender"],
      },
      order: [[Sequelize.literal(`${sort_by}`), `${order}`]],
      limit: limit,
      offset: offset,
    });

    user_list.rows = await generalHelper.userProfileMediaURL(user_list.rows, true)

    return user_list;
  },

  // delete user list
  async getDelUser(req, res) {
    let find_user = await User.findOne({
      where: { is_deleted: '1' },
    });

    return find_user;
  },


  /* View Provider by ID*/
  async getUserById(req, res) {
    let find_user = await User.findOne({
      where: { user_id: req.params.id },
      attributes: {
        exclude: [
          "is_active",
          "is_email_verifed",
          "is_deleted",
          "createdAt",
          "updatedAt",
        ],
      },
    });

    return find_user;
  },

  /** Delete provider or customer status */
  async deleteUser(req, res) {
    // let update_user_del_flag = await User.findAndCountAll(
    // let update_user_del_flag = await User.update(
    //   {
    //     is_deleted: "1",
    //   },
    //   {
    //     where: {
    //       user_id: req.params.id,
    //     },
    //   }
    // );

    // await UserToken.destroy({
    //   where: {
    //     user_id: req.params.id,
    //   },
    // });


    // return update_user_del_flag;

    // User hard delete
    let userId = req.params.id;
    let promise = [];

    let userItems = await Items.findAll({
      where: {
        user_id: userId,
      }
    })

    if (userItems.length) {
      for (let index = 0; index < userItems.length; index++) {
        const itemId = userItems[index].item_id;
        const allPromise = []

        // delete user comment from tbl_item_nots
        allPromise.push(ItemNotes.destroy({
          where: {
            item_id: itemId,
          }
        }));

        // delete user comment from tbl_item_ocr_labels
        allPromise.push(OCRLabels.destroy({
          where: {
            item_id: itemId,
          }
        }));

        // delete user comment from tbl_item_tags
        allPromise.push(ItemTags.destroy({
          where: {
            item_id: itemId,
          }
        }));

        // delete user from tbl_media
        allPromise.push(Images.destroy({
          where: {
            item_id: itemId,
          }
        }));

        // delete user from tbl_sharing_details
        allPromise.push(ItemSharingDetails.destroy({
          where: {
            item_id: itemId,
          }
        }));

        await Promise.all(allPromise);
      }
    }

    // delete user tbl_activity_logs
    promise.push(Activity_logs.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_alternative_emails
    promise.push(AlternativeEmail.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user comment from tbl_announcements
    promise.push(AnnouncementComment.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_tag_names
    promise.push(TagNames.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_user_subscriptions
    promise.push(UserSubscription.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_user_ai_subscriptions
    promise.push(UserAISubscription.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_items
    promise.push(Items.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_user_tokens
    promise.push(UserToken.destroy({
      where: {
        user_id: userId,
      }
    }));

    // delete user from tbl_users
    promise.push(User.destroy({
      where: {
        user_id: userId,
      }
    }));

    promise.push(gcpUtils.deleteGCPFolder(
      constant.GCP_BUCKET_FOLDER +
      constant.GCP_USER_FOLDER +
      userId +
      "/" +
      constant.GCP_ITEM_FOLDER
    ));

    await Promise.all(promise);

    return;
  },

  /*Update Provider and customer*/

  async updateUser(req, res) {
    let {
      first_name,
      last_name,
      email,
      password,
      photo,
      phone,
      address,
      gender,
      status,
      device_type,
    } = req.body;
    let user_obj = {
      first_name: first_name,
      email: email,
      phone: phone,
      gender: gender,
      status: status,
      device_type: device_type,
    };

    if (status === "inactive") {
      await UserToken.destroy({
        where: {
          user_id: req.params.id,
        },
      });
    }

    let update_user = await User.update(user_obj, {
      where: { user_id: req.params.id },
    });
    return update_user;
  },
};
