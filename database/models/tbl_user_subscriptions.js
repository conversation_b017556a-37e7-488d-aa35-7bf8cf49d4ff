'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class tbl_user_subscriptions extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      tbl_user_subscriptions.belongsTo(models.tbl_users, {
        targetKey: "user_id",
        foreignKey: "user_id",
        // as: "user_details",
      });
      // tbl_user_subscriptions.belongsTo(models.tbl_subscriptions, {
      //   targetKey: "subscription_id",
      //   foreignKey: "subscription_id",
      //   // as: "subscription_plan_details",
      // });
      tbl_user_subscriptions.belongsTo(models.tbl_new_subscriptions, {
        targetKey: "subscription_id",
        foreignKey: "subscription_id",
        // as: "subscription_plan_details",
      });
    }
  }
  tbl_user_subscriptions.init({
    user_subscription_id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    user_id: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    subscription_id: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    transaction_id: {
      allowNull: false,
      type: DataTypes.STRING
    },
    purchase_token: {
      allowNull: false,
      type: DataTypes.TEXT
    },
    payment_amount: {
      allowNull: false,
      type: DataTypes.STRING
    },
    start_date: {
      allowNull: false,
      type: DataTypes.DATE
    },
    end_date: {
      allowNull: false,
      type: DataTypes.DATE
    },
    is_active: {
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '1',
    },
    is_expired: {
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
    is_canceled: {
      type: DataTypes.ENUM('0', '1'),
      defaultValue: '0',
    },
    canceled_date: {
      type: DataTypes.DATE
    },
    status: {
      type: DataTypes.ENUM('completed', 'pending'),
    }
  }, {
    sequelize,
    createdAt: false, 
    updatedAt: false,
    modelName: 'tbl_user_subscriptions',
  });
  return tbl_user_subscriptions;
};