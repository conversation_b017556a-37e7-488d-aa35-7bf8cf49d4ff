const message = require("../../config/cms.message").cmsMessage;
const status = require("../../config/status").status;
const transaction_service = require("../../services/cms/paymentTransaction.service");
const { validationResult } = require("express-validator");
const { displayOnlyFirstError } = require("../../assets/common");

module.exports = {
  async listPaymentTransaction(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_transaction_res = await transaction_service.listPaymentTransaction(
        req,
        res
      );
      if (list_transaction_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_transaction_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.TRANSACTIONNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listPaymentTransaction -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async allListPaymentTransaction(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_transaction_res = await transaction_service.allListPaymentTransaction(
        req,
        res
      );
      if (list_transaction_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_transaction_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.TRANSACTIONNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listPaymentTransaction -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async aiListPaymentTransaction(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_transaction_res = await transaction_service.aiListPaymentTransaction(
        req,
        res
      );
      if (list_transaction_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_transaction_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.TRANSACTIONNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listPaymentTransaction -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },

  async allAiListPaymentTransaction(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      let message = "";
      displayOnlyFirstError(errors.array()).map((el, i, arr) =>
        arr.length - 1 === i ? (message += el.msg) : (message += el.msg + "\n")
      );
      return res
        .status(status.SUCCESSSTATUS)
        .send({ data: {}, message: message, status: 0 });
    }
    try {
      let list_transaction_res = await transaction_service.allAiListPaymentTransaction(
        req,
        res
      );
      if (list_transaction_res) {
        res.status(status.SUCCESSSTATUS).send({
          data: list_transaction_res,
          message: message.SUCCESS,
          status: status.SUCCESS,
        });
      } else {
        res.status(status.SUCCESSSTATUS).send({
          data: [],
          message: message.TRANSACTIONNOTFOUND,
          status: status.ERROR,
        });
      }
    } catch (error) {
      console.log("listPaymentTransaction -> error", error);
      res.status(status.INTERNALSERVERERRORSTATUS).send({
        error: error,
        message: message.INTERNALSERVERERROR,
        status: status.ERROR,
      });
    }
  },
};
