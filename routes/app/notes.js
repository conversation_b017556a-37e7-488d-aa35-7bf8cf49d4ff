/*
 * Summary:     tags.js file for handling all routes, request and response for app.
 * Author:      Openxcell(empCode-N00039)
 */

/**require NPM-modules for configuration */
const express = require("express");
const router = express.Router();
const user_auth = require("../../middleware/auth").userAuthentication;
const validators = require("../../assets/validators");
const note_controller = require("../../controllers/app/notes.controller");

router.post("/add", user_auth, validators.app.notes.add, note_controller.add);
router.delete(
  "/delete",
  user_auth,
  validators.app.notes.delete,
  note_controller.delete
);
router.put("/edit", user_auth, validators.app.notes.edit, note_controller.edit);

module.exports = router;
